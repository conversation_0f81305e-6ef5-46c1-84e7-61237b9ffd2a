
---

### **系统核心业务与财务集成全景图**

**图例说明:**
*   **类型/枚举 (`Type/Enum`)**: 定义了业务中固定的分类和选项。
*   **状态机 (`State Machine`)**: 使用Mermaid图展示了单个业务实体（单据）从创建到终结的完整生命周期和状态转换。
*   **集成流程图 (`Integrated Process Flow`)**: 使用Mermaid图展示了跨模块、跨单据的完整业务数据流和触发关系。

---
### **第一部分：采购与应付 (Purchase to Pay)**

本部分描绘了从下达采购订单，到货物入库，再到最终与供应商完成发票匹配和付款核销，并记录资金流出的全过程。

#### **1.1 涉及的核心实体**

*   `erp_purchase_order` (采购订单)
*   `erp_purchase_inbound` (采购入库单)
*   `wms_inbound` (WMS入库执行单)
*   `erp_fin_ap_payment_order` (付款单)
*   `erp_fin_ap_invoice` (应付单)
*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **1.2 核心实体状态机 (State Machines)**

##### **1.2.1 采购订单 (`erp_purchase_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> PENDING_APPROVAL: "提交审批"
    PENDING_APPROVAL --> DRAFT: "驳回修改"
    PENDING_APPROVAL --> CONFIRMED: "审批通过"
    CONFIRMED --> PARTIALLY_RECEIVED: "部分到货"
    CONFIRMED --> FULLY_RECEIVED: "全部到货"
    PARTIALLY_RECEIVED --> FULLY_RECEIVED: "剩余到货"
    PARTIALLY_RECEIVED --> CLOSED: "强制关闭"
    FULLY_RECEIVED --> CLOSED: "财务关闭"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    PENDING_APPROVAL --> fork_cancel
    CONFIRMED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"

    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **1.2.2 采购入库单 (`erp_purchase_inbound`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建入库单"
    DRAFT --> PENDING_WAREHOUSE: "提交"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    COMPLETED --> [*]
```

##### **1.2.3 应付单 (`erp_fin_ap_invoice`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNPAID: "确认"
    UNPAID --> PARTIALLY_PAID: "部分付款"
    UNPAID --> FULLY_PAID: "全额付款"
    PARTIALLY_PAID --> FULLY_PAID: "付清余款"
    FULLY_PAID --> [*]
```

##### **1.2.4 付款单 (`erp_fin_ap_payment_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNAPPLIED: "确认/待核销"
    UNAPPLIED --> PARTIALLY_APPLIED: "部分核销"
    UNAPPLIED --> FULLY_APPLIED: "完全核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "剩余核销"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    UNAPPLIED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    FULLY_APPLIED --> [*]
    CANCELLED --> [*]
```

#### **1.3 采购到应付集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "第一阶段: 采购与收货"
        A["<b>erp_purchase_order</b><br>DRAFT"] -- "审核" --> B["<b>erp_purchase_order</b><br>CONFIRMED"];
        B -- "供应商到货" --> C{"创建 <b>erp_purchase_inbound</b>"};
        C --> D["<b>erp_purchase_inbound</b><br>DRAFT"];
        D -- "提交" --> G["<b>erp_purchase_inbound</b><br>PENDING_WAREHOUSE"];
    end

    subgraph "第二阶段: 仓库执行与状态回传"
        G -- "推送入库指令" --> H["创建 <b>wms_inbound</b><br>PENDING_RECEIPT"];
        H -- "仓库收货上架" --> I["<b>wms_inbound</b><br>COMPLETED"];
        I -- "回传入库结果" --> J{"更新 <b>erp_purchase_inbound</b>"};
        J --> K["<b>erp_purchase_inbound</b><br>COMPLETED"];
        K -- "更新采购订单收货数量" --> L["<b>erp_purchase_order</b><br>PARTIALLY/FULLY_RECEIVED"];
    end

    subgraph "第三阶段: 财务应付处理"
        M["供应商提供发票"] --> N{"财务录入 <b>erp_fin_ap_invoice</b>"};
        N --> O["<b>erp_fin_ap_invoice</b><br>DRAFT"];
        O -- "确认" --> P["<b>erp_fin_ap_invoice</b><br>UNPAID"];
    end
    
    subgraph "第四阶段: 付款与核销"
        P -- "纳入付款计划" --> R{"创建 <b>erp_fin_ap_payment_order</b>"};
        R --> S["<b>erp_fin_ap_payment_order</b><br>DRAFT"];
        S -- "确认" --> T["<b>erp_fin_ap_payment_order</b><br>UNAPPLIED"];
        T -- "出纳付款并核销" --> U{"将付款单核销到应付单"};
        U -- "更新应付单状态" --> V["<b>erp_fin_ap_invoice</b><br>PARTIALLY_PAID/FULLY_PAID"];
        U -- "更新付款单状态" --> W["<b>erp_fin_ap_payment_order</b><br>PARTIALLY_APPLIED/FULLY_APPLIED"];
    end
    
    subgraph "第五阶段: 资金账户变动 (最终闭环)"
        X["<b>erp_fin_account</b><br>(公司银行账户)"]
        Y["<b>erp_fin_account_ledger</b><br>(账户流水)"]
        W -- "触发" --> Z{"创建<b>账户流水</b>记录<br>direction: EXPENSE"};
        Z --> Y;
        Z -- "更新" --> X["<b>erp_fin_account</b><br>(余额减少)"];
    end

```

---
### **第二部分：销售与应收 (Order to Cash)**

本部分描绘了从接收客户订单，到发货出库，再到最终向客户开具应收单、接收回款并记录资金流入的全过程。

#### **2.1 涉及的核心实体**

*   `erp_sale_order` (销售订单)
*   `erp_sale_outbound` (销售出库单)
*   `wms_outbound` (WMS出库执行单)
*   `erp_fin_ar_receipt_order` (收款单)
*   `erp_fin_ar_receivable` (应收单)
*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **2.2 核心实体状态机 (State Machines)**

##### **2.2.1 销售订单 (`erp_sale_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> CONFIRMED: "审核确认"
    
    state fork_status <<fork>>
    CONFIRMED --> fork_status
    fork_status --> ON_HOLD: "挂起"
    ON_HOLD --> CONFIRMED: "解除挂起"
    fork_status --> READY_TO_SHIP: "库存满足(待发货)"
    
    READY_TO_SHIP --> PARTIALLY_SHIPPED: "部分发货"
    PARTIALLY_SHIPPED --> FULLY_SHIPPED: "剩余发货"
    READY_TO_SHIP --> FULLY_SHIPPED: "全部发货"
    FULLY_SHIPPED --> CLOSED: "财务关闭"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    ON_HOLD --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **2.2.2 应收单 (`erp_fin_ar_receivable`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNPAID: "确认/待收款"
    UNPAID --> PARTIALLY_PAID: "收到部分款"
    UNPAID --> FULLY_PAID: "收到全款"
    PARTIALLY_PAID --> FULLY_PAID: "收清余款"
    FULLY_PAID --> [*]
```

##### **2.2.3 收款单 (`erp_fin_ar_receipt_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    
    state fork_type <<fork>>
    DRAFT --> fork_type
    fork_type --> UNAPPLIED: "普通收款"
    fork_type --> ADVANCE_PAYMENT: "预收款"

    UNAPPLIED --> PARTIALLY_APPLIED: "部分核销"
    UNAPPLIED --> FULLY_APPLIED: "完全核销"
    ADVANCE_PAYMENT --> PARTIALLY_APPLIED: "部分核销"
    ADVANCE_PAYMENT --> FULLY_APPLIED: "完全核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "剩余核销"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    UNAPPLIED --> fork_cancel
    ADVANCE_PAYMENT --> fork_cancel
    fork_cancel --> CANCELLED: "取消"

    FULLY_APPLIED --> [*]
    CANCELLED --> [*]
```

#### **2.3 销售到应收集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "第一阶段: 销售与发货"
        A["<b>erp_sale_order</b><br>DRAFT"] -- "审核" --> B["<b>erp_sale_order</b><br>CONFIRMED"];
        B -- "备货完成/通知发货" --> C{"创建 <b>erp_sale_outbound</b>"};
        C --> D["<b>erp_sale_outbound</b><br>PENDING_WAREHOUSE"];
    end

    subgraph "第二阶段: 仓库执行与状态回传"
        D -- "推送出库指令" --> E["创建 <b>wms_outbound</b><br>PENDING_PICKING"];
        E -- "仓库拣货/复核/发运" --> F["<b>wms_outbound</b><br>COMPLETED"];
        F -- "回传出库结果" --> G{"更新 <b>erp_sale_outbound</b>"};
        G --> H["<b>erp_sale_outbound</b><br>COMPLETED"];
        H -- "更新销售订单发货数量" --> I["<b>erp_sale_order</b><br>PARTIALLY/FULLY_SHIPPED"];
    end

    subgraph "第三阶段: 财务应收确认"
        H -- "发货完成，触发" --> J{"创建 <b>erp_fin_ar_receivable</b>"};
        J --> K["<b>erp_fin_ar_receivable</b><br>DRAFT"];
        K -- "确认" --> L["<b>erp_fin_ar_receivable</b><br>UNPAID"];
    end

    subgraph "第四阶段: 收款与核销"
        M["客户付款"] --> N{"财务录入 <b>erp_fin_ar_receipt_order</b>"};
        N --> O["<b>erp_fin_ar_receipt_order</b><br>DRAFT"];
        O -- "确认" --> P["<b>erp_fin_ar_receipt_order</b><br>UNAPPLIED"];
        P -- "财务在工作台进行核销" --> Q{"将收款核销到应收单"};
        Q -- "更新应收单状态" --> R["<b>erp_fin_ar_receivable</b><br>PARTIALLY_PAID / FULLY_PAID"];
        Q -- "更新收款单状态" --> S["<b>erp_fin_ar_receipt_order</b><br>PARTIALLY_APPLIED / FULLY_APPLIED"];
    end

    subgraph "第五阶段: 资金账户变动 (最终闭环)"
        T["<b>erp_fin_account</b><br>(公司银行账户)"]
        U["<b>erp_fin_account_ledger</b><br>(账户流水)"]
        S -- "触发" --> V{"创建<b>账户流水</b>记录<br>direction: INCOME"};
        V --> U;
        V -- "更新" --> T["<b>erp_fin_account</b><br>(余额增加)"];
    end

```

---
### **第三部分：账户收支与资金管理 (Cash Management)**

本部分是所有财务流程的终点，详细说明了所有业务付款和收款如何最终体现为公司资金账户的实际变动，并形成可追溯的会计流水。

#### **4.1 涉及的核心实体**

*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **4.2 核心类型与枚举 (Enums)**

*   **`erp_fin_account.account_type` (账户类型)**
    *   `BANK_CASH`: 银行存款
    *   `CASH`: 库存现金
    *   `THIRD_PARTY`: 第三方支付 (如支付宝)
    *   `VIRTUAL`: 虚拟账户
*   **`erp_fin_account_ledger.direction` (资金方向)**
    *   `INCOME`: 收入
    *   `EXPENSE`: 支出
*   **`erp_fin_account_ledger.transaction_type` (交易类型)**
    *   `SALES_RECEIPT`: 销售收款
    *   `PURCHASE_PAYMENT`: 采购付款
    *   `EXPENSE_REIMBURSEMENT`: 费用报销
    *   `OTHER`: 其他 (如银行手续费、利息等)

#### **4.3 核心实体状态机 (`erp_fin_account`)**

```mermaid
stateDiagram-v2
    [*] --> ACTIVE: "开户"
    ACTIVE --> FROZEN: "司法/风控冻结"
    FROZEN --> ACTIVE: "解冻"
    ACTIVE --> CLOSED: "销户"
    CLOSED --> [*]
```

#### **4.4 账户收支集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "来源1: 销售收款 (Order to Cash)"
        A["<b>erp_fin_ar_receipt_order</b><br>UNAPPLIED"] -- "财务核销" --> B["<b>erp_fin_ar_receipt_order</b><br>FULLY_APPLIED"];
    end
    
    subgraph "来源2: 采购/费用付款 (Purchase/Expense to Pay)"
        C["<b>erp_fin_ap_payment_order</b><br>APPROVED"] -- "财务付款" --> D["<b>erp_fin_ap_payment_order</b><br>FULLY_APPLIED"];
    end
    
    subgraph "来源3: 其他收支"
        E[/"<b>财务人员</b><br>手工录入"/] --> F{"其他收支<br>(如: 银行手续费、利息)"};
    end
    
    subgraph "核心处理: 账户与流水"
        direction LR
        G{"<b>创建账户流水</b><br><i>(erp_fin_account_ledger)</i>"}
        H["<b>erp_fin_account</b><br>(更新余额)"]
        I["<b>erp_fin_account_ledger</b><br>(新增流水记录)"]
        
        G -- "1. 更新账户余额" --> H
        G -- "2. 记录详细流水" --> I
    end

    B -- "direction: INCOME" --> G
    D -- "direction: EXPENSE" --> G
    F -- "direction: INCOME/EXPENSE" --> G
    
 
```
