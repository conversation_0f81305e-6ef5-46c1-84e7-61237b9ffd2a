# 系统状态机定义 🧠

本文件记录了系统中核心流程的状态机定义，使用PlantUML语法进行描述，便于开发人员理解和实现。

## 核心状态机定义 🎯

## 订单状态机
```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建订单
    DRAFT --> CONFIRMED: 审核确认
    CONFIRMED --> PENDING_RECEIVE: 等待到货
    PENDING_RECEIVE --> COMPLETED: 入库完成

```

## 库存状态机
```mermaid
stateDiagram-v2
    [*] --> ACTIVE: 正常库存
    [*] --> FROZEN: 冻结库存

```

## 枚举状态映射
```mermaid
stateDiagram-v2
    [*] --> ACTIVE: 正常库存
    [*] --> FROZEN: 冻结库存
```

## 采购订单状态机 🛒
```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建订单
    DRAFT --> CONFIRMED: 审核确认
    CONFIRMED --> PARTIALLY_RECEIVED: 部分到货
    CONFIRMED --> FULLY_RECEIVED: 全部到货
    PARTIALLY_RECEIVED --> FULLY_RECEIVED: 剩余到货
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    fork_cancel --> CANCELLED: 取消订单
    
    FULLY_RECEIVED --> [*]
    CANCELLED --> [*]

```

## 生产工单状态机 🏭
```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建工单
    DRAFT --> RELEASED: 审核下达
    RELEASED --> IN_PROGRESS: 开始执行
    IN_PROGRESS --> PARTIALLY_COMPLETED: 部分完工
    PARTIALLY_COMPLETED --> COMPLETED: 剩余完工
    IN_PROGRESS --> COMPLETED: 全部完工
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    RELEASED --> fork_cancel
    IN_PROGRESS --> fork_cancel
    fork_cancel --> CANCELLED: 取消工单
    
    COMPLETED --> [*]
    CANCELLED --> [*]
```

## 出库单状态机 🚚

```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建出库单
    DRAFT --> PENDING_PICKING: 提交拣货
    PENDING_PICKING --> PICKING_IN_PROGRESS: 开始拣货
    PICKING_IN_PROGRESS --> PICKED: 拣货完成
    PICKED --> PACKED: 打包复核
    PACKED --> SHIPPED: 发运完成
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    PENDING_PICKING --> fork_cancel
    PICKING_IN_PROGRESS --> fork_cancel
    fork_cancel --> CANCELLED: 取消出库
    
    SHIPPED --> [*]
    CANCELLED --> [*]
```

---

### **系统核心流程与状态机图全集 (最终修订版)**

**图例说明：**
*   `[*]`: 代表流程的开始或结束。
*   `State1 --> State2: "Action"`: 表示从 `State1` 状态经过 `Action`（动作/事件）后，流转到 `State2` 状态。
*   `fork`: 用于表示一个状态可以分叉到多个后续状态。

---
#### **一、 基础数据 (Master Data)**

##### **1.1 BOM物料清单 (`pro_bom`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> ACTIVE: "审核激活"
    ACTIVE --> ARCHIVED: "废弃/归档"
    ACTIVE --> DRAFT: "反激活(修订)"
    ARCHIVED --> ACTIVE: "重新激活"
    DRAFT --> [*]: "删除"
```

##### **1.2 工艺路线 (`pro_routing`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> ACTIVE: "审核激活"
    ACTIVE --> ARCHIVED: "废弃/归档"
    ACTIVE --> DRAFT: "反激活(修订)"
    ARCHIVED --> ACTIVE: "重新激活"
    DRAFT --> [*]: "删除"
```

---
#### **二、 采购流程 (Purchase Flow)**

##### **2.1 采购订单 (`erp_purchase_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> CONFIRMED: "审核确认"
    CONFIRMED --> PARTIALLY_RECEIVED: "部分到货"
    CONFIRMED --> FULLY_RECEIVED: "全部到货"
    PARTIALLY_RECEIVED --> FULLY_RECEIVED: "剩余到货"
    PARTIALLY_RECEIVED --> CLOSED: "强制关闭"
    FULLY_RECEIVED --> CLOSED: "完成"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **2.2 采购入库单 (`erp_purchase_inbound`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建入库单"
    DRAFT --> PENDING_WAREHOUSE: "提交/通知仓库"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    COMPLETED --> [*]
```

##### **2.3 采购退货单 (`erp_purchase_return`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建退货单"
    DRAFT --> PENDING_WAREHOUSE: "提交/通知仓库"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    COMPLETED --> [*]
```

---
#### **三、 销售流程 (Sale Flow)**

##### **3.1 销售订单 (`erp_sale_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> CONFIRMED: "审核确认"
    
    state fork_status <<fork>>
    CONFIRMED --> fork_status
    fork_status --> ON_HOLD: "挂起"
    ON_HOLD --> CONFIRMED: "解除挂起"
    fork_status --> PENDING_PRODUCTION: "转生产"
    PENDING_PRODUCTION --> IN_PRODUCTION: "生产开始"
    IN_PRODUCTION --> READY_TO_SHIP: "生产完成"
    fork_status --> READY_TO_SHIP: "库存满足(待发货)"
    
    READY_TO_SHIP --> PARTIALLY_SHIPPED: "部分发货"
    PARTIALLY_SHIPPED --> FULLY_SHIPPED: "剩余发货"
    READY_TO_SHIP --> FULLY_SHIPPED: "全部发货"
    FULLY_SHIPPED --> COMPLETED: "订单完成"
    COMPLETED --> CLOSED: "财务关闭"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    ON_HOLD --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **3.2 销售出库单 (`erp_sale_outbound`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建出库单"
    DRAFT --> PENDING_WAREHOUSE: "提交/通知仓库"
    DRAFT --> CANCELLED: "取消出库"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    PENDING_WAREHOUSE --> CANCELLED: "取消出库"
    COMPLETED --> [*]
    CANCELLED --> [*]
```

##### **3.3 销售退货单 (`erp_sale_return`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建退货单"
    DRAFT --> AWAITING_RETURN: "确认退货/等待客户退货"
    AWAITING_RETURN --> PENDING_WAREHOUSE: "收到退货"
    PENDING_WAREHOUSE --> COMPLETED: "仓库入库完毕"
    COMPLETED --> [*]

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    AWAITING_RETURN --> fork_cancel
    fork_cancel --> CANCELLED: "取消退货"
    CANCELLED --> [*]
```

---
#### **四、 生产流程 (MES Flow)**

##### **4.1 工作总单/项目单 (`mes_instance_manager`)**
```mermaid
stateDiagram-v2
    [*] --> PLANNING: "创建"
    PLANNING --> IN_PROGRESS: "开始执行"
    IN_PROGRESS --> ON_HOLD: "暂停"
    ON_HOLD --> IN_PROGRESS: "恢复"
    IN_PROGRESS --> COMPLETED: "全部完成"
    
    state fork_cancel <<fork>>
    PLANNING --> fork_cancel
    IN_PROGRESS --> fork_cancel
    ON_HOLD --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    COMPLETED --> [*]
    CANCELLED --> [*]
```

##### **4.2 生产订单 (`mes_production_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建工单"
    DRAFT --> RELEASED: "审核下达"
    RELEASED --> IN_PROGRESS: "领料/开工"
    IN_PROGRESS --> PARTIALLY_COMPLETED: "部分完工入库"
    PARTIALLY_COMPLETED --> COMPLETED: "剩余完工入库"
    IN_PROGRESS --> COMPLETED: "全部完工入库"
    COMPLETED --> CLOSED: "成本核算/归档"
    CLOSED --> [*]
```

##### **4.3 生产通用单据 (`mes_production_issue`/`_inbound`/`_return`)**
*(此图适用于生产领料单、生产入库单、生产退料单)*
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建单据"
    DRAFT --> PENDING_WAREHOUSE: "提交/通知仓库"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    COMPLETED --> [*]
```

---
#### **五、 仓库与库存 (WMS & Inventory)**

##### **5.1 仓库入库执行单 (`wms_inbound`)**
```mermaid
stateDiagram-v2
    [*] --> PENDING_RECEIPT: "上游触发(待收货)"
    PENDING_RECEIPT --> PARTIALLY_RECEIVED: "收到部分货物"
    PARTIALLY_RECEIVED --> COMPLETED: "收到剩余货物"
    PENDING_RECEIPT --> COMPLETED: "收到全部货物"
    
    state fork_cancel <<fork>>
    PENDING_RECEIPT --> fork_cancel
    PARTIALLY_RECEIVED --> fork_cancel
    fork_cancel --> CANCELLED: "上游取消"
    
    COMPLETED --> [*]
    CANCELLED --> [*]
```

##### **5.2 仓库出库执行单 (`wms_outbound`)**
```mermaid
stateDiagram-v2
    [*] --> PENDING_PICKING: "上游触发(待拣货)"
    PENDING_PICKING --> PICKING_IN_PROGRESS: "开始拣货"
    PICKING_IN_PROGRESS --> PICKED: "拣货完成"
    PICKED --> PACKED: "打包/复核"
    PACKED --> SHIPPED: "发运"
    SHIPPED --> [*]: "完成"
    
    state fork_cancel <<fork>>
    PENDING_PICKING --> fork_cancel
    PICKING_IN_PROGRESS --> fork_cancel
    fork_cancel --> CANCELLED: "上游取消"
    
    CANCELLED --> [*]
```

##### **5.3 仓库移库执行单 (`wms_transfer`)**
```mermaid
stateDiagram-v2
    [*] --> PENDING: "系统/人工触发(待执行)"
    PENDING --> IN_PROGRESS: "开始移库(下架)"
    IN_PROGRESS --> COMPLETED: "移库完成(上架)"
    COMPLETED --> [*]
```

##### **5.4 库存盘点任务 (`wms_inventory_check`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> CONFIRMED: "确认(生成快照)"
    CONFIRMED --> IN_PROGRESS: "开始盘点"
    IN_PROGRESS --> PENDING_APPROVAL: "盘点完成"
    PENDING_APPROVAL --> COMPLETED: "审核通过(调整库存)"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    COMPLETED --> [*]
    CANCELLED --> [*]
```

---
#### **六、 追溯与库存实体 (Entities)**

##### **6.1 产品实例 (`pro_instance`)**
```mermaid
stateDiagram-v2
    [*] --> IN_PRODUCTION: "首次报工"
    IN_PRODUCTION --> COMPLETED: "末站完工"
    COMPLETED --> IN_STOCK: "入库"
    IN_STOCK --> SHIPPED: "发货"
    
    state fork_scrap <<fork>>
    IN_PRODUCTION --> fork_scrap
    IN_STOCK --> fork_scrap
    fork_scrap --> SCRAPPED: "报废"
    
    SHIPPED --> [*]
    SCRAPPED --> [*]
```

##### **6.2 库存状态机 (`wms_inventory_batch`)**
```mermaid
stateDiagram-v2
    [*] --> AVAILABLE: "入库/解冻"
    AVAILABLE --> ON_HOLD: "盘点/人工冻结"
    ON_HOLD --> AVAILABLE: "解冻"
    
    AVAILABLE --> IN_TRANSIT: "移库(下架)"
    IN_TRANSIT --> AVAILABLE: "移库(上架)"
    
    state fork_terminal <<fork>>
    AVAILABLE --> fork_terminal
    ON_HOLD --> fork_terminal
    fork_terminal --> EXPIRED: "到期"
    fork_terminal --> [*]: "出库/消耗"
    
    EXPIRED --> [*]: "报废处置"
```

---
#### **七、 财务对账 (Finance Module)**

##### **7.1 应付单 (`erp_fin_ap_invoice`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNPAID: "确认"
    UNPAID --> PARTIALLY_PAID: "部分付款"
    UNPAID --> FULLY_PAID: "全额付款"
    PARTIALLY_PAID --> FULLY_PAID: "付清余款"
    FULLY_PAID --> [*]
```

##### **7.2 付款单 (`erp_fin_ap_payment_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNAPPLIED: "确认/待核销"
    UNAPPLIED --> PARTIALLY_APPLIED: "部分核销"
    UNAPPLIED --> FULLY_APPLIED: "完全核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "剩余核销"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    UNAPPLIED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    FULLY_APPLIED --> [*]
    CANCELLED --> [*]
```

##### **7.3 对账单 (`erp_fin_statement`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> CONFIRMED: "双方确认"
    CONFIRMED --> SETTLED: "完成结算"
    SETTLED --> [*]
```

##### **7.4 应收单 (`erp_fin_ar_receivable`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    DRAFT --> UNPAID: "确认/待收款"
    UNPAID --> PARTIALLY_PAID: "收到部分款"
    UNPAID --> FULLY_PAID: "收到全款"
    PARTIALLY_PAID --> FULLY_PAID: "收清余款"
    FULLY_PAID --> [*]
```

##### **7.5 收款单 (`erp_fin_ar_receipt_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建"
    
    state fork_type <<fork>>
    DRAFT --> fork_type
    fork_type --> UNAPPLIED: "普通收款"
    fork_type --> ADVANCE_PAYMENT: "预收款"

    UNAPPLIED --> PARTIALLY_APPLIED: "部分核销"
    UNAPPLIED --> FULLY_APPLIED: "完全核销"
    ADVANCE_PAYMENT --> PARTIALLY_APPLIED: "部分核销"
    ADVANCE_PAYMENT --> FULLY_APPLIED: "完全核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "剩余核销"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    UNAPPLIED --> fork_cancel
    ADVANCE_PAYMENT --> fork_cancel
    fork_cancel --> CANCELLED: "取消"

    FULLY_APPLIED --> [*]
    CANCELLED --> [*]
```
