
---

### **1. 系统模块说明与作用**

| 模块前缀 | 模块全称 | 核心作用与职责 |
| :--- | :--- | :--- |
| **`erp_`** | **企业资源计划 (Enterprise Resource Planning)** | **业务运营与财务中枢**。管理企业的核心商业流程，如销售、采购、财务（应收、应付）。它是企业经营活动的记录者和价值核算者。 |
| **`mes_`** | **制造执行系统 (Manufacturing Execution System)** | **车间生产的指挥官与书记员**。负责将生产订单转化为可执行的工单，监控生产过程，采集现场数据（报工、追溯），确保生产指令被准确、高效地执行。 |
| **`wms_`** | **仓库管理系统 (Warehouse Management System)** | **企业物料的守护者与调度员**。负责所有物料的物理位置和数量的精确管理，执行所有出、入、移、盘等仓储作业，保证账实相符。 |
| **`pro_`/`base_`**| **产品与基础主数据 (Product & Base Master Data)**| **整个系统的数据基石**。管理所有模块共享的核心静态数据，如产品信息、BOM、工艺路线、物料、单位、供应商/客户等。 |
| **`sys_`** | **系统管理 (System Administration)** | **系统的支撑框架**。提供用户、角色、权限、菜单、租户、字典等基础平台功能，保障整个系统的安全、稳定运行。 |

---

### **2. 系统宏观交互说明与流程图**

这张宏观交互图描绘了您的核心系统模块如何协同工作，共同完成从市场需求到客户交付的全过程。

```mermaid
graph TD
    %% 子图定义
    subgraph " "
        direction LR
        subgraph "外部实体"
            Customer[客户]
            Supplier[供应商]
        end

        subgraph "核心系统 (你的软件)"
            subgraph "运营与财务层 (神经中枢)"
                ERP["<b style='color:#2196F3;'>ERP 企业资源计划</b><br>销售/采购/财务"]
            end
            
            subgraph "执行层 (四肢)"
                MES["<b style='color:#FF9800;'>MES 制造执行</b>"]
                WMS["<b style='color:#4CAF50;'>WMS 仓库管理</b>"]
            end
            
            subgraph "基础层 (数据基石)"
                MDM["<b style='color:#607D8B;'>PRO/BASE 主数据</b>"]
            end
        end
    end

    %% 模块依赖关系图
    subgraph "模块依赖关系图"
        direction TB
        MDM -->|依赖| ERP
        MDM -->|依赖| WMS
        MDM -->|依赖| MES
        ERP -->|驱动| MES
        ERP -->|驱动| WMS
        WMS -->|反馈| ERP
        MES -->|反馈| ERP
        MES -->|交互| WMS
        WMS -->|交互| MES
    end

    %% 分层架构设计
    subgraph "分层架构设计"
        direction TB
        Client[客户端层] --> Gateway[网关层]
        Gateway --> Application[应用层]
        Application --> Framework[框架层]
        Framework --> Data[数据层]
    end

    %% 技术栈全景
    subgraph "技术栈全景"
        graph LR
            pie
                title 技术栈占比
                "Spring Boot 3.4" : 30
                "JDK21" : 20
                "Undertow" : 15
                "Sa-Token" : 15
                "MyBatis-Plus" : 20
    end

    %% 交互流程
    Customer -- "1. 销售订单" --> ERP
    Supplier -- "采购订单" --> ERP
    ERP -- "应付" --> Supplier
    
    ERP -- "2. 生产订单" --> MES
    ERP -- "3. 采购订单" --> WMS["触发采购入库流程"]
    ERP -- "4. 销售订单" --> WMS["触发销售出库流程"]
    
    MES -- "5. 领/退料请求" --> WMS
    MES -- "6. 完工入库请求" --> WMS
    
    WMS -- "7. 物料/库存变动" --> ERP["更新库存账"]
    WMS -- "8. 物料/库存变动" --> MES["更新工单用料"]
    
    MES -- "9. 生产数据(工时/消耗)" --> ERP["用于成本核算"]
    
    MDM -- "基础数据支持" --> ERP
    MDM -- "基础数据支持" --> MES
    MDM -- "基础数据支持" --> WMS

```

**宏观流程解读**

1.  **需求输入**: 流程始于`ERP`，它接收来自**客户**的销售订单或内部制定的生产预测。

2.  **指令下发**:
    *   `ERP`根据销售订单或生产计划，创建**生产订单**并下发给`MES`执行。
    *   `ERP`根据采购需求创建**采购订单**，其收货流程会触发`WMS`的入库作业。
    *   `ERP`根据销售订单创建**销售出库单**，触发`WMS`的出库作业。

3.  **车间与仓库交互**:
    *   `MES`根据生产订单执行生产，并向`WMS`发起领料、退料、成品入库等请求。
    *   `WMS`执行这些仓储作业。

4.  **信息回传**:
    *   `WMS`完成任何出入库操作后，都会将结果（如实际出入库数量）回传给`ERP`以更新财务库存，并回传给`MES`以更新工单的物料消耗状态。
    *   `MES`将生产过程中的数据（如实际工时、物料消耗、产成品数量）回传给`ERP`，作为成本核算的基础数据。

5.  **外部协同**: `ERP`作为统一的对外窗口，负责与**供应商**和**客户**的所有业务和财务往来。

6.  **数据基石**: `主数据(MDM)`模块为所有上层系统提供统一、准确的基础数据支持，确保了整个系统数据的一致性。
