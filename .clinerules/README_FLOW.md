
---

### **系统核心业务流程图全集**

**图例说明:**
*   `subgraph "模块"`: 表示该流程节点所属的系统或业务域。
*   `A["单据<br>状态"]`: 表示一个具体的业务单据及其当前状态。
*   `B{"事件/操作"}`: 表示一个触发流程变化的用户操作或系统事件。
*   `C -- "动作" --> D`: 表示数据流或指令的传递方向。

---
#### **1. 采购流程 (Purchase Flow)**

##### **1.1 标准采购入库流程**
*   **场景描述**: 从创建采购订单，到供应商送货，再到仓库收货入库的完整正向流程。
```mermaid
graph TD
    subgraph "ERP 系统 (业务层)"
        A["erp_purchase_order <br> DRAFT"] -- "审核" --> B["erp_purchase_order <br> CONFIRMED"];
        B -- "供应商到货" --> C{"创建 erp_purchase_inbound"};
        C --> D["erp_purchase_inbound <br> PENDING_WAREHOUSE"];
    end

    subgraph "WMS 系统 (执行层)"
        E["wms_inbound <br> PENDING_RECEIPT"] -- "仓库收货/上架" --> F["wms_inbound <br> COMPLETED"];
    end

    subgraph "ERP 系统 (回传)"
        G["erp_purchase_inbound <br> COMPLETED"] -- "更新订单" --> H["erp_purchase_order <br> PARTIALLY/FULLY_RECEIVED"];
    end

    D -- "调用 inboundService.createFromPurchaseInbound()" --> E;
    F -- "回传状态更新 erp_purchase_inbound.status = COMPLETED" --> G;
```

##### **1.2 采购退货出库流程**
*   **场景描述**: 已入库的货物发现问题，需要退还给供应商。
```mermaid
graph TD
    subgraph "ERP 系统 (业务层)"
        A["已完成的 erp_purchase_inbound"] -- "发起退货" --> B["erp_purchase_return <br> PENDING_WAREHOUSE"];
    end

    subgraph "WMS 系统 (执行层)"
        C["wms_outbound <br> PENDING_PICKING"] -- "仓库拣货/发运" --> D["wms_outbound <br> COMPLETED"];
    end

    subgraph "ERP 系统 (回传)"
        E["erp_purchase_return <br> COMPLETED"] -- "更新订单" --> F["erp_purchase_order <br> (已收货数减少)"];
    end

    B -- "推送出库指令" --> C;
    D -- "回传出库结果" --> E;
```

---
#### **2. 销售流程 (Sale Flow)**

##### **2.1 标准销售出库流程**
*   **场景描述**: 从创建销售订单，到通知仓库发货，再到货物出库的完整正向流程。
```mermaid
graph TD
    subgraph "ERP 系统 (业务层)"
        A["erp_sale_order <br> CONFIRMED"] -- "通知发货" --> B["erp_sale_outbound <br> PENDING_WAREHOUSE"];
    end

    subgraph "WMS 系统 (执行层)"
        C["wms_outbound <br> PENDING_PICKING"] -- "仓库拣货/发运" --> D["wms_outbound <br> COMPLETED"];
    end

    subgraph "ERP 系统 (回传)"
        E["erp_sale_outbound <br> COMPLETED"] -- "更新订单" --> F["erp_sale_order <br> PARTIALLY/FULLY_SHIPPED"];
    end

    B -- "调用 outboundService.createFromSaleOutbound()" --> C;
    D -- "回传状态更新 erp_sale_outbound.status = COMPLETED" --> E;
```

##### **2.2 销售退货入库流程**
*   **场景描述**: 客户将已售出的货物退回。
```mermaid
graph TD
    subgraph "ERP 系统 (业务层)"
        A["erp_sale_order<br>(历史记录)"] -- "客户发起退货" --> B["erp_sale_return <br> PENDING_WAREHOUSE"];
    end

    subgraph "WMS 系统 (执行层)"
        C["wms_inbound <br> PENDING_RECEIPT"] -- "仓库收货/上架" --> D["wms_inbound <br> COMPLETED"];
    end

    subgraph "ERP 系统 (回传)"
        E["erp_sale_return <br> COMPLETED"] -- "更新订单" --> F["erp_sale_order <br> (已退货数增加)"];
    end

    B -- "推送入库指令" --> C;
    D -- "回传出库结果" --> E;
```

---
#### **3. 生产流程 (Production Flow)**

##### **3.1 标准生产流程（领料与完工入库）**
*   **场景描述**: 生产订单下达后，先去仓库领料，生产完成后再将成品入库。
```mermaid
graph TD
    subgraph "MES 系统 (生产管理)"
        A["mes_production_order <br> RELEASED"];
        A -- "创建领料需求" --> B["mes_production_issue <br> PENDING_WAREHOUSE"];
        A -- "车间完工报工" --> G["mes_production_inbound <br> PENDING_WAREHOUSE"];
    end
    
    subgraph "WMS 系统 (仓库执行)"
        C["wms_outbound <br> (领料出库) <br> PENDING_PICKING"] -- "仓库发料" --> D["wms_outbound <br> COMPLETED"];
        H["wms_inbound <br> (成品入库) <br> PENDING_RECEIPT"] -- "仓库收货" --> I["wms_inbound <br> COMPLETED"];
    end

    subgraph "MES 系统 (状态回传)"
        E["mes_production_issue <br> COMPLETED"] -- "更新工单状态" --> F["mes_production_order <br> IN_PROGRESS"];
        J["mes_production_inbound <br> COMPLETED"] -- "更新工单状态" --> K["mes_production_order <br> PARTIALLY/FULLY_COMPLETED"];
    end

    B -- "推送领料指令" --> C;
    D -- "回传发料结果" --> E;
    G -- "推送入库指令" --> H;
    I -- "回传入库结果" --> J;
```

##### **3.2 生产退料流程（余料退库）**
*   **场景描述**: 车间将生产多余的物料退回仓库。
```mermaid
graph TD
    subgraph "MES 系统"
        A["mes_production_order <br> IN_PROGRESS"] -- "车间发起退料" --> B["mes_production_return <br> PENDING_WAREHOUSE"];
    end

    subgraph "WMS 系统"
        C["wms_inbound <br> (退料入库) <br> PENDING_RECEIPT"] -- "仓库接收退料" --> D["wms_inbound <br> COMPLETED"];
    end

    subgraph "MES 系统 (回传)"
        E["mes_production_return <br> COMPLETED"] -- "更新工单已领料" --> F["mes_production_order"];
    end

    B -- "推送入库指令" --> C;
    D -- "回传入库结果" --> E;
```

---
#### **4. 财务对账流程 (Finance Flow)**

##### **4.1 应付对账流程（三单匹配）**
*   **场景描述**: 财务人员核对采购订单、入库单和应付，以确认应付账款。
```mermaid
graph TD
    subgraph "业务数据源"
        A["erp_purchase_order"]
        B["erp_purchase_inbound"]
    end

    subgraph "财务凭证"
        C["erp_fin_ap_invoice <br> UNMATCHED"]
    end

    subgraph "财务操作"
        D{"<b>三单匹配工作台</b><br><i>(由 IThreeWayMatchService 实现)</i>"} -- "执行匹配" --> E{"更新 erp_fin_ap_invoice <br> 为 FULLY_MATCHED"};
    end

    subgraph "后续付款"
        F["erp_fin_ap_payment_order"] -- "核销" --> E;
        E -- "被核销后" --> G["erp_fin_ap_invoice <br> PAID"];
    end

    A & B & C --> D;
```

##### **4.2 应收核销流程**
*   **场景描述**: 财务人员将客户的付款流水与应收账款进行核销。
```mermaid
graph TD
    subgraph "数据源"
        A["erp_fin_ar_receivable <br> UNPAID"]
        B["erp_fin_ar_receipt_order <br> UNAPPLIED"]
    end
    
    subgraph "财务操作"
        C{"应收核销工作台"} -- "执行核销" --> D{"创建核销关系 <br>erp_fin_ar_receipt_receivable_link"};
    end

    subgraph "状态更新"
        D -- "更新应收单状态" --> E["erp_fin_ar_receivable <br> PARTIALLY_PAID / FULLY_PAID"]
        D -- "更新收款单状态" --> F["erp_fin_ar_receipt_order <br> PARTIALLY_APPLIED / FULLY_APPLIED"]
    end
    
    A & B --> C
```

---
#### **5. 生产报工流程**

##### **5.1 生产报工与追溯核心数据流**
*   **场景描述**: 从生产订单下达到最终报工，数据是如何在MES核心表中创建和关联的。
```mermaid
graph TD
    subgraph "计划与工艺"
        A["mes_production_order <br>(生产订单)"]
        B["pro_routing <br>(工艺路线)"]
    end

    subgraph "执行与追溯"
        C["pro_instance <br>(产品实例)"]
        D["wms_inventory_batch <br>(消耗的物料批次)"]
        E["pro_instance_usage <br>(实例用料记录)"]
        F["mes_production_report <br>(报工记录)"]
    end
    
    A -- "下达时创建" --> C
    B -- "指导" --> F
    C -- "报工产生" --> F
    C -- "用料关联" --> E
    D -- "被消耗" --> E
```

---
#### **6. 价格信息传递流程**
*   **场景描述**: 采购价格如何最终成为库存成本，并在生产中被使用。
```mermaid
graph TD
    subgraph "1.主数据 & 订单"
        A["pro_product <br> (参考采购价/不含税)"] -- "自动填充" --> B["erp_purchase_order_item <br> (交易采购价/不含税)"];
    end

    subgraph "2.采购入库"
        B -- "复制价格" --> C["erp_purchase_inbound_item <br> (入库价格/不含税)"];
        C -- "复制价格" --> D["erp_purchase_inbound_item_batch"];
    end

    subgraph "3.库存价值核心"
        D -- "写入成本价" --> E["wms_inventory_batch <br> cost_price (不含税成本价)"];
    end

    subgraph "4.生产消耗"
        F["pro_instance_usage <br> (物料消耗)"] -- "关联消耗的库存" --> E;
    end
    
    subgraph "5.产成品入库"
        H["pro_product <br> (标准成本)"] -- "填充" --> I["mes_production_inbound_item <br> (预估成本)"];
        I --> J["mes_production_inbound_item_batch"];
        J -- "写入成本价" --> E;
    end
```

# 模块交互流程图 🎯

## 全局业务流程
``mermaid
graph LR
A[BASE模块] --> B[PRO模块]
B --> C[ERP模块]
C --> D[WMS模块]
D --> E[MES模块]
E --> F[QMS模块]
F --> G[APS模块]
end
```

## 子流程分解

### BASE模块流程
``mermaid
subgraph BASE
B1[用户管理] --> B2[角色分配]
B2 --> B3[部门归属]
end
```

### ERP模块流程
``mermaid
subgraph ERP
E1[创建订单] --> E2[审核确认]
E2 --> E3[等待到货]
E3 --> E4[入库完成]
end
```

### WMS模块流程
```mermaid
subgraph WMS
    subgraph "入库流程"
        W1[PENDING_RECEIPT] --> W2[PARTIALLY_RECEIVED]
        W2 --> W3[COMPLETED]
        W1 --> W3
    end

    subgraph "出库流程"
        W4[PENDING_PICKING] --> W5[PICKING_IN_PROGRESS]
        W5 --> W6[PICKED]
        W6 --> W7[SHIPPED]
        W7 --> W8[COMPLETED]
    end

    subgraph "库存管理"
        W9[AVAILABLE] --> W10[FROZEN]
        W10 --> W9
        W9 --> W11[RESERVED]
        W11 --> W9
    end
end
```

## 流程交互示意
``mermaid
sequenceDiagram
    participant BASE
    participant ERP
    participant WMS
    
    BASE->>ERP: 用户权限验证
    ERP->>WMS: 库存占用请求
    WMS->>ERP: 库存状态响应
    ERP->>BASE: 操作日志记录
end
```

