# 枚举定义与使用规范 🎯

## 枚举实现标准
```mermaid
classDiagram
    class IDictEnum {
        <<interface>>
        +int getValue()
        +String getDictName()
        +String getDictDesc()
    }
    
    class OrderStatus {
        -int value
        -String dictName
        -String dictDesc
        +getValue()
        +getDictName()
        +getDictDesc()
    }
    
    IDictEnum <|.. OrderStatus
    OrderStatus : 实现IDictEnum接口
end
```

## 枚举迁移指南
```mermaid
journey
    title 枚举迁移流程
    section 旧代码改造
      替换常量: 5: Done
      数据库兼容: 5: Doing
    section 接口适配
      DTO调整: 3: Pending
      日志优化: 2: Pending
end
```

## 常见问题处理
```mermaid
flowchart LR
    A[未知枚举值] --> B[返回UNKNOWN枚举项]
    C[大小写不一致] --> D[解析时忽略大小写差异]
    E[多语言支持] --> F[通过dictDesc存储不同语言描述]
end
```