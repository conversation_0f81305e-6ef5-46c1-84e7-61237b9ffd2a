# 财务模块开发规范 🎯

## 核心业务规则

### 核销关系表结构
```sql
-- 标准核销关系表结构
CREATE TABLE fin_ap_payment_invoice_link (
    id BIGINT PRIMARY KEY,
    payment_id BIGINT NOT NULL,
    invoice_id BIGINT NOT NULL,
    applied_amount DECIMAL(18,2) NOT NULL,
    create_by BIGINT NOT NULL,
    create_time DATETIME NOT NULL
);
```

### 价格计算公式
```java
// 税金计算服务
public BigDecimal calculateInclusivePrice(BigDecimal exclusivePrice, BigDecimal taxRate) {
    return exclusivePrice.multiply(taxRate.divide(BigDecimal.valueOf(100)))
                   .add(exclusivePrice)
                   .setScale(2, RoundingMode.HALF_UP);
}
```

## 字段命名规范
| 字段类型 | 命名示例 | 说明 |
|----------|----------|------|
| 税前价 | price_exclusive_tax | 不含税的价格 |
| 税前金额 | amount_exclusive_tax | 不含税的金额 |
| 税额 | tax_amount | 计算得出的税金 |
| 含税价 | price_inclusive_tax | 包含税金的价格 |

## 公式库
- **税额计算**：taxAmount = priceExclusiveTax * (taxRate / 100)
- **含税价计算**：priceInclusiveTax = priceExclusiveTax + taxAmount
- **折扣计算**：discountedPrice = originalPrice * (1 - discountRate)