# Preferences
- User prefers systematic module completion by priority (Purchase→Sales→Production), following established patterns from PurchaseOrderServiceImpl/SaleOutboundServiceImpl, with strict state management, transaction control, and immediate Controller implementation after each Service completion.
- User prefers comprehensive module completion following established patterns, including sales outbound/return, purchase inbound/return, production orders/issue/return/inbound modules, referencing README files for completeness.
- User noted that ERP financial modules (accounts receivable, purchase reconciliation) still need completion, and emphasized checking README_FLOW, README_STATE, README_STATUS, README_CATEGORY, README_OVERVIEW files for proper requirements.
- User prefers systematic module completion workflow: read .doc folder documentation first, create detailed priority-based plan (BASE→PRO→ERP→WMS→MES→QMS→APS→PRO), complete modules one-by-one with Service layer Boolean returns and @Transactional annotations, generate module completion MD files, and use MCP feedback tool for each stage confirmation.
- User requires specific business flow methods for ERP (SaleOrder, ArReceivable) and APS (Demand) modules with standard patterns: confirm/cancel/hold operations, batch processing, MRP calculations, and cross-module creation methods, all following @Transactional annotation and Boolean/VO return type standards.
- User prefers MES production order business methods to follow specific state transition patterns (DRAFT→CONFIRMED→IN_PROGRESS→ON_HOLD/COMPLETED→CANCELLED) with comprehensive business rule validation, detailed logging, and referencing existing startProduction() method patterns for consistency.
- User prefers consistent business method implementation patterns across all modules (Sales, Demand, Production, WMS) with standardized status management, transaction control, and business rule validation following RuoYi-Vue-Plus 5.3.1 framework specifications.
- User prefers systematic ItemMapper.xml completion following PurchaseOrderItemMapper.xml pattern: complete Controller/Service/Mapper layers with queryByIdWith/queryPageListWith methods, include all 10 product fields, process by module priority (ERP→WMS→MES→QMS→COMMON), and generate completion reports.
- User prefers systematic ItemMapper.xml completion with specific priority order: first complete files with basic structure (MES/ERP/COMMON modules), then empty files, following PurchaseOrderItemMapper.xml pattern with queryByIdWith/queryPageListWith methods and all 10 new product fields.
- User prefers comprehensive database field additions for PRO module products including price fields (purchase/sale prices with tax calculations), quality inspection plan associations, with BigDecimal precision, tax rate validation (0-100%), and automatic price calculations following 含税价 = 不含税价 × (1 + 税率) formula across all code layers (Entity, DTO, Controller, Service, Mapper).
- For ERP invoice item tables, use field naming pattern: price_exclusive_tax, amount_exclusive_tax to clearly indicate tax-exclusive values, maintaining consistency with price-tax separation design principles.
- User prefers systematic module improvement following BASE→PRO→ERP→WMS→MES→QMS→APS→PRO priority order, with documentation-driven development based on MD files in doc folders, strict adherence to RuoYi-Vue-Plus 5.3.1 framework standards, and mandatory use of MCP feedback tool for stage confirmations with final MD summary generation in corresponding doc folders.
- User wants to focus specifically on financial module development for iotlaser-spms project, requiring comprehensive analysis of existing financial functions, creation of development roadmap with phased approach, and establishment of continuous execution mechanism with progress tracking and quality assurance.
- For financial module核销关系表 design: only keep essential fields (IDs, amounts, timestamps, operators), remove redundant fields (supplier/customer codes/names), remove status fields, use association queries for detailed info instead of redundant storage.
- User requires systematic TODO completion in financial modules with specific focus on payment reconciliation optimization, supplier statement generation, comprehensive code review of 9 financial service files, and adherence to RuoYi-Vue-Plus 5.4.0 framework standards with complete transaction management and exception handling.
- User requires standardized enum management for all form statuses and types, requiring review of existing enums to identify missing statuses/types and creation of step-by-step execution plans for implementation.
- User requires documentation-driven development workflow: analyze 5 design docs in docs/design folder first, create detailed phase-by-phase plans with BASE→PRO→ERP→WMS→MES→QMS→APS→PRO priority, use MCP Interactive Feedback tool after each phase completion, generate completion summaries in docs/schedule folder, and strictly follow RuoYi-Vue-Plus 5.4.0 standards with existing patterns.
- User strictly prohibits creating new database entity classes and requires using existing entities (FinApPaymentInvoiceLink, FinArReceiptReceivableLink) for all business logic implementation, focusing on enhancing existing Service methods rather than creating new tables.
- User prefers systematic post-compilation improvement workflow: analyze empty methods and TODOs across all Service classes, prioritize by business importance and module dependencies, focus on cross-module data flow (ERP-MES-WMS-QMS-APS-PRO integration), implement complete business logic with exception handling and logging, execute in phases with clear acceptance criteria, emphasizing financial reconciliation, production workflows, sales-purchase integration, inventory batch management, and quality inspection processes.
- User prefers comprehensive Service layer improvements with focus on: batch info generation/filling, price-tax separation calculations with BigDecimal precision, redundant field auto-filling (supplier_name, customer_name, product_name, responsibility tracking), simplified data validation (core business logic only, comment out format validations), complete unit test coverage for batch/amount/data filling logic, and modular execution approach (ERP→WMS→MES priority).
- User prefers enum comparison and assignment optimization: use direct enum comparison with == operator instead of string comparison, use direct enum assignment instead of getValue() string assignment, ensure Entity/BO/VO classes have enum field types rather than String types, maintain MyBatis-Plus @EnumValue compatibility.
- User requires systematic enhancement of all IDictEnum enums with DICT_NAME (Chinese display name) and DICT_DESC (detailed business description) constants plus corresponding getDictName()/getDictDesc() methods, following GenCodeType standard format, processed by module priority BASE→PRO→ERP→WMS→MES→QMS→APS.

# Compilation Error Fixing
- User prefers comprehensive batch error fixing approach - fix all compilation errors at once without stopping for intermediate confirmations.
- User prefers systematic compilation error fixing with specific priority order: type incompatibility errors (String↔Enum with .getValue()) → missing methods → Service dependency injection → other errors, using TODO comments for complex business logic, maintaining existing architecture, and targeting 80%+ fix rate with verification every 10-15 fixes.
- User prefers systematic compilation error fixing with specific priority order: method not found errors (30) → field not found errors (25) → import missing errors (15) → type conversion errors (11), using comment+TODO+alternative solution three-step fix method, with compilation verification every 10-15 fixes and maintaining business logic integrity.
- User prefers systematic compilation error fixing with priority on type mismatches (BigDecimal for financial fields, enum.getValue() for BO/String conversions, Entity uses enum directly), maintaining BO-Entity type consistency, adding detailed TODO comments, and achieving 50%+ fix rate through standardized repair patterns.
- User prefers systematic compilation error fixing with specific priority order (MapStruct→Import→Type details→Method signatures), batch verification every 10-15 fixes, TODO completion during fixes, and 100% compilation success as quality standard following RuoYi-Vue-Plus 5.4.0 framework specifications.
- User prefers systematic compilation error fixing approach: Phase 1 - unify enum usage to .getValue(), Phase 2 - fix type conversions (enum↔String, Long↔BigDecimal), Phase 3 - add missing methods/fields, with recompilation every 10-15 fixes and adherence to RuoYi-Vue-Plus 5.4.0 standards.
- User prefers systematic compilation error fixing with specific priority order (Phase 1 - enum standardization (getStatus/getType/getBeanIndex → getValue), Phase 2 - type conversion fixes (Long→BigDecimal, String↔enum, LocalDate↔Date), Phase 3 - missing methods/fields), with recompilation every 10-15 fixes and TODO comments for uncertain solutions.

# RuoYi-Vue-Plus Framework Standards
- For RuoYi-Vue-Plus 5.3.1: Service methods should return VO objects (except insertByBo/updateByBo returning Boolean), use @Transactional annotations, cross-module communication must use VO/BO only (no direct Entity exposure), use unified insertOrUpdateBatch methods, avoid exists with LambdaQueryWrapper - pass parameters to Service instead, systematic module development order BASE→PRO→ERP→WMS→MES→QMS→APS with milestone summaries and interactive feedback.
- User requires strict adherence to existing table structures and field names when developing financial modules, prohibits modifying entity field names/types except for obvious errors like Long to BigDecimal, and mandates following RuoYi-Vue-Plus 5.4.0 framework standards with MCP feedback confirmation at each stage.
- For financial module development: strictly prohibit any database table structure modifications, only adjust Java code to match existing table structure, and prioritize compilation error fixes and property alignment between Entity/BO/VO classes.
- User requires price-tax separation implementation in financial services with specific formulas: tax amount = exclusive amount × tax rate, inclusive amount = exclusive amount + tax amount, using BigDecimal for precision and following RuoYi-Vue-Plus 5.4.0 standards.
- Project uses warm-flow as workflow engine with external process control pattern where business tables should only maintain business status and not store workflow engine internal fields like processInstanceId, submitTime, approveTime.

# Project Completion
- User has systematically improved 5 core modules (BASE, ERP, WMS, MES, QMS) in the iotlaser-admin project, standardizing Service layer method return types to Boolean, adding transaction management and exception handling, supplementing enum classes, and optimizing approximately 30 Service implementations to ensure code standardization and business reliability.
- User has completed systematic improvements to all 7 modules (BASE, ERP, WMS, MES, QMS, APS, PRO) of the iotlaser-spms project: approximately 80 Service implementations standardized, 35 new enum classes added, 30 auto-coding types configured, unified transaction management, exception handling, and logging implemented, establishing a complete enterprise-level ERP+MES+WMS+QMS+APS+PRO integrated system.
- BASE module is 100% complete (6 Services). ERP module is 30% complete (approximately 9 Services completed, 21 remaining). WMS module is 20% complete (interface return types corrected, 3 Services completed, 12 remaining). MES, QMS, APS, and PRO modules are pending improvement. Approximately 20 out of 80 Services (25%) have been standardized, establishing a standardized implementation pattern and interface layer correction guidelines.
- Systematic improvements based on business processes have been carried out in depth: improved inventory record generation for purchase inbound, complete sales outbound process, production order status flow, inventory adjustment mechanism for inventory counting, complete quality inspection process (start inspection → complete inspection → audit inspection), and MRP calculation process for APS demand management (confirm demand → generate plan → release plan), with complete business status flow and core business logic in each module.
- Completed full business process functionality implementation for all modules: corrected BigDecimal data type errors, improved ERP accounts receivable and payable modules (ArPaymentServiceImpl complete payment process: confirm → complete → cancel), APS module (DemandServiceImpl complete MRP process: confirm demand → generate plan → release plan, RunServiceImpl complete plan execution: start → pause → resume → complete), QMS module (InspectionServiceImpl complete inspection process: start → complete inspection → audit inspection), all modules have complete status flow and core business logic.

# BASE Module Completion
- BASE module has been systematically improved according to project documentation: 6 Service implementations standardized (CompanyServiceImpl, LocationServiceImpl, MeasureUnitServiceImpl, AutoCodePartServiceImpl, etc.), AutoCodePartType enum added, data validation and exception handling improved, and unified transaction management and logging implemented, providing reliable basic data management support for other modules.

# Service Layer Standardization
- Service layer standardization: insertByBo() and updateByBo() methods should return Boolean type with @Transactional annotation, exception handling, and result validation.
- Status/del_flag fields are auto-filled by the database and should not be manually set in the Service layer.
- All status/type fields need corresponding enum classes following naming convention {ModuleName}{EntityName}Status/Type.
- For enum optimization in service layers: use == operator for enum comparisons, maintain Entity layer getValue() compatibility for database fields, preserve business logic exactly, verify compilation after each service fix, and prioritize core business flows (Inbound/Outbound) over supporting functions (Transfer/Check).

# Financial Module Business Flows
- Standard financial business flows: Sales (SaleOrder→SaleOutbound→出库完成→FinArReceivable→FinArReceiptOrder→核销→FinAccountLedger) and Purchase (PurchaseOrder→PurchaseInbound→入库完成→FinApInvoice→FinApPaymentOrder→核销→FinAccountLedger) with key timing rules: receivables generated after outbound completion, payables generated after inbound completion, and payment orders generated after receivable/payable creation.

# Inventory Management
- Inventory is materialized view for product/quantity statistics, Inventory is real inventory for product/location/quantity tracking, InventoryLog records inventory transaction history.

# Unit Testing
- User requires comprehensive unit testing for iotlaser-spms project using JUnit 5 + Mockito + Spring Boot Test, targeting 80%+ coverage of 284 business methods across 7 modules, with phased approach (BASE→PRO→ERP→WMS→MES/QMS/APS/PRO→Integration) and focus on business flow testing, enum type safety validation, and FIFO batch management algorithm verification.
- User requires adjusted 6-phase testing plan: BASE→PRO→ERP→WMS→MES→FIN (excluding QMS/APS), with specific coverage targets (80% line, 70% branch, 90% method), using MockitoExtension pattern, and requiring 100% compilation/test success rates.
- User requires continuation of 6-phase unit testing plan (BASE→PRO→ERP→WMS→MES→FIN) with current progress: Phase 1 BASE (6 Services, 93 tests) and Phase 2 PRO (6 Services, 120 tests) completed, Phase 3 ERP in progress with SaleOrderServiceImpl and SaleOrderItemServiceImpl done, requiring completion of remaining ERP services including SaleOutboundServiceImpl, PurchaseOrderServiceImpl, PurchaseOrderItemServiceImpl, PurchaseInboundServiceImpl, and financial services, with MCP feedback reporting and test completion reports in docs/schedule folder.
- User prefers comprehensive unit test coverage for main functionality to ensure complete feature testing and isolation from other module errors, focusing on core business flow integrity.

# Enum Standardization
- User requires complete enum standardization without backward compatibility: all enums must have only value/name/desc properties, implement IDictEnum interface, remove all @Deprecated methods, and delete old getStatus/getType methods entirely.
- For enum standardization in iotlaser-spms: all enums must have only value/name/desc properties, implement IDictEnum interface, remove all @Deprecated methods, and delete old getStatus/getType methods entirely.
- User requires all enum classes to implement IDictEnum<String> interface with DICT_CODE constants following naming pattern 'module_entity_field' (e.g., base_company_type, erp_purchase_order_status) and getDictCode() method implementation for dictionary support.
- For iotlaser-spms enum classes: maintain standard three fields (value, name, desc), check for existing methods before adding compatibility methods like getStatus()/getType(), prioritize enum type conversion fixes over BO/Service method additions, and always use codebase-retrieval to verify existing structure before modifications.
- User prefers enum standardization strategy: direct enum object comparisons (== operator), remove redundant getByValue/getByCode/getByType methods, change DTO/BO String fields to enum types, ensure MapStruct handles enum-String conversion, maintain backward compatibility for frontend String inputs, prioritize enum type mismatch fixes over getter/setter issues.
- User requires comprehensive functional verification for enum optimizations with integration tests covering complete business flows, state transition logic validation, exception handling verification, and detailed failure analysis with rollback plans when verification fails.
- User requires comprehensive WMS module enum optimization verification with specific focus on InboundServiceImpl, OutboundServiceImpl, TransferServiceImpl, InventoryServiceImpl, and InventoryAdjustmentServiceImpl, including FIFO batch management logic validation and complete inventory-related enum consistency checks.

# Code Optimization Verification
- User prefers comprehensive verification plans for code optimization projects including compilation verification (100% success), business flow validation (100% integrity), performance testing, compatibility verification, with specific deliverables: test reports, performance analysis, business flow validation, and final summary reports.

# Code Cleanup
- User prefers comprehensive 4-phase code cleanup approach: enum compatibility cleanup (remove @Deprecated methods, ensure IDictEnum implementation), business implementation completeness check (TODO evaluation, empty functions, cross-module interfaces), unit test improvement (80%+ coverage for enum optimizations), and AI-friendly documentation generation with structured Markdown, code highlighting, and clear status indicators.

# Enum Comment Optimization
- User prefers systematic enum comment optimization with specific standards: class comments should briefly describe business purpose, field comments should clearly explain enum values without redundancy, remove outdated method comments, use consistent JavaDoc format, and maintain comment accuracy with actual code functionality.

# Temporary Comment Code Review
- User prefers systematic temporary comment code review with priority-based approach (HIGH/MEDIUM/LOW), dependency analysis, phased enablement by module order (BASE→PRO→ERP→WMS→MES→QMS→APS), comprehensive testing strategy, and rollback plans for each activation phase.

# Documentation Management
- User prefers systematic documentation management with 7-module classification (BASE→PRO→ERP→WMS→MES→QMS→APS), standardized Markdown format with status indicators (✅❌⚠️🎯📊🔧), modular document structure with main index + module-specific files, and integration of technical achievements (enum optimization, comment optimization, temporary code activation) into comprehensive documentation system.