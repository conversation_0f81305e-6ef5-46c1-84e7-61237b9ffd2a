<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iotlaser.spms.common.mapper.SourceMapper">

    <resultMap id="BaseResultMap" type="com.iotlaser.spms.common.domain.Source">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="time" property="time"/>
        <result column="status" property="status"/>
        <result column="summary" property="summary"/>
        <result column="remark" property="remark"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="queryByIdWith" resultMap="BaseResultMap">
        SELECT
        ${sourceInfo.id} as id,
        '${sourceInfo.type}' as type,
        ${sourceInfo.code} as code,
        ${sourceInfo.time} as time,
        ${sourceInfo.status} as status,
        summary,
        remark,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time,
        create_dept,
        tenant_id
        FROM
        ${sourceInfo.main} main
        WHERE main.${sourceInfo.id} = #{id}
        and main.del_flag = '0'
    </select>

    <select id="queryPageListWith" resultMap="BaseResultMap">
        SELECT
        ${sourceInfo.id} as id,
        '${sourceInfo.type}' as type,
        ${sourceInfo.code} as code,
        ${sourceInfo.time} as time,
        ${sourceInfo.status} as status,
        summary,
        remark,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time,
        create_dept,
        tenant_id
        FROM
        ${sourceInfo.main} main
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
    </select>

</mapper>
