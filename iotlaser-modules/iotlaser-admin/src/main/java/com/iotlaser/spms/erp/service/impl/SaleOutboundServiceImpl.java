package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.enums.GenCodeType;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.event.OutboundEvent;
import com.iotlaser.spms.erp.event.SaleOutboundEvent;
import com.iotlaser.spms.erp.mapper.SaleOutboundItemMapper;
import com.iotlaser.spms.erp.mapper.SaleOutboundMapper;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 销售出库单 服务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOutboundServiceImpl implements ISaleOutboundService {

    private final SaleOutboundMapper baseMapper;
    private final SaleOutboundItemMapper itemMapper;
    private final Gen gen;
    private final ISaleReturnService saleReturnService;
    private final IOutboundService outboundService;

    @Autowired
    @Lazy
    private IFinArReceivableService finArReceivableService;

    /**
     * {@inheritDoc}
     */
    @Override
    public SaleOutboundVo queryById(Long outboundId) {
        return baseMapper.selectVoById(outboundId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<SaleOutboundVo> queryPageList(SaleOutboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        Page<SaleOutboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SaleOutboundVo> queryList(SaleOutboundBo bo) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOutbound> buildQueryWrapper(SaleOutboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOutbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(SaleOutbound::getOutboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutboundCode()), SaleOutbound::getOutboundCode, bo.getOutboundCode());
        lqw.eq(bo.getSourceId() != null, SaleOutbound::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), SaleOutbound::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(SaleOutbound::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, SaleOutbound::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), SaleOutbound::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(SaleOutbound::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getCustomerId() != null, SaleOutbound::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleOutbound::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getOutboundTime() != null, SaleOutbound::getOutboundTime, bo.getOutboundTime());
        if (bo.getOutboundStatus() != null) {
            lqw.eq(SaleOutbound::getOutboundStatus, bo.getOutboundStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOutbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            SaleOutbound::getOutboundTime, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOutboundVo insertByBo(SaleOutboundBo bo) {
        log.debug("[insertByBo] - start, bo: {}", bo);
        try {
            if (StringUtils.isBlank(bo.getOutboundCode())) {
                bo.setOutboundCode(gen.code(GenCodeType.ERP_SALE_OUTBOUND_CODE));
                log.debug("[insertByBo] - auto generated code: {}", bo.getOutboundCode());
            }
            if (bo.getOutboundStatus() == null) {
                bo.setOutboundStatus(SaleOutboundStatus.DRAFT);
            }
            if (bo.getOutboundTime() == null) {
                bo.setOutboundTime(LocalDateTime.now());
            }

            SaleOutbound add = MapstructUtils.convert(fillRedundantFields(bo), SaleOutbound.class);
            validEntityBeforeSave(add);

            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("新增失败");
            }
            bo.setOutboundId(add.getOutboundId());
            log.info("[insertByBo] - success, id: {}, code: {}", add.getOutboundId(), add.getOutboundCode());
            return MapstructUtils.convert(add, SaleOutboundVo.class);
        } catch (ServiceException e) {
            log.warn("[insertByBo] - error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[insertByBo] - error: {}", e.getMessage(), e);
            throw new ServiceException("新增失败, 请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOutboundVo updateByBo(SaleOutboundBo bo) {
        log.debug("[updateByBo] - start, bo: {}", bo);
        try {
            fillRedundantFields(bo);
            SaleOutbound update = MapstructUtils.convert(bo, SaleOutbound.class);
            validEntityBeforeSave(update);

            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getOutboundId());
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());

            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                throw new ServiceException("修改失败, 单据可能已被删除");
            }
            log.info("[updateByBo] - success, id: {}", update.getOutboundId());
            return MapstructUtils.convert(update, SaleOutboundVo.class);
        } catch (ServiceException e) {
            log.warn("[updateByBo] - error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[updateByBo] - error: {}", e.getMessage(), e);
            throw new ServiceException("修改失败, 请联系管理员");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOutbound entity) {
        if (StringUtils.isNotBlank(entity.getOutboundCode()) &&
            baseMapper.exists(new LambdaQueryWrapper<SaleOutbound>()
                .eq(SaleOutbound::getOutboundCode, entity.getOutboundCode())
                .ne(entity.getOutboundId() != null, SaleOutbound::getOutboundId, entity.getOutboundId()))) {
            throw new ServiceException("出库单编码 [" + entity.getOutboundCode() + "] 已存在");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("[deleteWithValidByIds] - start, ids: {}, isValid: {}", ids, isValid);
        try {
            if (isValid) {
                baseMapper.selectByIds(ids).forEach(outbound -> {
                    if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()) {
                        throw new ServiceException("出库单 [" + outbound.getOutboundCode() + "] 状态不为草稿, 无法删除");
                    }
                });
            }
            itemMapper.deleteByOutboundIds(ids);
            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("[deleteWithValidByIds] - success, ids: {}", ids);
            } else {
                log.warn("[deleteWithValidByIds] - no rows affected, ids: {}", ids);
            }
            return result;
        } catch (ServiceException e) {
            log.warn("[deleteWithValidByIds] - error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - error: {}", e.getMessage(), e);
            throw new ServiceException("删除失败, 请联系管理员");
        }
    }

    /**
     * 根据销售订单ID查询出库单列表
     *
     * @param orderId 销售订单ID
     * @return 销售出库单列表
     */
    @Override
    public List<SaleOutbound> queryByDirectSourceId(Long orderId) {
        return baseMapper.queryByDirectSourceId(orderId);
    }

    /**
     * 检查销售订单是否已存在关联的出库单
     *
     * @param orderId 销售订单ID
     * @return true-存在, false-不存在
     */
    @Override
    public Boolean existsByDirectSourceId(Long orderId) {
        return baseMapper.existsByDirectSourceId(orderId);
    }

    /**
     * 根据出库单ID查询明细
     *
     * @param outboundId 出库单ID
     * @return 销售出库单明细列表
     */
    @Override
    public List<SaleOutboundItem> queryItemByOutboundId(Long outboundId) {
        return itemMapper.queryByOutboundId(outboundId);
    }

    /**
     * 确认销售出库单
     *
     * @param outboundId 待确认的出库单ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOutbound(Long outboundId) {
        try {
            R<SaleOutbound> saleOutboundValid = saleOutboundValid(outboundId, false);
            if (R.isError(saleOutboundValid)) {
                throw new ServiceException(saleOutboundValid.getMsg());
            }
            SaleOutbound saleOutbound = saleOutboundValid.getData();
            if (SaleOutboundStatus.DRAFT != saleOutbound.getOutboundStatus()) {
                throw new ServiceException("销售出库单 [" + saleOutbound.getOutboundCode() + "] 状态为“" + saleOutbound.getOutboundStatus().getDesc() + "”，仅草稿状态可确认");
            }
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(outboundId);
            saleOutbound.setAmount(calculated.getAmount());
            saleOutbound.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            saleOutbound.setTaxAmount(calculated.getTaxAmount());
            saleOutbound.setOutboundStatus(SaleOutboundStatus.PENDING_WAREHOUSE);

            boolean result = baseMapper.updateById(saleOutbound) > 0;
            if (!result) {
                throw new ServiceException("确认失败");
            }
            createOutboundFromConfirm(saleOutbound);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 完成销售出库
     *
     * @param outboundId 已完成的出库单ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeOutbound(Long outboundId) {
        try {
            R<SaleOutbound> validBefore = saleOutboundValid(outboundId, false);
            if (R.isError(validBefore)) {
                log.error("[completeOutbound] - 前置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validBefore.getMsg());
            }
            SaleOutbound beforeData = validBefore.getData();
            if (SaleOutboundStatus.PENDING_WAREHOUSE != beforeData.getOutboundStatus()) {
                throw new ServiceException("销售出库单 [" + beforeData.getOutboundCode() + "] 状态不是待出库");
            }
            // 更新beforeData内明细的实收数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<SaleOutbound> validAfter = saleOutboundValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.error("[completeOutbound] - 后置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validAfter.getMsg());
            }
            // 更新销售出库单状态为已完成
            SaleOutbound afterData = validAfter.getData();
            afterData.setOutboundStatus(SaleOutboundStatus.COMPLETED);
            String remark = String.format(" [手动完成-%s]", DateUtils.getTime());
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);

            boolean completed = baseMapper.updateById(afterData) > 0;
            if (!completed) {
                throw new ServiceException("完成失败");
            }
            triggerNextStreamProcesses(afterData);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 取消销售出库单
     *
     * @param outboundId 待取消的出库单ID
     * @param reason     取消原因
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOutbound(Long outboundId, String reason) {
        log.info("[cancelOutbound] - start, id: {}, reason: {}", outboundId, reason);
        try {
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("出库单不存在");
            }
            if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()
                && SaleOutboundStatus.PENDING_WAREHOUSE != outbound.getOutboundStatus()) {
                throw new ServiceException("出库单 [" + outbound.getOutboundCode() + "] 状态不为草稿或待出库");
            }
            SaleOutbound update = new SaleOutbound();
            update.setOutboundId(outboundId);
            update.setOutboundStatus(SaleOutboundStatus.CANCELLED);
            if (StringUtils.isNotBlank(reason)) {
                update.setRemark(StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() + " | 取消原因: " + reason : "取消原因: " + reason);
            }
            if (baseMapper.updateById(update) > 0) {
                throw new ServiceException("取消失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 销售出库单确认后创建仓库出库单
     *
     * @param saleOutbound 销售出库单
     */
    private void createOutboundFromConfirm(SaleOutbound saleOutbound) {
        try {
            R<SaleOutbound> saleOutboundValid = saleOutboundValid(saleOutbound, false);
            if (R.isError(saleOutboundValid)) {
                throw new ServiceException(saleOutboundValid.getMsg());
            }
             boolean result = outboundService.createFromSaleOutbound(saleOutbound);
            if (!result) {
                throw new ServiceException("创建失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutbound(Long outboundId) {
        try {
            R<SaleOutbound> saleOutboundValid = saleOutboundValid(outboundId, false);
            if (R.isError(saleOutboundValid)) {
                throw new ServiceException(saleOutboundValid.getMsg());
            }
            SaleOutbound saleOutbound = saleOutboundValid.getData();
            if (!outboundService.createFromSaleOutbound(saleOutbound)) {
                throw new ServiceException("创建仓库出库单失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReturn(Long outboundId) {
        try {
            R<SaleOutbound> saleOutboundValid = saleOutboundValid(outboundId, true);
            if (R.isError(saleOutboundValid)) {
                throw new ServiceException(saleOutboundValid.getMsg());
            }
            SaleOutbound saleOutbound = saleOutboundValid.getData();
            if (!saleReturnService.createFromSaleOutbound(saleOutbound)) {
                throw new ServiceException("创建销售退货单失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建销售出库单的收付款单
     *
     * @param saleOutboundId 销售出库单ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFinReceivable(Long saleOutboundId) {
        try {
            R<SaleOutbound> saleOutboundValid = saleOutboundValid(saleOutboundId, true);
            if (R.isError(saleOutboundValid)) {
                throw new ServiceException(saleOutboundValid.getMsg());
            }
            SaleOutbound saleOutbound = saleOutboundValid.getData();
            if (!finArReceivableService.createFromSaleOutbound(saleOutbound)) {
                throw new ServiceException("创建应收单失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 根据销售订单创建出库单
     *
     * @param saleOrder 销售订单
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromSaleOrder(SaleOrder saleOrder) {
        try {
            if (SaleOrderStatus.CONFIRMED != saleOrder.getOrderStatus() &&
                SaleOrderStatus.PARTIALLY_SHIPPED != saleOrder.getOrderStatus()) {
                throw new ServiceException("创建失败：销售订单状态 [" + saleOrder.getOrderStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(saleOrder.getOrderId());
            if (exists) {
                throw new ServiceException("创建失败：销售订单 [" + saleOrder.getOrderCode() + "] 已生成过销售出库单，不能重复创建");
            }
            SaleOutbound add = new SaleOutbound();
            add.setOutboundCode(gen.code(GenCodeType.ERP_SALE_OUTBOUND_CODE));
            add.setSourceId(saleOrder.getSourceId());
            add.setSourceCode(saleOrder.getSourceCode());
            add.setSourceType(saleOrder.getSourceType());
            add.setDirectSourceId(saleOrder.getOrderId());
            add.setDirectSourceCode(saleOrder.getOrderCode());
            add.setDirectSourceType(DirectSourceType.SALE_ORDER);
            add.setCustomerId(saleOrder.getCustomerId());
            add.setCustomerName(saleOrder.getCustomerName());
            add.setOutboundStatus(SaleOutboundStatus.DRAFT);
            add.setOutboundTime(LocalDateTime.now());
            add.setSummary("[销售订单" + saleOrder.getOrderCode() + "]");

            if (!(baseMapper.insert(add) > 0)) {
                throw new ServiceException("创建销售出库单主表失败");
            }

            if (saleOrder.getItems() != null && !saleOrder.getItems().isEmpty()) {
                List<SaleOutboundItem> outboundItems = saleOrder.getItems().stream().map(saleOrderItem -> {
                    SaleOutboundItem outboundItem = new SaleOutboundItem();
                    outboundItem.setOutboundId(add.getOutboundId());
                    outboundItem.setProductId(saleOrderItem.getProductId());
                    outboundItem.setProductCode(saleOrderItem.getProductCode());
                    outboundItem.setProductName(saleOrderItem.getProductName());
                    outboundItem.setUnitId(saleOrderItem.getUnitId());
                    outboundItem.setUnitCode(saleOrderItem.getUnitCode());
                    outboundItem.setUnitName(saleOrderItem.getUnitName());
                    outboundItem.setQuantity(saleOrderItem.getQuantity());
                    outboundItem.setFinishQuantity(BigDecimal.ZERO);
                    outboundItem.setPrice(saleOrderItem.getPrice());
                    outboundItem.setPriceExclusiveTax(saleOrderItem.getPriceExclusiveTax());
                    outboundItem.setAmount(saleOrderItem.getAmount());
                    outboundItem.setAmountExclusiveTax(saleOrderItem.getAmountExclusiveTax());
                    outboundItem.setTaxRate(saleOrderItem.getTaxRate());
                    outboundItem.setTaxAmount(saleOrderItem.getTaxAmount());
                    outboundItem.setRemark("[销售订单" + saleOrder.getOrderCode() + "]");
                    return outboundItem;
                }).toList();
                if (!itemMapper.insertBatch(outboundItems)) {
                    throw new ServiceException("创建销售出库单明细失败");
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 仓库出库完成事件
     *
     * @param event 事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeOutboundEvent(OutboundEvent event) {
        try {
            Outbound outbound = event.getOutbound();
            if (outbound == null || outbound.getDirectSourceType() != DirectSourceType.SALE_OUTBOUND) {
                return;
            }
            Long outboundId = outbound.getDirectSourceId();
            R<SaleOutbound> validBefore = saleOutboundValid(outboundId, false);
            if (R.isError(validBefore)) {
                log.error("[completeOutboundEvent] - 前置验证失败: {}", validBefore.getMsg());
                return;
            }
            SaleOutbound beforeData = validBefore.getData();
            if (beforeData.getOutboundStatus() == SaleOutboundStatus.COMPLETED) {
                log.warn("[completeOutboundEvent] - 销售出库单已完成，跳过更新: {}", beforeData);
                return;
            }
            // 更新beforeData内明细的实收数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<SaleOutbound> validAfter = saleOutboundValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.error("[completeOutboundEvent] - 后置验证失败: {}", validBefore.getMsg());
                return;
            }
            // 更新销售出库单状态为已完成
            SaleOutbound afterData = validAfter.getData();
            afterData.setOutboundStatus(SaleOutboundStatus.COMPLETED);
            String remark = String.format(" [自动完成-仓库出库编码: %s-时间: %s]", outbound.getOutboundCode(), DateUtils.getTime());
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean complete = baseMapper.updateById(afterData) > 0;
            if (complete) {
                triggerNextStreamProcesses(afterData);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制，可后续增加重试或补偿机制
        }
    }

    /**
     * 更新销售出库单明细实发数量
     */
    private void updateActualQuantities(SaleOutbound saleOutbound) {
        try {
            List<Outbound> outbounds = outboundService.queryCompleteByDirectSourceId(saleOutbound.getOutboundId(), DirectSourceType.SALE_OUTBOUND);
            Map<Long, BigDecimal> finishQuantityMap = new HashMap<>();
            for (Outbound inbound : outbounds) {
                List<OutboundItem> outboundItems = outboundService.queryItemByOutboundId(inbound.getOutboundId());
                for (OutboundItem inboundItem : outboundItems) {
                    finishQuantityMap.merge(inboundItem.getDirectSourceItemId(), inboundItem.getFinishQuantity(), BigDecimal::add);
                }
            }
            List<SaleOutboundItem> updates = new ArrayList<>();
            for (SaleOutboundItem saleOutboundItem : saleOutbound.getItems()) {
                BigDecimal finishQuantity = finishQuantityMap.getOrDefault(saleOutboundItem.getItemId(), BigDecimal.ZERO);
                if (finishQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    saleOutboundItem.setFinishQuantity(finishQuantity);
                    updates.add(saleOutboundItem);
                }
            }
            if (!itemMapper.updateBatchById(updates)) {
                throw new ServiceException("批量更新销售出库单明细实发数量失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
            // 记录异常并抛给上层处理
        }
    }

    /**
     * 触发后续流程
     */
    private void triggerNextStreamProcesses(SaleOutbound saleOutbound) {
        try {
            // 触发销售出库完成事件
            SaleOutboundEvent event = new SaleOutboundEvent();
            event.setSaleOutbound(saleOutbound);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error("[triggerNextStreamProcesses] - createFinReceivable error: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 销售出库校验
     *
     * @param outboundId 出库单ID
     * @param isComplete 是否完成
     * @return 校验结果
     */
    private R<SaleOutbound> saleOutboundValid(Long outboundId, boolean isComplete) {
        if (outboundId == null) {
            throw new ServiceException("销售出库单ID不能为空");
        }
        SaleOutbound saleOutbound = baseMapper.selectById(outboundId);
        return saleOutboundValid(saleOutbound, isComplete);
    }

    private R<SaleOutbound> saleOutboundValid(SaleOutbound saleOutbound, boolean isComplete) {
        if (saleOutbound == null) {
            return R.fail("销售出库单不存在");
        }
        List<SaleOutboundItem> items = Optional.ofNullable(saleOutbound.getItems())
            .filter(list -> !list.isEmpty())
            .orElseGet(() -> itemMapper.queryByOutboundId(saleOutbound.getOutboundId()));

        if (items == null || items.isEmpty()) {
            return R.fail("出库单 [" + saleOutbound.getOutboundCode() + "] 没有明细项");
        }

        for (SaleOutboundItem item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("明细 [" + item.getProductName() + "] 应出库数量必须大于0");
            }
            if (isComplete) {
                if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("明细 [" + item.getProductName() + "] 实发数量必须大于0");
                }
                if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                    return R.fail("明细 [" + item.getProductName() + "] 实发数量与应出库量不符");
                }
            }
        }
        saleOutbound.setItems(items);
        return R.ok(saleOutbound);
    }

    /**
     * 数据填充
     *
     * @param bo 业务对象
     * @return 填充后的业务对象
     */
    private SaleOutboundBo fillRedundantFields(SaleOutboundBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setHandlerId(loginUser.getUserId());
            bo.setHandlerName(loginUser.getNickname());
        }
        return bo;
    }
}
