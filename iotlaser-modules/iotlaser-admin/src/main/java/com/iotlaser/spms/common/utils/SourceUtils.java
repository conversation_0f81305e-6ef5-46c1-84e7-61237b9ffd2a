package com.iotlaser.spms.common.utils;

import com.iotlaser.spms.common.domain.SourceInfo;

import static com.iotlaser.spms.common.constant.SourceConstant.SOURCE_DOCUMENT;

public class SourceUtils {

    public static boolean init_status = false;

    /**
     * 初始化源文档信息映射
     * 该方法用于在系统启动时初始化各种业务单据的源文档信息
     * 源文档信息包括销售订单、销售出库、销售退货入库、
     * 采购订单、采购入库、采购退货出库、
     * 生产发料出库、生产退货入库、生产入库、
     * 调拨出库和调拨入库等
     */
    public static void init() {
        // 销售相关单据
        SOURCE_DOCUMENT.put("sale_order", SourceInfo.builder().type("sale_order").main("erp_sale_order").id("order_id").code("order_code").time("order_date").status("order_status").item("erp_sale_order_item").taxFlag(true).build());
        SOURCE_DOCUMENT.put("sale_outbound", SourceInfo.builder().type("sale_outbound").main("erp_sale_outbound").id("outbound_id").code("outbound_code").time("outbound_time").status("outbound_status").item("erp_sale_outbound_item").itemInventoryIdFlag(true).locationFlag(true).costFlag(true).build());
        SOURCE_DOCUMENT.put("sale_return_inbound", SourceInfo.builder().type("sale_return_inbound").main("erp_sale_return").id("return_id").code("return_code").time("return_time").status("return_status").item("erp_sale_return_item").itemInventoryIdFlag(true).locationFlag(true).taxFlag(true).build());

        // 采购相关单据
        SOURCE_DOCUMENT.put("purchase_order", SourceInfo.builder().type("purchase_order").main("erp_purchase_order").id("order_id").code("order_code").time("order_date").status("order_status").item("erp_purchase_order_item").taxFlag(true).build());
        SOURCE_DOCUMENT.put("purchase_order_inbound", SourceInfo.builder().type("purchase_order_inbound").main("erp_purchase_order").id("order_id").code("order_code").time("order_date").status("order_status").item("erp_purchase_order_item").taxFlag(false).build());
        SOURCE_DOCUMENT.put("purchase_inbound", SourceInfo.builder().type("purchase_inbound").main("erp_purchase_inbound").id("inbound_id").code("inbound_code").time("inbound_time").status("inbound_status").item("erp_purchase_inbound_item").itemInventoryIdFlag(true).locationFlag(true).taxFlag(true).build());
        SOURCE_DOCUMENT.put("purchase_return_outbound", SourceInfo.builder().type("purchase_return_outbound").main("erp_purchase_return").id("return_id").code("return_code").time("return_time").status("return_status").item("erp_purchase_return_item").itemInventoryIdFlag(true).locationFlag(true).taxFlag(true).build());

        // 生产相关单据
        SOURCE_DOCUMENT.put("production_issue_outbound", SourceInfo.builder().type("production_issue_outbound").main("mes_production_issue").id("issue_id").code("issue_code").time("issue_time").status("issue_status").item("mes_production_issue_item").itemInventoryIdFlag(true).locationFlag(true).build());
        SOURCE_DOCUMENT.put("production_return_inbound", SourceInfo.builder().type("production_return_inbound").main("mes_production_return").id("return_id").code("return_code").time("return_time").status("return_status").item("mes_production_return_item").itemInventoryIdFlag(true).locationFlag(true).build());
        SOURCE_DOCUMENT.put("production_inbound", SourceInfo.builder().type("production_inbound").main("mes_production_inbound").id("inbound_id").code("inbound_code").time("inbound_time").status("inbound_status").item("mes_production_inbound_item").itemInventoryIdFlag(true).locationFlag(true).build());

        // 调拨相关单据
        SOURCE_DOCUMENT.put("transfer_outbound", SourceInfo.builder().type("transfer_outbound").main("wms_outbound").id("outbound_id").code("outbound_code").time("outbound_time").status("outbound_status").item("wms_outbound_item").batch("wms_outbound_item_batch").itemInventoryIdFlag(true).batchFlag(true).locationFlag(true).build());
        SOURCE_DOCUMENT.put("transfer", SourceInfo.builder().type("transfer").main("wms_transfer").id("transfer_id").code("transfer_code").time("transfer_time").status("transfer_status").item("wms_transfer_item").batch("wms_transfer_item_batch").itemInventoryIdFlag(true).batchFlag(true).locationFlag(true).build());

        init_status = true;
    }

    /**
     * 根据文档类型获取文档信息
     * 此方法用于从预定义的文档信息映射中检索特定类型的文档信息
     * 如果映射为空，则会先调用init()方法进行初始化
     *
     * @param sourceType 文档类型字符串，用作映射的键来获取对应的文档信息
     * @return SourceInfo对象，包含文档的具体信息如果找不到匹配的类型，则返回null
     */
    public static SourceInfo getInfo(String sourceType) {
        // 检查文档信息映射是否为空，为空则进行初始化
        if (!init_status) {
            init();
        }
        // 根据提供的文档类型返回对应的文档信息
        return SOURCE_DOCUMENT.get(sourceType);
    }

}
