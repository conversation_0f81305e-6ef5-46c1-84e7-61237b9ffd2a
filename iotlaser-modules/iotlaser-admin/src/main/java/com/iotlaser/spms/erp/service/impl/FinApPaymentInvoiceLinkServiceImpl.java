package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import com.iotlaser.spms.erp.mapper.FinApPaymentInvoiceLinkMapper;
import com.iotlaser.spms.erp.service.IFinApPaymentInvoiceLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 付款单与应付核销关系Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApPaymentInvoiceLinkServiceImpl implements IFinApPaymentInvoiceLinkService {

    private final FinApPaymentInvoiceLinkMapper baseMapper;

    /**
     * 查询付款单与发票核销关系
     *
     * @param linkId 主键
     * @return 付款单与发票核销关系
     */
    @Override
    public FinApPaymentInvoiceLinkVo queryById(Long linkId) {
        return baseMapper.selectVoById(linkId);
    }

    /**
     * 分页查询付款单与发票核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单与发票核销关系分页列表
     */
    @Override
    public TableDataInfo<FinApPaymentInvoiceLinkVo> queryPageList(FinApPaymentInvoiceLinkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = buildQueryWrapper(bo);
        Page<FinApPaymentInvoiceLinkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的付款单与发票核销关系列表
     *
     * @param bo 查询条件
     * @return 付款单与发票核销关系列表
     */
    @Override
    public List<FinApPaymentInvoiceLinkVo> queryList(FinApPaymentInvoiceLinkBo bo) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApPaymentInvoiceLink> buildQueryWrapper(FinApPaymentInvoiceLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApPaymentInvoiceLink::getLinkId);
        lqw.eq(bo.getPaymentId() != null, FinApPaymentInvoiceLink::getPaymentId, bo.getPaymentId());
        lqw.eq(bo.getInvoiceId() != null, FinApPaymentInvoiceLink::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getAppliedAmount() != null, FinApPaymentInvoiceLink::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getCancellationDate() != null, FinApPaymentInvoiceLink::getCancellationDate, bo.getCancellationDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApPaymentInvoiceLink::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinApPaymentInvoiceLinkBo bo) {
        FinApPaymentInvoiceLink add = MapstructUtils.convert(bo, FinApPaymentInvoiceLink.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLinkId(add.getLinkId());
        }
        return flag;
    }

    /**
     * 修改付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinApPaymentInvoiceLinkBo bo) {
        FinApPaymentInvoiceLink update = MapstructUtils.convert(bo, FinApPaymentInvoiceLink.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApPaymentInvoiceLink entity) {
        // 数据校验：检查唯一约束和必填字段

    }

    /**
     * 校验并批量删除付款单与发票核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验付款发票核销关系是否可以删除
            List<FinApPaymentInvoiceLink> links = baseMapper.selectByIds(ids);
            for (FinApPaymentInvoiceLink link : links) {
                // 检查核销状态，已确认的核销关系不能删除
                if ("CONFIRMED".equals(link.getStatus())) {
                    throw new ServiceException("付款发票核销关系【" + link.getLinkId() + "】已确认，不允许删除");
                }

                // 检查关联的付款单状态
                // TODO: 添加对付款单状态的检查
                // 如果付款单已审批，则不允许删除核销关系

                // 检查关联的发票状态
                // TODO: 添加对发票状态的检查
                // 如果发票已审批，则不允许删除核销关系

                log.info("删除付款发票核销关系校验通过：{}", link.getLinkId());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除付款发票核销关系成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除付款发票核销关系失败：{}", e.getMessage(), e);
            throw new ServiceException("删除付款发票核销关系失败：" + e.getMessage());
        }
    }

}
