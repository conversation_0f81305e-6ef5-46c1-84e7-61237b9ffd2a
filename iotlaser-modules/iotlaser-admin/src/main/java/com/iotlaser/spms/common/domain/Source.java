package com.iotlaser.spms.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class Source extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String type;
    private String code;
    private Date time;
    private String status;
    private String summary;
    private String remark;
    @TableLogic
    private String delFlag;
    @TableField(exist = false)
    private List<SourceItem> items;
}
