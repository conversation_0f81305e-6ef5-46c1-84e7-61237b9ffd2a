package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.event.InboundEvent;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import com.iotlaser.spms.mes.service.IProductionInboundService;
import com.iotlaser.spms.wms.domain.*;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.*;
import com.iotlaser.spms.wms.mapper.InboundItemBatchMapper;
import com.iotlaser.spms.wms.mapper.InboundItemMapper;
import com.iotlaser.spms.wms.mapper.InboundMapper;
import com.iotlaser.spms.wms.service.IInboundService;
import com.iotlaser.spms.wms.service.IInventoryService;
import com.iotlaser.spms.wms.service.ITransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_INBOUND_CODE;
import static org.dromara.common.core.constant.SystemConstants.YES;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 入库单服务实现
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InboundServiceImpl implements IInboundService {

    private final InboundMapper baseMapper;
    private final InboundItemMapper itemMapper;
    private final InboundItemBatchMapper batchMapper;
    private final Gen gen;
    private final IInventoryService inventoryService;

    // ERP 服务注入，用于状态回传
    @Autowired
    @Lazy
    private IPurchaseInboundService purchaseInboundService;
    @Autowired
    @Lazy
    private ISaleReturnService saleReturnService;
    @Autowired
    @Lazy
    private IProductionInboundService productionInboundService;
    @Autowired
    @Lazy
    private ITransferService transferService;

    /**
     * {@inheritDoc}
     */
    @Override
    public InboundVo queryById(Long inboundId) {
        return baseMapper.selectVoById(inboundId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<InboundVo> queryPageList(InboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Inbound> lqw = buildQueryWrapper(bo);
        Page<InboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InboundVo> queryList(InboundBo bo) {
        LambdaQueryWrapper<Inbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InboundVo> queryBySourceId(Long sourceId, SourceType sourceType) {
        if (sourceId == null || sourceType == null) {
            throw new ServiceException("源单ID和源单类型不能为空");
        }
        try {
            LambdaQueryWrapper<Inbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inbound::getSourceId, sourceId);
            wrapper.eq(Inbound::getSourceType, sourceType);
            wrapper.orderByDesc(Inbound::getCreateTime);
            List<InboundVo> result = baseMapper.selectVoList(wrapper);
            log.info("[queryBySourceId] - 查询成功: sourceId={}, sourceType={}, count={}", sourceId, sourceType, result.size());
            return result;
        } catch (Exception e) {
            log.error("[queryBySourceId] - 查询失败: sourceId={}, sourceType={}, error={}", sourceId, sourceType, e.getMessage(), e);
            throw new ServiceException("查询入库单失败");
        }
    }

    private LambdaQueryWrapper<Inbound> buildQueryWrapper(InboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Inbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(Inbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), Inbound::getInboundCode, bo.getInboundCode());
        lqw.eq(bo.getInboundStatus() != null, Inbound::getInboundStatus, bo.getInboundStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Inbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginInboundTime") != null && params.get("endInboundTime") != null,
            Inbound::getInboundTime, params.get("beginInboundTime"), params.get("endInboundTime"));
        return lqw;
    }

    /**
     * 新增仓库入库单
     *
     * @param bo 包含新仓库入库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundVo insertByBo(InboundBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getInboundCode())) {
                bo.setInboundCode(gen.code(WMS_INBOUND_CODE));
            }
            if (bo.getInboundStatus() == null) {
                bo.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            }
            if (bo.getInboundTime() == null) {
                bo.setInboundTime(LocalDateTime.now());
            }
            fillRedundantFields(bo);
            Inbound add = MapstructUtils.convert(bo, Inbound.class);
            validEntityBeforeSave(add);

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建入库单失败");
            }

            if (add.getSourceType() == null) {
                add.setSourceType(SourceType.INBOUND);
                add.setSourceId(add.getInboundId());
                add.setSourceCode(add.getInboundCode());
            }
            if (add.getDirectSourceType() == null) {
                add.setDirectSourceId(add.getInboundId());
                add.setDirectSourceCode(add.getInboundCode());
                add.setDirectSourceType(DirectSourceType.INVENTORY_INIT);
            }
            baseMapper.updateById(add);

            bo.setInboundId(add.getInboundId());
            log.info("[insertByBo] - 新增成功: {}", add.getInboundCode());
            return MapstructUtils.convert(add, InboundVo.class);
        } catch (Exception e) {
            log.error("[insertByBo] - 新增失败: {}", e.getMessage(), e);
            throw new ServiceException("新增入库单失败");
        }
    }

    /**
     * 修改仓库入库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InboundVo updateByBo(InboundBo bo) {
        try {
            fillRedundantFields(bo);
            Inbound update = MapstructUtils.convert(bo, Inbound.class);
            validEntityBeforeSave(update);
            if (baseMapper.updateById(update) <= 0) {
                throw new ServiceException("修改入库单失败，单据不存在或数据未变更");
            }
            log.info("[updateByBo] - 修改成功: {}", update.getInboundCode());
            return MapstructUtils.convert(update, InboundVo.class);
        } catch (Exception e) {
            log.error("[updateByBo] - 修改失败: {}", e.getMessage(), e);
            throw new ServiceException("修改入库单失败");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Inbound entity) {
        if (StringUtils.isNotBlank(entity.getInboundCode())) {
            LambdaQueryWrapper<Inbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inbound::getInboundCode, entity.getInboundCode());
            wrapper.ne(entity.getInboundId() != null, Inbound::getInboundId, entity.getInboundId());
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("入库单编码已存在: " + entity.getInboundCode());
            }
        }
    }

    /**
     * 校验并批量删除仓库入库单
     *
     * @param ids     待删除的仓库入库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        try {
            if (isValid) {
                List<Inbound> inbounds = baseMapper.selectByIds(ids);
                for (Inbound inbound : inbounds) {
                    if (inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT && inbound.getInboundStatus() != InboundStatus.PARTIALLY_RECEIVED) {
                        throw new ServiceException("入库单 [" + inbound.getInboundCode() + "] 状态为 [" + inbound.getInboundStatus() + "]，不允许删除");
                    }
                }
            }
            int itemResult = itemMapper.deleteByInboundIds(ids);
            log.info("[deleteWithValidByIds] - 删除明细: count={}", itemResult);
            int batchResult = batchMapper.deleteByInboundIds(ids);
            log.info("[deleteWithValidByIds] - 删除批次: count={}", batchResult);
            int result = baseMapper.deleteByIds(ids);
            log.info("[deleteWithValidByIds] - 删除主表: count={}", result);
            return result > 0;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - 删除失败: {}", e.getMessage(), e);
            throw new ServiceException("删除入库单失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InboundItem queryItemById(Long itemId) {
        return itemMapper.queryById(itemId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InboundItem> queryItemByInboundId(Long inboundId) {
        InboundItemBo bo = new InboundItemBo();
        bo.setInboundId(inboundId);
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InboundItem> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        InboundItemBo bo = new InboundItemBo();
        bo.setDirectSourceId(directSourceId);
        bo.setDirectSourceType(directSourceType);
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Inbound> queryCompleteByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return baseMapper.selectList(new LambdaQueryWrapper<Inbound>().eq(Inbound::getDirectSourceId, directSourceId).eq(Inbound::getDirectSourceType, directSourceType).eq(Inbound::getInboundStatus, InboundStatus.COMPLETED));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return baseMapper.existsByDirectSourceId(directSourceId, directSourceType);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InboundItem> queryItemList(InboundItemBo bo) {
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<InboundItemVo> queryItemPageList(InboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<InboundItemVo> result = MapstructUtils.convert(itemMapper.queryPageList(pageQuery.build(), queryWrapper), InboundItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<InboundItem> buildQueryWrapperWith(InboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<InboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getInboundId() != null, "item.inbound_id", bo.getInboundId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getSourceId() != null, "item.source_id", bo.getSourceId());
        if (bo.getSourceType() != null) {
            wrapper.eq("item.source_type", bo.getSourceType());
        }
        wrapper.eq(bo.getDirectSourceId() != null, "item.direct_source_id", bo.getDirectSourceId());
        if (bo.getDirectSourceType() != null) {
            wrapper.eq("item.direct_source_type", bo.getDirectSourceType());
        }
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(bo.getPriceExclusiveTax() != null, "item.price_exclusive_tax", bo.getPriceExclusiveTax());
        wrapper.eq(bo.getAmountExclusiveTax() != null, "item.amount_exclusive_tax", bo.getAmountExclusiveTax());
        wrapper.eq(bo.getAmount() != null, "item.amount", bo.getAmount());
        wrapper.eq(bo.getTaxRate() != null, "item.tax_rate", bo.getTaxRate());
        wrapper.eq(bo.getTaxAmount() != null, "item.tax_amount", bo.getTaxAmount());
        wrapper.eq(bo.getProductionTime() != null, "item.production_time", bo.getProductionTime());
        wrapper.eq(bo.getExpiryTime() != null, "item.expiry_time", bo.getExpiryTime());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 取消仓库入库单
     *
     * @param inboundId    待取消的仓库入库单ID
     * @param cancelReason 取消原因
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInbound(Long inboundId, String cancelReason) {
        try {
            Inbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("入库单不存在");
            }

            if (inbound.getInboundStatus() == InboundStatus.CANCELLED) {
                throw new ServiceException("入库单已取消，不能重复操作");
            }

            if (inbound.getInboundStatus() == InboundStatus.COMPLETED) {
                throw new ServiceException("入库单已完成，不能取消");
            }

            validateInboundForCancel(inbound);

            inbound.setInboundStatus(InboundStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(inbound.getRemark()) ? inbound.getRemark() : "";
                inbound.setRemark(currentRemark + " [取消原因: " + cancelReason + "，取消时间: " + DateUtils.getTime() + "]");
            }

            if (baseMapper.updateById(inbound) <= 0) {
                throw new ServiceException("取消入库单失败");
            }

            log.info("[cancelInbound] - 取消成功: inboundCode={}, reason={}", inbound.getInboundCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("[cancelInbound] - 取消失败: inboundId={}, error={}", inboundId, e.getMessage(), e);
            throw new ServiceException("取消入库单失败");
        }
    }

    /**
     * 完成仓库入库操作
     *
     * @param inboundId 待完成的仓库入库单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeInbound(Long inboundId) {
        try {
            Inbound inbound = baseMapper.selectById(inboundId);
            inbound.setItems(queryItemByInboundId(inboundId));
            validOnComplete(inbound);
            updateInventoryRecords(inbound);

            Inbound update = new Inbound();
            update.setInboundId(inboundId);
            update.setInboundStatus(InboundStatus.COMPLETED);
            String currentRemark = StringUtils.isNotBlank(inbound.getRemark()) ? inbound.getRemark() : "";
            update.setRemark(currentRemark + " [完成时间: " + DateUtils.getTime() + "]");
            if (baseMapper.updateById(update) <= 0) {
                throw new ServiceException("更新入库单状态失败");
            }
            notifyUpstreamSystemOnCompletion(inbound);
            log.info("[completeInbound] - 完成成功: inboundCode={}", inbound.getInboundCode());
            return true;
        } catch (Exception e) {
            log.error("[completeInbound] - 完成失败: inboundId={}, error={}", inboundId, e.getMessage(), e);
            throw new ServiceException("完成入库单失败");
        }
    }


    /**
     * 从采购订单创建仓库入库单
     *
     * @param purchaseOrder 已确认的采购订单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder) {
        try {
            if (PurchaseOrderStatus.CONFIRMED != purchaseOrder.getOrderStatus() &&
                PurchaseOrderStatus.PARTIALLY_RECEIVED != purchaseOrder.getOrderStatus()) {
                throw new ServiceException("创建失败：采购订单状态 [" + purchaseOrder.getOrderStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(purchaseOrder.getOrderId(), DirectSourceType.PURCHASE_ORDER);
            if (exists) {
                throw new ServiceException("创建失败：采购订单 [" + purchaseOrder.getOrderCode() + "] 已生成过仓库入库单，不能重复创建");
            }
            LoginUser loginUser = getLoginUser();
            Inbound add = new Inbound();
            add.setInboundCode(gen.code(WMS_INBOUND_CODE));

            add.setSourceId(purchaseOrder.getSourceId());
            add.setSourceCode(purchaseOrder.getOrderCode());
            add.setSourceType(purchaseOrder.getSourceType());
            add.setDirectSourceId(purchaseOrder.getOrderId());
            add.setDirectSourceCode(purchaseOrder.getOrderCode());
            add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);

            add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            add.setInboundTime(LocalDateTime.now());
            if (loginUser != null) {
                add.setOperatorId(loginUser.getUserId());
                add.setOperatorName(loginUser.getNickname());
            }
            add.setSummary("[采购订单" + purchaseOrder.getOrderCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<InboundItem> inboundItems = new ArrayList<>();
            for (PurchaseOrderItem purchaseOrderItem : purchaseOrder.getItems()) {
                InboundItem inboundItem = new InboundItem();

                inboundItem.setSourceId(purchaseOrder.getSourceId());
                inboundItem.setSourceCode(purchaseOrder.getSourceCode());
                inboundItem.setSourceType(SourceType.getByValue(purchaseOrder.getSourceType().getValue()));

                inboundItem.setDirectSourceId(purchaseOrder.getOrderId());
                inboundItem.setDirectSourceCode(purchaseOrder.getSourceCode());
                inboundItem.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
                inboundItem.setDirectSourceItemId(purchaseOrderItem.getItemId());

                inboundItem.setInboundId(add.getInboundId());
                inboundItem.setProductId(purchaseOrderItem.getProductId());
                inboundItem.setProductCode(purchaseOrderItem.getProductCode());
                inboundItem.setProductName(purchaseOrderItem.getProductName());
                inboundItem.setUnitId(purchaseOrderItem.getUnitId());
                inboundItem.setUnitCode(purchaseOrderItem.getUnitCode());
                inboundItem.setUnitName(purchaseOrderItem.getUnitName());
                inboundItem.setQuantity(purchaseOrderItem.getQuantity());
                inboundItem.setFinishQuantity(BigDecimal.ZERO);
                inboundItem.setPrice(purchaseOrderItem.getPrice());
                inboundItem.setPriceExclusiveTax(purchaseOrderItem.getPriceExclusiveTax());
                inboundItem.setAmount(purchaseOrderItem.getAmount());
                inboundItem.setAmountExclusiveTax(purchaseOrderItem.getAmountExclusiveTax());
                inboundItem.setTaxRate(purchaseOrderItem.getTaxRate());
                inboundItem.setTaxAmount(purchaseOrderItem.getTaxAmount());
                inboundItem.setRemark("[采购订单" + purchaseOrder.getOrderCode() + "]");
                inboundItems.add(inboundItem);
            }
            if (!itemMapper.insertBatch(inboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从采购入库单创建仓库入库单
     *
     * @param purchaseInbound 待入库的采购入库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound) {
        try {
            if (PurchaseInboundStatus.PENDING_WAREHOUSE != purchaseInbound.getInboundStatus()) {
                throw new ServiceException("创建失败：采购入库单状态 [" + purchaseInbound.getInboundStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(purchaseInbound.getInboundId(), DirectSourceType.PURCHASE_INBOUND);
            if (exists) {
                throw new ServiceException("创建失败：采购入库单 [" + purchaseInbound.getInboundCode() + "] 已生成过仓库入库单，不能重复创建");
            }
            LoginUser loginUser = getLoginUser();
            Inbound add = new Inbound();
            add.setInboundCode(gen.code(WMS_INBOUND_CODE));

            add.setSourceId(purchaseInbound.getSourceId());
            add.setSourceCode(purchaseInbound.getSourceCode());
            add.setSourceType(purchaseInbound.getSourceType());
            add.setDirectSourceId(purchaseInbound.getInboundId());
            add.setDirectSourceCode(purchaseInbound.getInboundCode());
            add.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);

            add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            add.setInboundTime(LocalDateTime.now());
            if (loginUser != null) {
                add.setOperatorId(loginUser.getUserId());
                add.setOperatorName(loginUser.getNickname());
            }
            add.setSummary("[采购入库单" + purchaseInbound.getInboundCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<InboundItem> inboundItems = new ArrayList<>();
            for (PurchaseInboundItem purchaseInboundItem : purchaseInbound.getItems()) {
                InboundItem item = new InboundItem();
                item.setInboundId(add.getInboundId());

                item.setSourceId(purchaseInbound.getSourceId());
                item.setSourceCode(purchaseInbound.getSourceCode());
                item.setSourceType(purchaseInbound.getSourceType());

                item.setDirectSourceId(purchaseInbound.getInboundId());
                item.setDirectSourceCode(purchaseInbound.getInboundCode());
                item.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);
                item.setDirectSourceItemId(purchaseInboundItem.getItemId());

                item.setProductId(purchaseInboundItem.getProductId());
                item.setProductCode(purchaseInboundItem.getProductCode());
                item.setProductName(purchaseInboundItem.getProductName());
                item.setUnitId(purchaseInboundItem.getUnitId());
                item.setUnitCode(purchaseInboundItem.getUnitCode());
                item.setUnitName(purchaseInboundItem.getUnitName());

                item.setLocationId(purchaseInboundItem.getLocationId());
                item.setLocationCode(purchaseInboundItem.getLocationCode());
                item.setLocationName(purchaseInboundItem.getLocationName());

                item.setQuantity(purchaseInboundItem.getQuantity());
                item.setFinishQuantity(purchaseInboundItem.getFinishQuantity());
                item.setPrice(purchaseInboundItem.getPrice());
                item.setPriceExclusiveTax(purchaseInboundItem.getPriceExclusiveTax());
                item.setAmount(purchaseInboundItem.getAmount());
                item.setAmountExclusiveTax(purchaseInboundItem.getAmountExclusiveTax());
                item.setTaxRate(purchaseInboundItem.getTaxRate());
                item.setTaxAmount(purchaseInboundItem.getTaxAmount());

                item.setExpiryTime(purchaseInboundItem.getExpiryTime());
                item.setProductionTime(purchaseInboundItem.getProductionTime());
                item.setRemark("[采购入库单" + purchaseInbound.getInboundCode() + "]");
                inboundItems.add(item);
            }
            if (!itemMapper.insertBatch(inboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从销售退货单创建仓库入库单
     *
     * @param saleReturn 已完成的销售退货单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromSaleReturn(SaleReturn saleReturn) {
        try {
            if (SaleReturnStatus.COMPLETED != saleReturn.getReturnStatus()) {
                throw new ServiceException("创建失败：销售退货单状态 [" + saleReturn.getReturnStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(saleReturn.getReturnId(), DirectSourceType.SALE_RETURN);
            if (exists) {
                throw new ServiceException("创建失败：销售退货单 [" + saleReturn.getReturnCode() + "] 已生成过仓库入库单，不能重复创建");
            }
            LoginUser loginUser = getLoginUser();
            Inbound add = new Inbound();
            add.setInboundCode(gen.code(WMS_INBOUND_CODE));

            add.setSourceId(saleReturn.getSourceId());
            add.setSourceCode(saleReturn.getSourceCode());
            add.setSourceType(saleReturn.getSourceType());
            add.setDirectSourceId(saleReturn.getReturnId());
            add.setDirectSourceCode(saleReturn.getReturnCode());
            add.setDirectSourceType(DirectSourceType.SALE_RETURN);

            add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            add.setInboundTime(LocalDateTime.now());
            if (loginUser != null) {
                add.setOperatorId(loginUser.getUserId());
                add.setOperatorName(loginUser.getNickname());
            }
            add.setSummary("[销售退货单" + saleReturn.getReturnCode() + "]");
            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<InboundItem> inboundItems = new ArrayList<>();
            for (SaleReturnItem saleReturnItem : saleReturn.getItems()) {
                InboundItem item = new InboundItem();
                item.setInboundId(add.getInboundId());

                item.setSourceId(saleReturn.getSourceId());
                item.setSourceCode(saleReturn.getSourceCode());
                item.setSourceType(saleReturn.getSourceType());

                item.setDirectSourceId(saleReturn.getReturnId());
                item.setDirectSourceCode(saleReturn.getReturnCode());
                item.setDirectSourceType(DirectSourceType.SALE_RETURN);
                item.setDirectSourceItemId(saleReturnItem.getItemId());

                item.setProductId(saleReturnItem.getProductId());
                item.setProductCode(saleReturnItem.getProductCode());
                item.setProductName(saleReturnItem.getProductName());
                item.setUnitId(saleReturnItem.getUnitId());
                item.setUnitCode(saleReturnItem.getUnitCode());
                item.setUnitName(saleReturnItem.getUnitName());

                item.setLocationId(saleReturnItem.getLocationId());
                item.setLocationCode(saleReturnItem.getLocationCode());
                item.setLocationName(saleReturnItem.getLocationName());

                item.setQuantity(saleReturnItem.getQuantity());
                item.setFinishQuantity(saleReturnItem.getFinishQuantity());
                item.setPrice(saleReturnItem.getPrice());
                item.setPriceExclusiveTax(saleReturnItem.getPriceExclusiveTax());
                item.setAmount(saleReturnItem.getAmount());
                item.setAmountExclusiveTax(saleReturnItem.getAmountExclusiveTax());
                item.setTaxRate(saleReturnItem.getTaxRate());
                item.setTaxAmount(saleReturnItem.getTaxAmount());
                item.setRemark("[销售退货单" + saleReturn.getReturnCode() + "]");

                inboundItems.add(item);
            }
            if (!itemMapper.insertBatch(inboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从仓库出库单创建仓库入库单（退货入库）
     *
     * @param outbound 已完成的仓库出库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromOutbound(Outbound outbound) {
        try {
            if (OutboundStatus.PICKING_IN_PROGRESS != outbound.getOutboundStatus() &&
                OutboundStatus.SHIPPED != outbound.getOutboundStatus() &&
                OutboundStatus.PICKED != outbound.getOutboundStatus() &&
                OutboundStatus.COMPLETED != outbound.getOutboundStatus()) {
                throw new ServiceException("创建失败：仓库出库单状态 [" + outbound.getOutboundStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(outbound.getOutboundId(), DirectSourceType.OUTBOUND);
            if (exists) {
                throw new ServiceException("创建失败：仓库出库单 [" + outbound.getOutboundCode() + "] 已生成过仓库入库单，不能重复创建");
            }

            LoginUser loginUser = getLoginUser();
            Inbound add = new Inbound();
            add.setInboundCode(gen.code(WMS_INBOUND_CODE));

            add.setSourceId(outbound.getSourceId());
            add.setSourceCode(outbound.getOutboundCode());
            add.setSourceType(outbound.getSourceType());
            add.setDirectSourceId(outbound.getOutboundId());
            add.setDirectSourceCode(outbound.getOutboundCode());
            add.setDirectSourceType(DirectSourceType.OUTBOUND);

            add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            add.setInboundTime(LocalDateTime.now());
            if (loginUser != null) {
                add.setOperatorId(loginUser.getUserId());
                add.setOperatorName(loginUser.getNickname());
            }
            add.setSummary("仓库出库单[" + outbound.getOutboundCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<InboundItem> inboundItems = new ArrayList<>();
            for (OutboundItem itemVo : outbound.getItems()) {
                InboundItem item = new InboundItem();
                item.setInboundId(add.getInboundId());

                item.setSourceId(itemVo.getSourceId());
                item.setSourceCode(itemVo.getSourceCode());
                item.setSourceType(itemVo.getSourceType());

                item.setDirectSourceId(outbound.getOutboundId());
                item.setDirectSourceCode(outbound.getOutboundCode());
                item.setDirectSourceType(DirectSourceType.OUTBOUND);
                item.setDirectSourceItemId(itemVo.getItemId());

                item.setProductId(itemVo.getProductId());
                item.setProductCode(itemVo.getProductCode());
                item.setProductName(itemVo.getProductName());
                item.setUnitId(itemVo.getUnitId());
                item.setUnitCode(itemVo.getUnitCode());
                item.setUnitName(itemVo.getUnitName());
                item.setQuantity(itemVo.getQuantity());
                item.setFinishQuantity(BigDecimal.ZERO);
                item.setPriceExclusiveTax(itemVo.getCostPrice());
                item.setRemark("由仓库出库单[" + outbound.getOutboundCode() + "]");

                if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                    List<InboundItemBatch> itemBatches = new ArrayList<>();
                    itemVo.getBatches().forEach(batchVo -> {
                        InboundItemBatch batch = new InboundItemBatch();
                        batch.setInboundId(add.getInboundId());
                        batch.setInternalBatchNumber(batchVo.getInternalBatchNumber());
                        batch.setSupplierBatchNumber(batchVo.getSupplierBatchNumber());
                        batch.setSerialNumber(batchVo.getSerialNumber());

                        batch.setProductId(batchVo.getProductId());
                        batch.setProductCode(batchVo.getProductCode());
                        batch.setProductName(batchVo.getProductName());
                        batch.setUnitId(batchVo.getUnitId());
                        batch.setUnitCode(batchVo.getUnitCode());
                        batch.setUnitName(batchVo.getUnitName());
                        batch.setLocationId(batchVo.getLocationId());
                        batch.setLocationCode(batchVo.getLocationCode());
                        batch.setLocationName(batchVo.getLocationName());

                        batch.setQuantity(batchVo.getQuantity());
                        batch.setPriceExclusiveTax(batchVo.getCostPrice());

                        batch.setProductionTime(batchVo.getProductionTime());
                        batch.setExpiryTime(batchVo.getExpiryTime());
                        batch.setRemark("由仓库出库单[" + outbound.getOutboundCode() + "]");

                        itemBatches.add(batch);
                    });
                    item.setBatches(itemBatches);
                }
                inboundItems.add(item);
            }
            if (!itemMapper.insertBatch(inboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }

            for (InboundItem item : inboundItems) {
                if (item.getBatches() != null && !item.getBatches().isEmpty()) {
                    item.getBatches().forEach(batch -> batch.setItemId(item.getItemId()));
                    if (!batchMapper.insertBatch(item.getBatches())) {
                        throw new ServiceException("创建批次记录失败");
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 校验入库单是否可以完成
     */
    private void validOnComplete(Inbound inbound) {
        if (inbound == null) {
            throw new ServiceException("入库单不存在");
        }
        try {
            if (inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT && inbound.getInboundStatus() != InboundStatus.PARTIALLY_RECEIVED) {
                throw new ServiceException("只有待收货和部分收货状态的单据才能完成");
            }
            if (inbound.getItems() == null || inbound.getItems().isEmpty()) {
                throw new ServiceException("入库单 [" + inbound.getInboundCode() + "] 缺少明细");
            }
            for (InboundItem item : inbound.getItems()) {
                if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("明细 [" + item.getProductName() + "] 数量必须大于0");
                }
                if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("明细 [" + item.getProductName() + "] 已完成数量必须大于0");
                }
                if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                    throw new ServiceException("明细 [" + item.getProductName() + "] 数量与已完成数量不一致");
                }
                if (item.getProduct().getBatchFlag().equals(YES)) {
                    if (item.getBatches().isEmpty()) {
                        throw new ServiceException("批次管理产品 [" + item.getProductName() + "] 缺少批次信息");
                    }
                    BigDecimal totalBatchQuantity = item.getBatches().stream().map(InboundItemBatch::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (totalBatchQuantity.compareTo(item.getFinishQuantity()) != 0) {
                        throw new ServiceException("产品 [" + item.getProductName() + "] 批次数量总和与明细数量不一致");
                    }
                    for (InboundItemBatch batch : item.getBatches()) {
                        if (batch.getQuantity() == null || batch.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ServiceException("批次 [" + batch.getInternalBatchNumber() + "] 数量必须大于0");
                        }
                        if (batch.getLocationId() == null) {
                            throw new ServiceException("批次 [" + batch.getInternalBatchNumber() + "] 未选择库位");
                        }
                    }
                } else {
                    if (item.getLocationId() == null) {
                        throw new ServiceException("产品 [" + item.getProductName() + "] 未选择库位");
                    }
                }
            }
            log.info("[validOnComplete] - 校验通过: inboundCode={}", inbound.getInboundCode());
        } catch (Exception e) {
            log.error("[validOnComplete] - 校验失败: inboundCode={}, error={}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("完成前校验失败: ");
        }
    }

    /**
     * 校验入库单是否可以取消
     */
    private void validateInboundForCancel(Inbound inbound) {
        // TODO: 检查是否已有库存变动，若有则禁止取消
        log.debug("[validateInboundForCancel] - 校验通过: inboundCode={}", inbound.getInboundCode());
    }

    /**
     * 更新库存记录
     */
    private void updateInventoryRecords(Inbound inbound) {
        try {
            for (InboundItem inboundItem : inbound.getItems()) {
                if (inboundItem.getProduct().getBatchFlag().equals(YES)) {
                    processInboundItemBatches(inbound, inboundItem);
                } else {
                    createInventoryFromItem(inbound, inboundItem);
                }
            }
            log.info("[updateInventoryRecords] - 更新成功: inboundCode={}, itemCount={}", inbound.getInboundCode(), inbound.getItems().size());
        } catch (Exception e) {
            log.error("[updateInventoryRecords] - 更新失败: inboundCode={}, error={}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("更新库存记录失败");
        }
    }

    /**
     * 从入库单明细创建库存
     */
    private void createInventoryFromItem(Inbound inbound, InboundItem inboundItem) {
        try {
            InventoryBo query = new InventoryBo();
            query.setProductId(inboundItem.getProductId());
            query.setLocationId(inboundItem.getLocationId());
            query.setInventoryStatus(InventoryStatus.AVAILABLE);

            List<InventoryVo> inventoryVos = inventoryService.queryList(query);

            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.NONE);

            inventoryBo.setSourceId(inbound.getSourceId() != null ? inbound.getSourceId() : inboundItem.getSourceId());
            inventoryBo.setSourceCode(inbound.getSourceCode() != null ? inbound.getSourceCode() : inboundItem.getSourceCode());
            inventoryBo.setSourceType(inbound.getSourceType() != null ? inbound.getSourceType() : inboundItem.getSourceType());

            inventoryBo.setDirectSourceId(inbound.getDirectSourceId() != null ? inbound.getDirectSourceId() : inboundItem.getDirectSourceId());
            inventoryBo.setDirectSourceCode(inbound.getDirectSourceCode() != null ? inbound.getDirectSourceCode() : inboundItem.getDirectSourceCode());
            inventoryBo.setDirectSourceType(inbound.getDirectSourceType() != null ? inbound.getDirectSourceType() : inboundItem.getDirectSourceType());
            inventoryBo.setDirectSourceItemId(inboundItem.getItemId());
            inventoryBo.setDirectSourceBatchId(0L);

            inventoryBo.setProductId(inboundItem.getProductId());
            inventoryBo.setProductCode(inboundItem.getProductCode());
            inventoryBo.setProductName(inboundItem.getProductName());
            inventoryBo.setUnitId(inboundItem.getUnitId());
            inventoryBo.setUnitCode(inboundItem.getUnitCode());
            inventoryBo.setUnitName(inboundItem.getUnitName());
            inventoryBo.setLocationId(inboundItem.getLocationId());
            inventoryBo.setLocationCode(inboundItem.getLocationCode());
            inventoryBo.setLocationName(inboundItem.getLocationName());

            inventoryBo.setQuantity(inboundItem.getFinishQuantity());
            inventoryBo.setCostPrice(inboundItem.getPriceExclusiveTax());

            inventoryBo.setExpiryTime(inboundItem.getExpiryTime());
            inventoryBo.setInventoryTime(inbound.getInboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE);
            inventoryBo.setRemark("入库单明细: " + inbound.getInboundCode() + "-" + inboundItem.getProductName());

            if (inventoryVos != null && !inventoryVos.isEmpty()) {
                inventoryBo.setInventoryId(inventoryVos.getFirst().getInventoryId());
                if (!inventoryService.updateByBo(inventoryBo)) {
                    throw new ServiceException("更新库存失败");
                }
            } else {
                if (!inventoryService.insertByBo(inventoryBo)) {
                    throw new ServiceException("创建库存失败");
                }
            }
        } catch (Exception e) {
            log.error("[createInventoryFromItem] - 操作失败: productCode={}, error={}", inboundItem.getProductCode(), e.getMessage(), e);
            throw new ServiceException("从明细创建库存失败");
        }
    }

    /**
     * 处理入库单明细的批次
     */
    private void processInboundItemBatches(Inbound inbound, InboundItem inboundItem) {
        try {
            for (InboundItemBatch itemBatch : inboundItem.getBatches()) {
                createInventoryFromItemBatch(inbound, inboundItem, itemBatch);
            }
        } catch (Exception e) {
            log.error("[processInboundItemBatches] - 处理失败: itemId={}, error={}", inboundItem.getItemId(), e.getMessage(), e);
            throw new ServiceException("处理入库单批次失败");
        }
    }

    /**
     * 从入库单批次创建库存
     */
    private void createInventoryFromItemBatch(Inbound inbound, InboundItem inboundItem, InboundItemBatch itemBatch) {
        try {
            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.BATCH);
            inventoryBo.setInternalBatchNumber(itemBatch.getInternalBatchNumber());
            inventoryBo.setSupplierBatchNumber(itemBatch.getSupplierBatchNumber());
            inventoryBo.setSerialNumber(itemBatch.getSerialNumber());

            inventoryBo.setSourceId(inboundItem.getSourceId());
            inventoryBo.setSourceCode(inboundItem.getSourceCode());
            inventoryBo.setSourceType(inboundItem.getSourceType());

            inventoryBo.setDirectSourceId(inboundItem.getDirectSourceId());
            inventoryBo.setDirectSourceCode(inboundItem.getDirectSourceCode());
            inventoryBo.setDirectSourceType(inboundItem.getDirectSourceType());
            inventoryBo.setDirectSourceItemId(inboundItem.getItemId());
            inventoryBo.setDirectSourceBatchId(itemBatch.getBatchId());

            inventoryBo.setProductId(inboundItem.getProductId());
            inventoryBo.setProductCode(inboundItem.getProductCode());
            inventoryBo.setProductName(inboundItem.getProductName());
            inventoryBo.setUnitId(inboundItem.getUnitId());
            inventoryBo.setUnitCode(inboundItem.getUnitCode());
            inventoryBo.setUnitName(inboundItem.getUnitName());
            inventoryBo.setLocationId(itemBatch.getLocationId());
            inventoryBo.setLocationCode(itemBatch.getLocationCode());
            inventoryBo.setLocationName(itemBatch.getLocationName());

            inventoryBo.setQuantity(itemBatch.getQuantity());
            inventoryBo.setCostPrice(itemBatch.getPriceExclusiveTax());

            inventoryBo.setExpiryTime(itemBatch.getExpiryTime());
            inventoryBo.setInventoryTime(inbound.getInboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE);

            inventoryBo.setRemark("入库单批次: " + inbound.getInboundCode() + "-" + itemBatch.getProductName());

            if (!inventoryService.insertByBo(inventoryBo)) {
                throw new ServiceException("创建库存失败");
            }
        } catch (Exception e) {
            log.error("[createInventoryFromItemBatch] - 创建失败: productCode={}, error={}", inboundItem.getProductCode(), e.getMessage(), e);
            throw new ServiceException("从批次创建库存失败");
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(InboundBo bo) {
        // TODO: 根据业务需求填充冗余字段，如操作员信息等
    }

    /**
     * 入库完成事件通知
     */
    private void notifyUpstreamSystemOnCompletion(Inbound inbound) {
        try {
            InboundEvent event = new InboundEvent();
            event.setInbound(inbound);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error("[notifyUpstream] - 通知失败: inboundCode={}, error={}", inbound.getInboundCode(), e.getMessage(), e);
        }
    }


    /**
     * 数据一致性检查
     *
     * @param inboundId 入库单ID
     * @return 检查报告
     */
    public String checkDataConsistency(Long inboundId) {
        StringBuilder report = new StringBuilder("=== WMS-ERP 数据一致性检查报告 ===\n");
        try {
            Inbound wmsInbound = baseMapper.selectById(inboundId);
            if (wmsInbound == null) {
                return report.append("❌ 仓库入库单不存在: ").append(inboundId).toString();
            }
            report.append("🔍 检查入库单: ").append(wmsInbound.getInboundCode()).append("\n");

            if (wmsInbound.getDirectSourceType() == DirectSourceType.PURCHASE_INBOUND && wmsInbound.getDirectSourceId() != null) {
                try {
                    PurchaseInboundVo erpInbound = purchaseInboundService.queryById(wmsInbound.getDirectSourceId());
                    if (erpInbound != null) {
                        report.append("✅ 采购入库单存在: ").append(erpInbound.getInboundCode()).append("\n");
                        boolean statusConsistent = checkStatusConsistency(wmsInbound.getInboundStatus(), erpInbound.getInboundStatus());
                        report.append(statusConsistent ? "✅" : "❌").append(" 状态一致性: WMS=").append(wmsInbound.getInboundStatus()).append(", ERP=").append(erpInbound.getInboundStatus()).append("\n");
                        checkQuantityConsistency(wmsInbound, erpInbound, report);
                    } else {
                        report.append("❌ 采购入库单不存在: ").append(wmsInbound.getDirectSourceId()).append("\n");
                    }
                } catch (Exception e) {
                    report.append("❌ 查询采购入库单异常: ").append(e.getMessage()).append("\n");
                }
            }
            return report.append("=== 检查完成 ===\n").toString();
        } catch (Exception e) {
            log.error("[checkDataConsistency] - 检查异常: inboundId={}, error={}", inboundId, e.getMessage(), e);
            return report.append("❌ 数据一致性检查异常: ").append(e.getMessage()).toString();
        }
    }

    /**
     * 检查状态一致性
     */
    private boolean checkStatusConsistency(InboundStatus wmsStatus, PurchaseInboundStatus erpStatus) {
        if (wmsStatus == InboundStatus.COMPLETED && erpStatus == PurchaseInboundStatus.COMPLETED) {
            return true;
        }
        return wmsStatus == InboundStatus.PARTIALLY_RECEIVED && erpStatus == PurchaseInboundStatus.PENDING_WAREHOUSE;
    }

    /**
     * 检查数量一致性
     */
    private void checkQuantityConsistency(Inbound wmsInbound, PurchaseInboundVo erpInbound, StringBuilder report) {
        try {
            // TODO: 实现明细级别的数量一致性检查
            report.append("ℹ️ 数量一致性检查（待实现）\n");
        } catch (Exception e) {
            report.append("❌ 数量一致性检查异常: ").append(e.getMessage()).append("\n");
        }
    }

}
