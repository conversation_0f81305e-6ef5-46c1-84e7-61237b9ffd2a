package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 应收单（发票）Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinArReceivableService {

    /**
     * 查询应收单
     *
     * @param receivableId 主键
     * @return 应收单
     */
    FinArReceivableVo queryById(Long receivableId);

    /**
     * 分页查询应收单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收单分页列表
     */
    TableDataInfo<FinArReceivableVo> queryPageList(FinArReceivableBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应收单列表
     *
     * @param bo 查询条件
     * @return 应收单列表
     */
    List<FinArReceivableVo> queryList(FinArReceivableBo bo);

    /**
     * 新增应收单
     *
     * @param bo 应收单
     * @return 是否新增成功
     */
    FinArReceivableVo insertByBo(FinArReceivableBo bo);

    /**
     * 修改应收单
     *
     * @param bo 应收单
     * @return 是否修改成功
     */
    FinArReceivableVo updateByBo(FinArReceivableBo bo);

    /**
     * 校验并批量删除应收单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据上游ID查询是否存在应收单
     *
     * @param directSourceId 上游单据ID
     * @return 是否存在
     */
    Boolean existsByDirectSourceId(Long directSourceId);

    /**
     * 根据源头ID查询应收单
     *
     * @param sourceId 源头单据ID
     * @return 应收单列表
     */
    List<FinArReceivableVo> queryBySourceId(Long sourceId);

    /**
     * 根据上游ID查询应收单
     *
     * @param directSourceId 上游单据ID
     * @return 应收单列表
     */
    List<FinArReceivableVo> queryByDirectSourceId(Long directSourceId);

    /**
     * 更新应收账款状态
     *
     * @param receivableId 应收账款ID
     * @param newStatus    新状态
     * @return 是否更新成功
     */
    Boolean updateReceivableStatus(Long receivableId, String newStatus);

    /**
     * 从应收单生成收款单
     *
     * @param receivableId  应收单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 收款单ID
     */
    Long generateReceiptOrderFromReceivable(Long receivableId, BigDecimal receiptAmount,
                                            Long accountId, Long operatorId, String operatorName);

    /**
     * 确认应收单
     *
     * @param receivableId 应收单ID
     * @return 是否确认成功
     */
    Boolean confirmReceivable(Long receivableId);

    /**
     * 取消应收单
     *
     * @param receivableId 应收单ID
     * @param reason       取消原因
     * @return 是否取消成功
     */
    Boolean cancelReceivable(Long receivableId, String reason);

    /**
     * 从销售订单生成应收单
     *
     * @param saleOrder 销售订单Vo
     * @return 应收单ID
     */
    Boolean createFromSaleOrder(SaleOrder saleOrder);

    /**
     * 更新已使用的金额和状态
     *
     * @param receivableId 应收单ID
     * @return 是否更新成功
     */
    Boolean updateAppliedAmountAndStatus(Long receivableId);

    /**
     * 从销售出库单生成应收账款
     *
     * @param saleOutbound 出库单Vo
     * @return 应收账款ID
     */
    Boolean createFromSaleOutbound(SaleOutbound saleOutbound);

}
