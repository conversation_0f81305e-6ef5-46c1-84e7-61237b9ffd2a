package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchaseOrderType implements IDictEnum<String> {

    SALE_ORDER("sale_order", "销售订单", "基于销售订单的采购需求"),
    PRODUCTION_ORDER("production_order", "生产订单", "基于生产订单的采购需求"),
    PURCHASE_ORDER("purchase_order", "其他采购", "其他类型的采购需求");

    public final static String DICT_CODE = "erp_purchase_order_type";
    public final static String DICT_NAME = "采购订单类型";
    public final static String DICT_DESC = "定义采购订单的来源类型，包括基于销售订单、生产订单或其他需求的采购";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 采购订单类型枚举
     */
    public static PurchaseOrderType getByValue(String value) {
        for (PurchaseOrderType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
