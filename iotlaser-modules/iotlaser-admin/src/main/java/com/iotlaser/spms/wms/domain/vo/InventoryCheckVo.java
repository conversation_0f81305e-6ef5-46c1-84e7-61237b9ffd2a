package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.InventoryCheck;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 库存盘点视图对象 wms_inventory_check
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryCheck.class)
public class InventoryCheckVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点ID
     */
    @ExcelProperty(value = "盘点ID")
    private Long checkId;

    /**
     * 盘点编码
     */
    @ExcelProperty(value = "盘点编码")
    private String checkCode;

    /**
     * 盘点名称
     */
    @ExcelProperty(value = "盘点名称")
    private String checkName;

    /**
     * 盘点类型
     */
    @ExcelProperty(value = "盘点类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_check_type")
    private String checkType;

    /**
     * 盘点范围
     */
    @ExcelProperty(value = "盘点范围")
    private String checkScope;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private LocalDateTime actualEndTime;

    /**
     * 盘点状态
     */
    @ExcelProperty(value = "盘点状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_check_status")
    private String checkStatus;

    /**
     * 盘点负责人 ID
     */
    @ExcelProperty(value = "盘点负责人 ID")
    private Long supervisorId;

    /**
     * 盘点负责人
     */
    @ExcelProperty(value = "盘点负责人")
    private String supervisorName;

    /**
     * 参与盘点人员IDS
     */
    @ExcelProperty(value = "参与盘点人员IDS")
    private String operatorIds;

    /**
     * 参与盘点人员
     */
    @ExcelProperty(value = "参与盘点人员")
    private String operatorNames;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
