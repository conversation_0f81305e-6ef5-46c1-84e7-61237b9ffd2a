package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;

/**
 * 付款单与发票核销关系Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface FinApPaymentInvoiceLinkMapper extends BaseMapperPlus<FinApPaymentInvoiceLink, FinApPaymentInvoiceLinkVo> {


    /**
     * 获取发票已核销金额
     */
    default BigDecimal getAppliedAmountByInvoiceId(Long invoiceId) {
        return selectList(new LambdaQueryWrapper<FinApPaymentInvoiceLink>().select(FinApPaymentInvoiceLink::getAppliedAmount).eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId)).stream().map(FinApPaymentInvoiceLink::getAppliedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取付款单已核销金额
     */
    default BigDecimal getAppliedAmountByPaymentIdId(Long paymentId) {
        return selectList(new LambdaQueryWrapper<FinApPaymentInvoiceLink>().select(FinApPaymentInvoiceLink::getAppliedAmount).eq(FinApPaymentInvoiceLink::getPaymentId, paymentId)).stream().map(FinApPaymentInvoiceLink::getAppliedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    default Boolean existsByInvoiceId(Long invoiceId) {
        return exists(new LambdaQueryWrapper<FinApPaymentInvoiceLink>().eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId));
    }

    default Boolean existsByPaymentId(Long paymentId) {
        return exists(new LambdaQueryWrapper<FinApPaymentInvoiceLink>().eq(FinApPaymentInvoiceLink::getPaymentId, paymentId));
    }
}
