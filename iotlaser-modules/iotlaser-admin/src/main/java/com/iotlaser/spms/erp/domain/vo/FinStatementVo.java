package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinStatement;
import com.iotlaser.spms.erp.enums.FinStatementStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 对账单视图对象 erp_fin_statement
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinStatement.class)
public class FinStatementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 对账ID
     */
    @ExcelProperty(value = "对账ID")
    private Long statementId;

    /**
     * 对账编码
     */
    @ExcelProperty(value = "对账编码")
    private String statementCode;

    /**
     * 对账名称
     */
    @ExcelProperty(value = "对账名称")
    private String statementName;

    /**
     * 往来单位ID
     */
    @ExcelProperty(value = "往来单位ID")
    private Long partnerId;

    /**
     * 往来单位名称
     */
    @ExcelProperty(value = "往来单位名称")
    private String partnerName;

    /**
     * 开始日期
     */
    @ExcelProperty(value = "开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期")
    private LocalDate endDate;

    /**
     * 此前余额
     */
    @ExcelProperty(value = "此前余额")
    private BigDecimal openingBalance;

    /**
     * 期末余额 (应收余额)
     */
    @ExcelProperty(value = "期末余额 (应收余额)")
    private BigDecimal closingBalance;

    /**
     * 对账状态
     */
    @ExcelProperty(value = "对账状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_statement_status")
    private FinStatementStatus statementStatus;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
