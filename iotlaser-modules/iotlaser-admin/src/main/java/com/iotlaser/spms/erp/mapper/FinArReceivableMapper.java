package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.FinArReceivable;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 应收单Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface FinArReceivableMapper extends BaseMapperPlus<FinArReceivable, FinArReceivableVo> {
    default List<FinArReceivableVo> queryBySourceId(Long sourceId) {
        return selectVoList(new LambdaQueryWrapper<FinArReceivable>().eq(FinArReceivable::getSourceId, sourceId));
    }

    default List<FinArReceivableVo> queryByDirectSourceId(Long directSourceId) {
        return selectVoList(new LambdaQueryWrapper<FinArReceivable>().eq(FinArReceivable::getDirectSourceId, directSourceId));
    }

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<FinArReceivable>().eq(FinArReceivable::getDirectSourceId, directSourceId));
    }
}
