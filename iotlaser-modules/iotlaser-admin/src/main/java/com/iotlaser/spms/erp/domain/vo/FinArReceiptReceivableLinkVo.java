package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 收款单与应收单核销关系视图对象 erp_fin_ar_receipt_receivable_link
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinArReceiptReceivableLink.class)
public class FinArReceiptReceivableLinkVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @ExcelProperty(value = "关系ID")
    private Long linkId;

    /**
     * 收款ID
     */
    @ExcelProperty(value = "收款ID")
    private Long receiptId;

    /**
     * 应收ID
     */
    @ExcelProperty(value = "应收ID")
    private Long receivableId;

    /**
     * 核销金额
     */
    @ExcelProperty(value = "核销金额")
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    @ExcelProperty(value = "核销日期")
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
