package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.event.PurchaseInboundEvent;
import com.iotlaser.spms.erp.event.PurchaseReturnEvent;
import com.iotlaser.spms.erp.mapper.PurchaseOrderItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseOrderMapper;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.wms.service.IInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_ORDER_CODE;

/**
 * 采购订单服务实现
 *
 * <AUTHOR> Kai
 * @version 1.2
 * @since 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderServiceImpl implements IPurchaseOrderService {

    private final PurchaseOrderMapper baseMapper;
    private final PurchaseOrderItemMapper itemMapper;
    private final IPurchaseInboundService purchaseInboundService;
    private final IPurchaseReturnService purchaseReturnService;
    private final IInboundService inboundService;
    private final ICompanyService companyService;
    private final Gen gen;

    /**
     * 根据ID查询采购订单
     *
     * @param orderId 采购订单的唯一主键ID
     * @return 采购订单的详细视图对象 (VO)，若不存在则返回 {@code null}
     */
    @Override
    public PurchaseOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * 分页查询采购订单列表
     *
     * @param bo        查询条件业务对象 (BO)
     * @param pageQuery 分页参数
     * @return 封装了分页结果的 TableDataInfo 对象
     */
    @Override
    public TableDataInfo<PurchaseOrderVo> queryPageList(PurchaseOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseOrder> lqw = buildQueryWrapper(bo);
        Page<PurchaseOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购订单列表
     *
     * @param bo 查询条件业务对象 (BO)
     * @return 采购订单视图对象 (VO) 的列表
     */
    @Override
    public List<PurchaseOrderVo> queryList(PurchaseOrderBo bo) {
        LambdaQueryWrapper<PurchaseOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseOrder> buildQueryWrapper(PurchaseOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(PurchaseOrder::getOrderId);

        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), PurchaseOrder::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getSourceId() != null, PurchaseOrder::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseOrder::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseOrder::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseOrder::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseOrder::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseOrder::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseOrder::getSupplierId, bo.getSupplierId());
        lqw.eq(bo.getOrderDate() != null, PurchaseOrder::getOrderDate, bo.getOrderDate());
        if (bo.getOrderStatus() != null) {
            lqw.eq(PurchaseOrder::getOrderStatus, bo.getOrderStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseOrder::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseOrder::getSupplierName, bo.getSupplierName());
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            PurchaseOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * 新增采购订单
     *
     * @param bo 包含新采购订单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderVo insertByBo(PurchaseOrderBo bo) {
        try {
            log.info("开始新增采购订单，操作人: {}", LoginHelper.getUsername());

            // 自动生成订单编码
            if (StringUtils.isEmpty(bo.getOrderCode())) {
                bo.setOrderCode(gen.code(ERP_PURCHASE_ORDER_CODE));
            }
            // 设置初始状态为草稿
            if (bo.getOrderStatus() == null) {
                bo.setOrderStatus(PurchaseOrderStatus.DRAFT);
            }
            // 设置订单日期为当前日期
            if (bo.getOrderDate() == null) {
                bo.setOrderDate(LocalDate.now());
            }

            fillRedundantFields(bo);
            PurchaseOrder add = MapstructUtils.convert(bo, PurchaseOrder.class);
            validEntityBeforeSave(add);

            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                throw new ServiceException("新增采购订单失败");
            }

            // 设置来源信息（自引用）
            if (add.getSourceType() == null || add.getSourceType() == SourceType.PURCHASE_ORDER) {
                add.setSourceId(add.getOrderId());
                add.setSourceCode(add.getOrderCode());
                add.setSourceType(SourceType.PURCHASE_ORDER);
            }
            if (add.getDirectSourceType() == null || add.getDirectSourceType() == DirectSourceType.PURCHASE_ORDER) {
                add.setDirectSourceId(add.getOrderId());
                add.setDirectSourceCode(add.getOrderCode());
                add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
            }
            baseMapper.updateById(add);

            log.info("新增采购订单成功，订单ID: {}, 订单编码: {}", add.getOrderId(), add.getOrderCode());
            return MapstructUtils.convert(add, PurchaseOrderVo.class);
        } catch (Exception e) {
            log.error("新增采购订单失败: {}", e.getMessage(), e);
            throw new ServiceException("新增采购订单失败: " + e.getMessage());
        }
    }

    /**
     * 修改采购订单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderVo updateByBo(PurchaseOrderBo bo) {
        try {
            log.info("开始修改采购订单，订单ID: {}, 操作人: {}", bo.getOrderId(), LoginHelper.getUsername());

            fillRedundantFields(bo);
            PurchaseOrder update = MapstructUtils.convert(bo, PurchaseOrder.class);
            validEntityBeforeSave(update);

            // 重新计算订单总金额
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getOrderId());
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());

            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                throw new ServiceException("修改采购订单失败");
            }

            log.info("修改采购订单成功，订单ID: {}", update.getOrderId());
            return MapstructUtils.convert(update, PurchaseOrderVo.class);
        } catch (Exception e) {
            log.error("修改采购订单失败: {}", e.getMessage(), e);
            throw new ServiceException("修改采购订单失败: " + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseOrder entity) {
        if (StringUtils.isNotBlank(entity.getOrderCode())) {
            LambdaQueryWrapper<PurchaseOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrder::getOrderCode, entity.getOrderCode());
            if (entity.getOrderId() != null) {
                wrapper.ne(PurchaseOrder::getOrderId, entity.getOrderId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("采购订单编码 [" + entity.getOrderCode() + "] 已存在");
            }
        }
    }

    /**
     * 校验并批量删除采购订单
     *
     * @param ids     待删除的采购订单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("[deleteWithValidByIds] - 开始. ids: {}, isValid: {}", ids, isValid);
        if (isValid) {
            List<PurchaseOrder> orders = baseMapper.selectByIds(ids);
            for (PurchaseOrder order : orders) {
                if (PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("订单 [" + order.getOrderCode() + "] 状态为“" + order.getOrderStatus().getDesc() + "”，仅草稿状态可删除");
                }
                if (purchaseInboundService.existsByDirectSourceId(order.getOrderId())) {
                    throw new ServiceException("订单 [" + order.getOrderCode() + "] 已关联采购入库单，不允许删除");
                }
            }
        }
        itemMapper.deleteByOrderIds(ids);
        boolean result = baseMapper.deleteByIds(ids) > 0;
        log.info("[deleteWithValidByIds] - 成功. 影响行数: {}", result ? ids.size() : 0);
        return result;
    }

    /**
     * 确认采购订单
     *
     * @param orderId 待确认的采购订单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        try {
            log.info("[confirmOrder] - 开始. 采购订单ID: {}", orderId);
            R<PurchaseOrder> validPurchaseOrder = purchaseOrderValid(orderId, false);
            if (R.isError(validPurchaseOrder)) {
                throw new ServiceException(validPurchaseOrder.getMsg());
            }
            PurchaseOrder purchaseOrder = validPurchaseOrder.getData();
            if (PurchaseOrderStatus.DRAFT != purchaseOrder.getOrderStatus()) {
                throw new ServiceException("采购订单 [" + purchaseOrder.getOrderCode() + "] 状态为“" + purchaseOrder.getOrderStatus().getDesc() + "”，仅草稿状态可确认");
            }

            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(orderId);
            purchaseOrder.setAmount(calculated.getAmount());
            purchaseOrder.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            purchaseOrder.setTaxAmount(calculated.getTaxAmount());
            purchaseOrder.setOrderStatus(PurchaseOrderStatus.CONFIRMED);
            boolean confirmed = baseMapper.updateById(purchaseOrder) > 0;
            if (confirmed) {
                log.info("[confirmOrder] - 采购订单【{}】确认成功", purchaseOrder.getOrderCode());
                createPurchaseInboundFromConfirm(purchaseOrder);
            }
            return confirmed;
        } catch (Exception e) {
            log.error("[confirmOrder] - 确认采购订单异常: ", e);
            throw new ServiceException("确认采购订单失败");
        }
    }

    /**
     * 校验采购订单是否满足条件
     */
    private R<PurchaseOrder> purchaseOrderValid(Long orderId, boolean isComplete) {
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            return R.fail("校验失败：ID为【" + orderId + "】的采购订单不存在");
        }
        return purchaseOrderValid(order, isComplete);
    }

    /**
     * 校验采购订单是否满足条件
     */
    private R<PurchaseOrder> purchaseOrderValid(PurchaseOrder order, boolean isComplete) {
        if (order == null) {
            return R.fail("校验失败：采购订单不存在");
        }
        List<PurchaseOrderItem> items = itemMapper.queryByOrderId(order.getOrderId());
        if (items.isEmpty()) {
            return R.fail("校验失败：订单【" + order.getOrderCode() + "】没有明细项，无法进行下一步操作");
        }
        for (PurchaseOrderItem item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("校验失败：订单【" + order.getOrderCode() + "】中的产品【" + item.getProductName() + "】采购数量必须大于0");
            }
            if (isComplete) {
                if (item.getPrice() == null || item.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("校验失败：订单【" + order.getOrderCode() + "】中的产品【" + item.getProductName() + "】单价必须大于0");
                }
                if (item.getAmount() == null || item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("校验失败：订单【" + order.getOrderCode() + "】中的产品【" + item.getProductName() + "】金额必须大于0");
                }
                if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("校验失败：订单【" + order.getOrderCode() + "】中的产品【" + item.getProductName() + "】实收数量必须大于0");
                }
                if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                    return R.fail("校验失败：订单【" + order.getOrderCode() + "】中的产品【" + item.getProductName() + "】实收数量不能小于或大于应入库数量");
                }
            }
        }
        order.setItems(items);
        return R.ok(order);
    }

    /**
     * 取消采购订单
     *
     * @param orderId 待取消的采购订单ID
     * @param reason  取消原因
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, String reason) {
        log.info("[cancelOrder] - 开始. orderId: {}, reason: {}", orderId, reason);
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单 [" + orderId + "] 不存在");
        }

        if (PurchaseOrderStatus.DRAFT != order.getOrderStatus()
            && PurchaseOrderStatus.CONFIRMED != order.getOrderStatus()) {
            throw new ServiceException("订单 [" + order.getOrderCode() + "] 状态为“" + order.getOrderStatus().getDesc() + "”，无法取消");
        }

        if (PurchaseOrderStatus.CONFIRMED == order.getOrderStatus()) {
            List<PurchaseOrderItem> items = itemMapper.queryByOrderId(orderId);
            for (PurchaseOrderItem item : items) {
                if (item.getFinishQuantity() != null && item.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("订单 [" + order.getOrderCode() + "] 已存在收货记录，无法取消");
                }
            }
        }

        PurchaseOrder update = new PurchaseOrder();
        update.setOrderId(orderId);
        update.setOrderStatus(PurchaseOrderStatus.CANCELLED);
        if (StringUtils.isNotBlank(reason)) {
            String newRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() + " | " : "";
            newRemark += "取消原因: " + reason;
            update.setRemark(newRemark);
        }

        boolean result = baseMapper.updateById(update) > 0;
        log.info("[cancelOrder] - 成功. orderId: {}", orderId);
        return result;
    }

    /**
     * 关闭采购订单
     *
     * @param orderId 待关闭的采购订单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrder(Long orderId) {
        log.info("[closeOrder] - 开始. orderId: {}", orderId);
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单 [" + orderId + "] 不存在");
        }

        if (PurchaseOrderStatus.FULLY_RECEIVED != order.getOrderStatus()
            && PurchaseOrderStatus.PARTIALLY_RECEIVED != order.getOrderStatus()) {
            throw new ServiceException("订单 [" + order.getOrderCode() + "] 状态为“" + order.getOrderStatus().getDesc() + "”，仅已收货状态可关闭");
        }

        PurchaseOrder update = new PurchaseOrder();
        update.setOrderId(orderId);
        update.setOrderStatus(PurchaseOrderStatus.CLOSED);
        boolean result = baseMapper.updateById(update) > 0;

        log.info("[closeOrder] - 成功. orderId: {}", orderId);
        return result;
    }

    /**
     * 采购订单财务对账查询
     *
     * @param purchaseOrderId 采购订单ID
     * @return 包含对账信息的Map对象，包括订单、入库、发票、付款等相关数据
     */
    @Override
    public Map<String, Object> reconcilePurchaseOrder(Long purchaseOrderId) {
        log.info("[reconcilePurchaseOrder] - 开始. purchaseOrderId: {}", purchaseOrderId);
        Map<String, Object> result = new HashMap<>();

        PurchaseOrderVo purchaseOrder = queryById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单 [" + purchaseOrderId + "] 不存在");
        }
        result.put("purchaseOrder", purchaseOrder);

        // TODO: [功能待实现] - 优先级: HIGH - 财务对账核心关联查询
        result.put("inbounds", purchaseInboundService.queryByDirectSourceId(purchaseOrderId));
        result.put("invoices", new ArrayList<>());
        result.put("payments", new ArrayList<>());
        result.put("links", new ArrayList<>());
        result.put("ledgers", new ArrayList<>());

        Map<String, Object> analysis = new HashMap<>();
        analysis.put("orderAmount", purchaseOrder.getAmount() != null ? purchaseOrder.getAmount() : BigDecimal.ZERO);
        analysis.put("invoiceAmount", BigDecimal.ZERO);
        analysis.put("paymentAmount", BigDecimal.ZERO);
        analysis.put("balanceAmount", BigDecimal.ZERO);
        analysis.put("reconcileStatus", "PENDING_IMPLEMENTATION");
        result.put("analysis", analysis);

        result.put("success", true);
        result.put("message", "查询成功");

        log.info("[reconcilePurchaseOrder] - 成功. purchaseOrderId: {}", purchaseOrderId);
        return result;
    }

    /**
     * 采购订库单确认后创建采购入库单
     *
     * @param purchaseOrder 采购订单
     */
    private void createPurchaseInboundFromConfirm(PurchaseOrder purchaseOrder) {
        try {
            R<PurchaseOrder> purchaseOrderValid = purchaseOrderValid(purchaseOrder, false);
            if (R.isError(purchaseOrderValid)) {
                throw new ServiceException(purchaseOrderValid.getMsg());
            }
            Boolean result = purchaseInboundService.createFromPurchaseOrder(purchaseOrder);
            if (!result) {
                throw new ServiceException("创建失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 手动从采购订单创建采购入库单
     *
     * @param orderId 已确认的采购订单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createPurchaseInbound(Long orderId) {
        try {
            R<PurchaseOrder> validPurchaseOrder = purchaseOrderValid(orderId, false);
            if (R.isError(validPurchaseOrder)) {
                throw new ServiceException(validPurchaseOrder.getMsg());
            }
            PurchaseOrder purchaseOrder = validPurchaseOrder.getData();
            boolean result = purchaseInboundService.createFromPurchaseOrder(purchaseOrder);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 手动从采购订单创建仓库入库单
     *
     * @param orderId 已确认的采购订单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createInbound(Long orderId) {
        try {
            R<PurchaseOrder> validPurchaseOrder = purchaseOrderValid(orderId, false);
            if (R.isError(validPurchaseOrder)) {
                throw new ServiceException(validPurchaseOrder.getMsg());
            }
            PurchaseOrder purchaseOrder = validPurchaseOrder.getData();
            boolean result = inboundService.createFromPurchaseOrder(purchaseOrder);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 采购入库完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completePurchaseInboundEvent(PurchaseInboundEvent event) {
        try {
            log.info("[completePurchaseInboundEvent] - 开始. event: {}", event);
            Long orderId = event.getPurchaseInbound().getDirectSourceId();
            List<PurchaseInbound> purchaseInbounds = purchaseInboundService.queryByDirectSourceId(orderId);
            Map<Long, BigDecimal> finishQuantity = new HashMap<>();
            for (PurchaseInbound purchaseInbound : purchaseInbounds) {
                List<PurchaseInboundItem> itemVo = purchaseInboundService.queryByInboundId(purchaseInbound.getInboundId());
                if (itemVo != null && !itemVo.isEmpty()) {
                    for (PurchaseInboundItem purchaseInboundItem : itemVo) {
                        if (purchaseInboundItem.getFinishQuantity() != null && purchaseInboundItem.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                            finishQuantity.merge(purchaseInboundItem.getProductId(), purchaseInboundItem.getFinishQuantity(), BigDecimal::add);
                        }
                    }
                }
            }
            //更新和填充采购订单明细已入库数量
            List<PurchaseOrderItem> purchaseOrderItems = itemMapper.queryByOrderId(orderId);
            List<PurchaseOrderItem> updates = new ArrayList<>();
            for (PurchaseOrderItem purchaseOrderItem : purchaseOrderItems) {
                BigDecimal inboundQty = finishQuantity.getOrDefault(purchaseOrderItem.getProductId(), BigDecimal.ZERO);
                if (purchaseOrderItem.getFinishQuantity() == null || purchaseOrderItem.getFinishQuantity().compareTo(inboundQty) != 0) {
                    purchaseOrderItem.setFinishQuantity(inboundQty);
                    updates.add(purchaseOrderItem);
                }
            }
            if (!itemMapper.updateBatchById(updates)) {
                throw new ServiceException("批量更新采购订单明细的已入库数量失败");
            }
            BigDecimal totalQty = purchaseOrderItems.stream().map(PurchaseOrderItem::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalFinishQty = purchaseOrderItems.stream().map(PurchaseOrderItem::getFinishQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            PurchaseOrderStatus newStatus = null;
            if (totalFinishQty.compareTo(BigDecimal.ZERO) > 0 && totalFinishQty.compareTo(totalQty) < 0) {
                newStatus = PurchaseOrderStatus.PARTIALLY_RECEIVED;
            } else if (totalFinishQty.compareTo(totalQty) >= 0) {
                newStatus = PurchaseOrderStatus.FULLY_RECEIVED;
            }

            if (newStatus != null) {
                PurchaseOrder currentOrder = baseMapper.selectById(orderId);
                if (currentOrder != null && currentOrder.getOrderStatus() != newStatus) {
                    PurchaseOrder orderUpdate = new PurchaseOrder();
                    orderUpdate.setOrderId(orderId);
                    orderUpdate.setOrderStatus(newStatus);
                    if (baseMapper.updateById(orderUpdate) > 0) {
                        log.info("[completePurchaseInboundEvent] - 状态更新成功. orderId: {}, from: {}, to: {}", orderId, currentOrder.getOrderStatus().getDesc(), newStatus.getDesc());
                    } else {
                        log.error("[completePurchaseInboundEvent] - 状态更新失败. orderId: {}", orderId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[completePurchaseInboundEvent] - 状态更新失败.", e);
        }
    }

    /**
     * 采购退货完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completePurchaseReturnEvent(PurchaseReturnEvent event) {
        try {
            log.info("[completePurchaseReturnEvent] - 开始. event: {}", event);
            Long orderId = event.getPurchaseReturn().getSourceId();
            List<PurchaseReturn> purchaseReturns = purchaseReturnService.queryByDirectSourceId(orderId);
            Map<Long, BigDecimal> finishQuantity = new HashMap<>();
            for (PurchaseReturn purchaseReturn : purchaseReturns) {
                List<PurchaseReturnItem> purchaseReturnItems = purchaseReturnService.queryItemByReturnId(purchaseReturn.getReturnId());
                if (purchaseReturnItems != null && !purchaseReturnItems.isEmpty()) {
                    for (PurchaseReturnItem purchaseReturnItem : purchaseReturnItems) {
                        finishQuantity.merge(purchaseReturnItem.getProductId(), purchaseReturnItem.getFinishQuantity(), BigDecimal::add);
                    }
                }
            }
            List<PurchaseOrderItem> orderItems = itemMapper.queryByOrderId(orderId);
            List<PurchaseOrderItem> updates = new ArrayList<>();
            for (PurchaseOrderItem item : orderItems) {
                BigDecimal returnedQty = finishQuantity.getOrDefault(item.getProductId(), BigDecimal.ZERO);
                if (item.getReturnedQuantity() == null || item.getReturnedQuantity().compareTo(returnedQty) != 0) {
                    item.setReturnedQuantity(returnedQty);
                    updates.add(item);
                }
            }
            if (!itemMapper.updateBatchById(updates)) {
                throw new ServiceException("批量更新采购订单明细的已退货数量失败");
            }
            log.info("[completePurchaseReturnEvent] - 成功. event: {}, 更新数: {}", event, updates.size());
        } catch (Exception e) {
            log.error("[completePurchaseReturnEvent] - 错误. event: {}", event, e);
        }
    }


    /**
     * 填充订单中的冗余字段
     */
    private void fillRedundantFields(PurchaseOrderBo bo) {
        if (bo.getSupplierId() != null && StringUtils.isEmpty(bo.getSupplierName())) {
            CompanyVo supplier = companyService.queryById(bo.getSupplierId());
            if (supplier != null) {
                bo.setSupplierName(supplier.getCompanyName());
            }
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            if (bo.getOrderId() == null) {
                bo.setApplicantId(loginUser.getUserId());
                bo.setApplicantName(loginUser.getNickname());
            }
            bo.setHandlerId(loginUser.getUserId());
            bo.setHandlerName(loginUser.getNickname());
        }
    }

}
