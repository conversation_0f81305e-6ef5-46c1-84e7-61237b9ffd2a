package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购退货服务接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-17
 */
public interface IPurchaseReturnService {

    /**
     * 根据ID查询详情
     *
     * @param returnId 主键
     * @return VO
     */
    PurchaseReturnVo queryById(Long returnId);

    /**
     * 查询分页列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页列表
     */
    TableDataInfo<PurchaseReturnVo> queryPageList(PurchaseReturnBo bo, PageQuery pageQuery);

    /**
     * 查询列表
     *
     * @param bo 查询条件
     * @return 列表
     */
    List<PurchaseReturnVo> queryList(PurchaseReturnBo bo);

    /**
     * 新增采购退货单
     *
     * @param bo 包含新采购退货单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    PurchaseReturnVo insertByBo(PurchaseReturnBo bo);

    /**
     * 修改采购退货单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    PurchaseReturnVo updateByBo(PurchaseReturnBo bo);

    /**
     * 校验并批量删除采购退货单
     *
     * @param ids     待删除的采购退货单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查是否存在指定上游单据的退货单
     *
     * @param directSourceId   上游单据ID
     * @param directSourceType 上游单据类型
     * @return 是否存在
     */
    Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 根据上游单据ID查询
     *
     * @param directSourceId 上游单据ID
     * @return 列表
     */
    List<PurchaseReturn> queryByDirectSourceId(Long directSourceId);

    /**
     * 根据退货单ID查询明细项
     *
     * @param returnId 采购退货单ID
     * @return 明细列表
     */
    List<PurchaseReturnItem> queryItemByReturnId(Long returnId);

    /**
     * 确认
     *
     * @param returnId 主键
     * @return 操作结果
     */
    Boolean confirmReturn(Long returnId);

    /**
     * 批量确认
     *
     * @param returnIds 主键集合
     * @return 操作结果
     */
    Boolean batchConfirmReturns(Collection<Long> returnIds);

    /**
     * 完成出库
     *
     * @param returnId 主键
     * @return 操作结果
     */
    Boolean completeReturn(Long returnId);

    /**
     * 取消
     *
     * @param returnId 主键
     * @param reason   取消原因
     * @return 操作结果
     */
    Boolean cancelReturn(Long returnId, String reason);

    /**
     * 基于采购入库单创建
     *
     * @param purchaseInbound 采购入库单
     * @return 操作结果
     */
    Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound);

}
