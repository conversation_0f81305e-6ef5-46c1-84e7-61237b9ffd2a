package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.domain.Transfer;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 仓库出库单 服务层
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface IOutboundService {

    /**
     * 查询仓库出库单
     *
     * @param outboundId 仓库出库单ID
     * @return 仓库出库单
     */
    OutboundVo queryById(Long outboundId);

    /**
     * 分页查询仓库出库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库出库单分页列表
     */
    TableDataInfo<OutboundVo> queryPageList(OutboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的仓库出库单列表
     *
     * @param bo 查询条件
     * @return 仓库出库单列表
     */
    List<OutboundVo> queryList(OutboundBo bo);

    /**
     * 新增仓库出库单
     *
     * @param bo 仓库出库单
     * @return 仓库出库单
     */
    OutboundVo insertByBo(OutboundBo bo);

    /**
     * 修改仓库出库单
     *
     * @param bo 仓库出库单
     * @return 仓库出库单
     */
    OutboundVo updateByBo(OutboundBo bo);

    /**
     * 校验并批量删除仓库出库单信息
     *
     * @param ids     待删除的仓库出库单ID集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询仓库出库单明细及其关联信息
     *
     * @param itemId 仓库出库单明细ID
     * @return 仓库出库单明细
     */
    OutboundItem queryItemById(Long itemId);

    /**
     * 查询仓库出库单明细列表及其关联信息
     *
     * @param outboundId 仓库出库单ID
     * @return 仓库出库单明细列表
     */
    List<OutboundItem> queryItemByOutboundId(Long outboundId);

    /**
     * 查询仓库出库单明细列表及其关联信息
     *
     * @param bo 查询条件
     * @return 仓库出库单明细列表
     */
    List<OutboundItem> queryItemList(OutboundItemBo bo);

    /**
     * 分页查询仓库出库单明细列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库出库单明细分页列表
     */
    TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery);

    /**
     * 根据直接来源ID查询已完成的仓库出库单
     *
     * @param directSourceId   直接来源ID
     * @param directSourceType 直接来源类型
     * @return 仓库出库单列表
     */
    List<Outbound> queryCompleteByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 根据直接来源ID查询仓库出库单是否存在
     *
     * @param directSourceId 直接来源ID
     * @return 是否存在
     */
    Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 根据销售出库单创建仓库出库单
     *
     * @param saleOutbound 销售出库单
     * @return 是否创建成功
     */
    Boolean createFromSaleOutbound(SaleOutbound saleOutbound);

    /**
     * 根据采购退货单创建仓库出库单
     *
     * @param purchaseReturn 采购退货单
     * @return 是否创建成功
     */
    Boolean createFromPurchaseReturn(PurchaseReturn purchaseReturn);

    /**
     * 基于仓库移库单创建仓库出库单
     *
     * @param transfer 仓库移库单
     * @return 是否创建成功
     */
    Boolean createFromTransfer(Transfer transfer);

    /**
     * 确认仓库出库单
     *
     * @param outboundId 仓库出库单ID
     * @return 是否确认成功
     */
    Boolean confirmOutbound(Long outboundId);

    /**
     * 批量确认仓库出库单
     *
     * @param outboundIds 仓库出库单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOutbounds(Collection<Long> outboundIds);

    /**
     * 完成仓库出库
     *
     * @param outboundId 仓库出库单ID
     * @return 是否完成成功
     */
    Boolean completeOutbound(Long outboundId);

    /**
     * 取消仓库出库单
     *
     * @param outboundId   仓库出库单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    Boolean cancelOutbound(Long outboundId, String cancelReason);
}
