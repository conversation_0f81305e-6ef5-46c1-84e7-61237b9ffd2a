package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionIssue;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 生产领料业务对象 mes_production_issue
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionIssue.class, reverseConvertGenerate = false)
public class ProductionIssueBo extends BaseEntity {

    /**
     * 领料单ID
     */
    private Long issueId;

    /**
     * 领料单编号
     */
    @NotBlank(message = "领料单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String issueCode;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 领料时间
     */
    @NotNull(message = "领料时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDateTime issueTime;

    /**
     * 领料状态
     */
    @NotBlank(message = "领料状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private ProductionIssueStatus issueStatus;

    /**
     * 领料申请人ID
     */
    private Long applicantId;

    /**
     * 领料申请人
     */
    private String applicantName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
