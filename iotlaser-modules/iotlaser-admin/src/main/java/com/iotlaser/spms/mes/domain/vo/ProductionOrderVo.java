package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionOrder;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 生产订单视图对象 mes_production_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionOrder.class)
public class ProductionOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long orderId;

    /**
     * 生产订单编码
     */
    @ExcelProperty(value = "生产订单编码")
    private String orderCode;

    /**
     * 生产订单类型
     */
    @ExcelProperty(value = "生产订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_order_type")
    private String orderType;

    /**
     * 源头单据ID (整个业务流程的最初发起单据)
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头单据编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头单据类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游单据ID (当前单据的直接创建来源单据)
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游单据编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游单据类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * BOMID
     */
    @ExcelProperty(value = "BOMID")
    private Long bomId;

    /**
     * BOM编码
     */
    @ExcelProperty(value = "BOM编码")
    private String bomCode;

    /**
     * BOM名称
     */
    @ExcelProperty(value = "BOM名称")
    private String bomName;

    /**
     * 计划生产数量
     */
    @ExcelProperty(value = "计划生产数量")
    private BigDecimal quantity;

    /**
     * 已完工入库数量
     */
    @ExcelProperty(value = "已完工入库数量")
    private BigDecimal finishQuantity;

    /**
     * 计划开始
     */
    @ExcelProperty(value = "计划开始")
    private LocalDate plannedStartDate;

    /**
     * 计划结束
     */
    @ExcelProperty(value = "计划结束")
    private LocalDate plannedEndDate;

    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private LocalDateTime actualEndTime;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_order_status")
    private ProductionOrderStatus orderStatus;

    /**
     * 计划员ID
     */
    @ExcelProperty(value = "计划员ID")
    private Long plannerId;

    /**
     * 计划员
     */
    @ExcelProperty(value = "计划员")
    private String plannerName;

    /**
     * 车间主管 ID
     */
    @ExcelProperty(value = "车间主管 ID")
    private Long supervisorId;

    /**
     * 车间主管
     */
    @ExcelProperty(value = "车间主管")
    private String supervisorName;

    /**
     * 工单下达时间
     */
    @ExcelProperty(value = "工单下达时间")
    private LocalDateTime releaseTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
