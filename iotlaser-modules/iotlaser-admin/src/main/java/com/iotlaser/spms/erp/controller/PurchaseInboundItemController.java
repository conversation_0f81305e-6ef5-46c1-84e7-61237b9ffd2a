package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购入库明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseInboundItem")
public class PurchaseInboundItemController extends BaseController {

    private final IPurchaseInboundItemService purchaseInboundItemService;

    /**
     * 查询采购入库明细列表
     */
    @SaCheckPermission("erp:purchaseInboundItem:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseInboundItemVo> list(PurchaseInboundItemBo bo, PageQuery pageQuery) {
        return purchaseInboundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购入库明细列表
     */
    @SaCheckPermission("erp:purchaseInboundItem:export")
    @Log(title = "采购入库明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseInboundItemBo bo, HttpServletResponse response) {
        List<PurchaseInboundItemVo> list = purchaseInboundItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购入库明细", PurchaseInboundItemVo.class, response);
    }

    /**
     * 获取采购入库明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:purchaseInboundItem:query")
    @GetMapping("/{itemId}")
    public R<PurchaseInboundItemVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long itemId) {
        return R.ok(purchaseInboundItemService.queryById(itemId));
    }

    /**
     * 新增采购入库明细
     */
    @SaCheckPermission("erp:purchaseInboundItem:add")
    @Log(title = "采购入库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<PurchaseInboundItemBo> bos) {
        return toAjax(purchaseInboundItemService.insertOrUpdateBatch(bos));
    }

    /**
     * 新增采购入库明细
     */
    @SaCheckPermission("erp:purchaseInboundItem:add")
    @Log(title = "采购入库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseInboundItemBo bo) {
        return toAjax(purchaseInboundItemService.insertByBo(bo));
    }

    /**
     * 修改采购入库明细
     */
    @SaCheckPermission("erp:purchaseInboundItem:edit")
    @Log(title = "采购入库明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseInboundItemBo bo) {
        return toAjax(purchaseInboundItemService.updateByBo(bo));
    }

    /**
     * 删除采购入库明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:purchaseInboundItem:remove")
    @Log(title = "采购入库明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(purchaseInboundItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

}
