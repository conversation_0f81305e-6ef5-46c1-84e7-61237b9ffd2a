package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.mapper.PurchaseOrderItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseOrderMapper;
import com.iotlaser.spms.erp.service.IPurchaseOrderItemService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ServiceImpl: 采购订单明细
 *
 * <AUTHOR> Kai
 * @date 2025-07-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderItemServiceImpl implements IPurchaseOrderItemService {

    private final PurchaseOrderItemMapper baseMapper;
    private final PurchaseOrderMapper purchaseOrderMapper;
    private final IProductService productService;

    /**
     * {@inheritDoc}
     *
     * @param itemId 主键
     * @return 采购订单明细
     */
    @Override
    public PurchaseOrderItemVo queryById(Long itemId) {
        log.debug("开始查询采购订单明细，ID: {}", itemId);
        PurchaseOrderItemVo vo = baseMapper.selectVoById(itemId);
        log.debug("查询采购订单明细完成，结果: {}", vo);
        return vo;
    }

    /**
     * {@inheritDoc}
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseOrderItemVo> queryPageList(PurchaseOrderItemBo bo, PageQuery pageQuery) {
        log.debug("开始分页查询采购订单明细列表，查询条件: {}, 分页参数: {}", bo, pageQuery);
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseOrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        log.debug("分页查询采购订单明细列表完成，总记录数: {}", result.getTotal());
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     *
     * @param bo 查询条件
     * @return 采购订单明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryList(PurchaseOrderItemBo bo) {
        log.debug("开始查询符合条件的采购订单明细列表，查询条件: {}", bo);
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        List<PurchaseOrderItemVo> list = baseMapper.selectVoList(lqw);
        log.debug("查询符合条件的采购订单明细列表完成，查询到 {} 条记录", list.size());
        return list;
    }

    /**
     * 构建查询包装器
     *
     * @param bo 查询条件
     * @return Lambda查询包装器
     */
    private LambdaQueryWrapper<PurchaseOrderItem> buildQueryWrapper(PurchaseOrderItemBo bo) {
        LambdaQueryWrapper<PurchaseOrderItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseOrderItem::getItemId);
        lqw.eq(bo.getOrderId() != null, PurchaseOrderItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, PurchaseOrderItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseOrderItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseOrderItem::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseOrderItem::getStatus, bo.getStatus());
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), PurchaseOrderItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * {@inheritDoc}
     *
     * @param bo 采购订单明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PurchaseOrderItemBo bo) {
        log.info("开始新增采购订单明细，产品: {}", bo.getProductName());
        try {
            // 1. 填充冗余信息与价格计算
            fillRedundantFields(bo);
            PurchaseOrderItem add = MapstructUtils.convert(bo, PurchaseOrderItem.class);

            // 2. 实体校验
            validEntityBeforeSave(add);

            // 3. 执行插入
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setItemId(add.getItemId());
                log.info("新增采购订单明细成功，ID: {}", add.getItemId());

                // 4. 更新主订单金额
                if (add.getOrderId() != null) {
                    updateOrderTotalAmounts(add.getOrderId());
                }
            } else {
                log.warn("新增采购订单明细失败，数据库未返回成功标志，产品: {}", bo.getProductName());
            }
            return flag;
        } catch (ServiceException e) {
            log.error("新增采购订单明细业务校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("新增采购订单明细时发生未知异常", e);
            throw new ServiceException("系统异常，新增采购订单明细失败");
        }
    }

    /**
     * {@inheritDoc}
     *
     * @param bo 采购订单明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PurchaseOrderItemBo bo) {
        log.info("开始更新采购订单明细，ID: {}", bo.getItemId());
        try {
            // 1. 填充冗余信息与价格计算
            fillRedundantFields(bo);
            PurchaseOrderItem item = MapstructUtils.convert(bo, PurchaseOrderItem.class);

            // 2. 验证实体
            validEntityBeforeSave(item);

            // 3. 执行更新
            int result = baseMapper.updateById(item);
            boolean flag = result > 0;

            if (flag) {
                log.info("更新采购订单明细成功，ID: {}", item.getItemId());
                // 4. 更新主订单金额
                if (item.getOrderId() != null) {
                    updateOrderTotalAmounts(item.getOrderId());
                }
            } else {
                log.warn("更新采购订单明细失败，未找到匹配的记录或数据未变更，ID: {}", bo.getItemId());
            }
            return flag;
        } catch (ServiceException e) {
            log.error("更新采购订单明细业务校验失败，ID: {}，错误: {}", bo.getItemId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新采购订单明细时发生未知异常，ID: {}", bo.getItemId(), e);
            throw new ServiceException("系统异常，更新采购订单明细失败");
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 待校验的实体
     */
    private void validEntityBeforeSave(PurchaseOrderItem entity) {
        log.debug("开始对采购订单明细进行保存前校验，产品ID: {}", entity.getProductId());
        // 校验同一订单中产品不能重复
        if (entity.getOrderId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrderItem::getOrderId, entity.getOrderId());
            wrapper.eq(PurchaseOrderItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(PurchaseOrderItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                String errorMsg = String.format("同一采购订单中不能重复添加相同产品【%s】", entity.getProductName());
                log.warn(errorMsg);
                throw new ServiceException(errorMsg);
            }
        }
        log.debug("采购订单明细保存前校验通过");
    }

    /**
     * {@inheritDoc}
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("开始批量删除采购订单明细，ID列表: {}, 是否校验: {}", ids, isValid);
        // 1. 获取待删除明细，用于后续更新主订单金额
        List<PurchaseOrderItem> itemsToDelete = baseMapper.selectByIds(ids);
        if (itemsToDelete.isEmpty()) {
            log.warn("没有找到要删除的采购订单明细，ID列表: {}", ids);
            return false;
        }
        Set<Long> orderIds = new HashSet<>();

        // 2. 如果需要，执行业务校验
        if (isValid) {
            log.debug("开始对要删除的采购订单明细进行业务校验...");
            for (PurchaseOrderItem item : itemsToDelete) {
                orderIds.add(item.getOrderId());

                // 2.1 检查关联的采购订单状态
                PurchaseOrder order = purchaseOrderMapper.selectById(item.getOrderId());
                if (order != null && PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
                    String errorMsg = String.format("采购订单明细所属订单【%s】状态为【%s】，不允许删除",
                        order.getOrderCode(), order.getOrderStatus().getName());
                    log.warn(errorMsg);
                    throw new ServiceException(errorMsg);
                }

                // 2.2 检查是否已有收货记录
                if (item.getFinishQuantity() != null && item.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    String errorMsg = String.format("采购订单明细【%s】已有收货记录，不允许删除", item.getProductName());
                    log.warn(errorMsg);
                    throw new ServiceException(errorMsg);
                }
                log.debug("删除采购订单明细校验通过：产品【{}】", item.getProductName());
            }
        } else {
            // 如果不校验，也需要收集订单ID用于后续金额更新
            orderIds.addAll(itemsToDelete.stream().map(PurchaseOrderItem::getOrderId).collect(Collectors.toSet()));
        }

        try {
            // 3. 执行删除
            int result = baseMapper.deleteByIds(ids);
            boolean flag = result > 0;

            if (flag) {
                log.info("批量删除采购订单明细成功，删除数量: {}", result);
                // 4. 更新主订单金额
                for (Long orderId : orderIds) {
                    if (orderId != null) {
                        updateOrderTotalAmounts(orderId);
                    }
                }
            } else {
                log.warn("批量删除采购订单明细失败，数据库未返回成功标志，ID列表: {}", ids);
            }
            return flag;
        } catch (Exception e) {
            log.error("批量删除采购订单明细时发生未知异常，ID列表: {}", ids, e);
            throw new ServiceException("系统异常，删除采购订单明细失败");
        }
    }

    /**
     * {@inheritDoc}
     *
     * @param items 采购订单明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<PurchaseOrderItemBo> items) {
        log.info("开始批量新增或更新采购订单明细，数量: {}", items.size());
        try {
            // 1. 转换并填充数据
            List<PurchaseOrderItem> entities = items.stream()
                .map(bo -> {
                    fillRedundantFields(bo);
                    return MapstructUtils.convert(bo, PurchaseOrderItem.class);
                })
                .collect(Collectors.toList());

            // 2. 逐个验证实体
            entities.forEach(this::validEntityBeforeSave);

            // 3. 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新采购明细成功，数量: {}", entities.size());
            } else {
                log.warn("批量插入或更新采购明细失败，数据库未返回成功标志");
            }
            return result;
        } catch (ServiceException e) {
            log.error("批量插入或更新采购明细业务校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批量插入或更新采购明细时发生未知异常", e);
            throw new ServiceException("系统异常，批量操作失败");
        }
    }

    /**
     * {@inheritDoc}
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryByOrderId(Long orderId) {
        log.debug("开始根据订单ID查询明细列表，订单ID: {}", orderId);
        LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseOrderItem::getOrderId, orderId);
        List<PurchaseOrderItemVo> list = baseMapper.selectVoList(wrapper);
        log.debug("根据订单ID查询明细列表完成，订单ID: {}, 查询到 {} 条记录", orderId, list.size());
        return list;
    }

    /**
     * 填充冗余字段与价格计算
     * <p>
     * 1. 根据产品ID，自动填充产品编码、名称、单位等信息。
     * 2. 如果未提供价格，则从产品主数据中获取默认采购价。
     * 3. 根据“含税价、数量、税率”自动计算“不含税价、含税总额、不含税总额、总税额”。
     *
     * @param bo 采购明细业务对象
     * @return 填充后的采购明细业务对象
     */
    private PurchaseOrderItemBo fillRedundantFields(PurchaseOrderItemBo bo) {
        log.debug("开始为采购订单明细填充冗余字段，产品ID: {}", bo.getProductId());
        // 1. 填充产品相关信息
        if (bo.getProductId() != null) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product != null) {
                bo.setProductCode(product.getProductCode());
                bo.setProductName(product.getProductName());
                bo.setUnitId(product.getUnitId());
                bo.setUnitCode(product.getUnitCode());
                bo.setUnitName(product.getUnitName());
                // 如果没有设置价格，使用产品的默认采购价格
                if (bo.getPrice() == null && product.getPurchasePrice() != null) {
                    bo.setPrice(product.getPurchasePrice());
                    log.debug("明细项未提供价格，使用产品主数据默认含税采购价: {}", product.getPurchasePrice());
                }
                if (bo.getPriceExclusiveTax() == null && product.getPurchasePriceExclusiveTax() != null) {
                    bo.setPriceExclusiveTax(product.getPurchasePriceExclusiveTax());
                }
                if (bo.getTaxRate() == null && product.getPurchaseTaxRate() != null) {
                    bo.setTaxRate(product.getPurchaseTaxRate());
                    log.debug("明细项未提供税率，使用产品主数据默认采购税率: {}", product.getPurchaseTaxRate());
                }
            } else {
                log.warn("未找到产品ID为 {} 的产品信息", bo.getProductId());
            }
        }

        // 2. 价格计算
        if (bo.getPrice() != null && bo.getTaxRate() != null && bo.getQuantity() != null) {
            log.debug("开始计算采购订单明细价格，数量: {}, 含税价: {}, 税率: {}", bo.getQuantity(), bo.getPrice(), bo.getTaxRate());
            // 从含税价计算其他价格字段
            TaxCalculationResultBo result = TaxCalculationUtils.calculate(bo.getQuantity(), bo.getTaxRate(), bo.getPrice());
            bo.setPriceExclusiveTax(result.getPriceExclusiveTax());
            bo.setAmount(result.getAmount());
            bo.setAmountExclusiveTax(result.getAmountExclusiveTax());
            bo.setTaxAmount(result.getTaxAmount());

            log.debug("采购订单明细价格计算完成 - 不含税价: {}, 金额(含税): {}, 金额(不含税): {}, 税额: {}",
                result.getPriceExclusiveTax(), result.getAmount(), result.getAmountExclusiveTax(), result.getTaxAmount());
        }
        return bo;
    }

    /**
     * 更新主订单金额合计
     * <p>
     * 在采购订单明细发生任何（新增、修改、删除）变更后，调用此方法重新计算并更新主订单的价税合计字段。
     *
     * @param orderId 订单ID
     */
    private void updateOrderTotalAmounts(Long orderId) {
        log.debug("准备更新采购订单金额合计，订单ID: {}", orderId);
        try {
            if (orderId == null) {
                log.warn("订单ID为空，跳过金额合计更新");
                return;
            }

            // 1. 从数据库重新计算总金额
            TaxCalculationResultBo totalAmount = baseMapper.calculateTotalAmount(orderId);

            // 2. 如果订单已无明细，则将金额清零
            if (totalAmount == null) {
                log.debug("采购订单【{}】已无明细，将清空金额字段", orderId);
                totalAmount = TaxCalculationResultBo.builder()
                    .amount(BigDecimal.ZERO)
                    .amountExclusiveTax(BigDecimal.ZERO)
                    .taxAmount(BigDecimal.ZERO)
                    .build();
            }

            // 3. 构建更新对象并执行
            PurchaseOrder update = new PurchaseOrder();
            update.setOrderId(orderId);
            update.setAmount(totalAmount.getAmount());
            update.setAmountExclusiveTax(totalAmount.getAmountExclusiveTax());
            update.setTaxAmount(totalAmount.getTaxAmount());

            int result = purchaseOrderMapper.updateById(update);
            if (result > 0) {
                log.info("更新采购订单金额合计成功 - 订单ID: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                    orderId, totalAmount.getAmount(), totalAmount.getAmountExclusiveTax(), totalAmount.getTaxAmount());
            } else {
                // 注意：在某些情况下，即使计算结果与原值相同，update也会返回0。这不一定是错误。
                log.warn("更新采购订单金额合计操作未影响任何行，可能金额未发生变化 - 订单ID: {}", orderId);
            }
        } catch (Exception e) {
            // 此处捕获异常是为了防止更新主订单金额失败影响到明细操作的主流程
            log.error("更新采购订单金额合计时发生未知异常 - 订单ID: {}", orderId, e);
        }
    }
}
