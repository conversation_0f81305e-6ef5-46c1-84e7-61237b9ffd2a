package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收款单与应收单核销关系Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface FinArReceiptReceivableLinkMapper extends BaseMapperPlus<FinArReceiptReceivableLink, FinArReceiptReceivableLinkVo> {

    default List<FinArReceiptReceivableLinkVo> queryByReceivableId(Long receivableId) {
        return selectVoList(new LambdaQueryWrapper<FinArReceiptReceivableLink>()
            .eq(FinArReceiptReceivableLink::getReceivableId, receivableId));
    }

    default List<FinArReceiptReceivableLinkVo> queryByReceiptId(Long receiptId) {
        return selectVoList(new LambdaQueryWrapper<FinArReceiptReceivableLink>()
            .eq(FinArReceiptReceivableLink::getReceiptId, receiptId));
    }

    default BigDecimal getAppliedAmountByReceiptId(Long receiptId) {
        return selectList(new LambdaQueryWrapper<FinArReceiptReceivableLink>().select(FinArReceiptReceivableLink::getAppliedAmount).eq(FinArReceiptReceivableLink::getReceiptId, receiptId)).stream().map(FinArReceiptReceivableLink::getAppliedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    default BigDecimal getAppliedAmountByReceivableId(Long receivableId) {
        return selectList(new LambdaQueryWrapper<FinArReceiptReceivableLink>().select(FinArReceiptReceivableLink::getAppliedAmount).eq(FinArReceiptReceivableLink::getReceivableId, receivableId)).stream().map(FinArReceiptReceivableLink::getAppliedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    default Boolean existsByReceiptId(Long receiptId) {
        return exists(new LambdaQueryWrapper<FinArReceiptReceivableLink>().eq(FinArReceiptReceivableLink::getReceiptId, receiptId));
    }

    default Boolean existsByReceivableId(Long receivableId) {
        return exists(new LambdaQueryWrapper<FinArReceiptReceivableLink>().eq(FinArReceiptReceivableLink::getReceivableId, receivableId));
    }

}
