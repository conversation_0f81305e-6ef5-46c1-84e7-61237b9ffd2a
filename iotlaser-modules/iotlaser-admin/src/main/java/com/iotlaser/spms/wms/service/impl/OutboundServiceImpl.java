package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.event.OutboundEvent;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import com.iotlaser.spms.mes.service.IProductionIssueService;
import com.iotlaser.spms.wms.domain.*;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.*;
import com.iotlaser.spms.wms.mapper.OutboundItemBatchMapper;
import com.iotlaser.spms.wms.mapper.OutboundItemMapper;
import com.iotlaser.spms.wms.mapper.OutboundMapper;
import com.iotlaser.spms.wms.service.IInventoryService;
import com.iotlaser.spms.wms.service.IOutboundService;
import com.iotlaser.spms.wms.service.ITransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_OUTBOUND_CODE;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 仓库出库单 服务层实现
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundServiceImpl implements IOutboundService {

    private final OutboundMapper baseMapper;
    private final OutboundItemMapper itemMapper;
    private final OutboundItemBatchMapper batchMapper;
    private final Gen gen;
    private final IInventoryService inventoryService;

    @Autowired
    @Lazy
    private ISaleOutboundService saleOutboundService;
    @Autowired
    @Lazy
    private IPurchaseReturnService purchaseReturnService;
    @Autowired
    @Lazy
    private IProductionIssueService productionIssueService;
    @Autowired
    @Lazy
    private ITransferService transferService;

    /**
     * {@inheritDoc}
     */
    @Override
    public OutboundVo queryById(Long outboundId) {
        return baseMapper.selectVoById(outboundId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<OutboundVo> queryPageList(OutboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Outbound> lqw = buildQueryWrapper(bo);
        Page<OutboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<OutboundVo> queryList(OutboundBo bo) {
        LambdaQueryWrapper<Outbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Outbound> buildQueryWrapper(OutboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Outbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(Outbound::getOutboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutboundCode()), Outbound::getOutboundCode, bo.getOutboundCode());
        if (bo.getOutboundStatus() != null) {
            lqw.eq(Outbound::getOutboundStatus, bo.getOutboundStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Outbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginOutboundTime") != null && params.get("endOutboundTime") != null,
            Outbound::getOutboundTime, params.get("beginOutboundTime"), params.get("endOutboundTime"));
        return lqw;
    }

    /**
     * 新增仓库出库单
     *
     * @param bo 包含新仓库出库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutboundVo insertByBo(OutboundBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getOutboundCode())) {
                bo.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));
            }
            if (bo.getOutboundStatus() == null) {
                bo.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            }
            if (bo.getOutboundTime() == null) {
                bo.setOutboundTime(LocalDateTime.now());
            }

            Outbound add = MapstructUtils.convert(bo, Outbound.class);
            validEntityBeforeSave(add);

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("新增仓库出库单失败");
            }

            if (add.getSourceType() == null) {
                add.setSourceType(SourceType.OUTBOUND);
                add.setSourceId(add.getOutboundId());
                add.setSourceCode(add.getOutboundCode());
            }
            if (add.getDirectSourceType() == null) {
                add.setDirectSourceId(add.getOutboundId());
                add.setDirectSourceCode(add.getOutboundCode());
                add.setDirectSourceType(DirectSourceType.INVENTORY_ADJUSTMENT);
            }
            baseMapper.updateById(add);

            log.info("[insertByBo] - 新增成功: {}", add.getOutboundCode());
            return MapstructUtils.convert(add, OutboundVo.class);
        } catch (Exception e) {
            log.error("[insertByBo] - 新增失败", e);
            throw new ServiceException("新增仓库出库单失败");
        }
    }

    /**
     * 修改仓库出库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutboundVo updateByBo(OutboundBo bo) {
        try {
            Outbound update = MapstructUtils.convert(bo, Outbound.class);
            validEntityBeforeSave(update);

            if (baseMapper.updateById(update) <= 0) {
                throw new ServiceException("修改仓库出库单失败：出库单不存在或数据未变更");
            }
            log.info("[updateByBo] - 修改成功: {}", update.getOutboundCode());
            return MapstructUtils.convert(update, OutboundVo.class);
        } catch (Exception e) {
            log.error("[updateByBo] - 修改失败", e);
            throw new ServiceException("修改仓库出库单失败");
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Outbound entity) {
        // 校验出库单编码唯一性
        if (StringUtils.isNotBlank(entity.getOutboundCode())) {
            LambdaQueryWrapper<Outbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Outbound::getOutboundCode, entity.getOutboundCode());
            if (entity.getOutboundId() != null) {
                wrapper.ne(Outbound::getOutboundId, entity.getOutboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("出库单编码已存在：" + entity.getOutboundCode());
            }
        }
    }

    /**
     * 校验并批量删除仓库出库单
     *
     * @param ids     待删除的仓库出库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            List<Outbound> outbounds = baseMapper.selectByIds(ids);
            for (Outbound outbound : outbounds) {
                if (OutboundStatus.PENDING_PICKING != outbound.getOutboundStatus() && OutboundStatus.PICKING_IN_PROGRESS != outbound.getOutboundStatus()) {
                    throw new ServiceException("出库单【" + outbound.getOutboundCode() + "】状态为【" + outbound.getOutboundStatus() + "】，不允许删除");
                }
            }
        }

        try {
            batchMapper.deleteByOutboundIds(ids);
            itemMapper.deleteByOutboundIds(ids);
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("[deleteWithValidByIds] - 批量删除成功, ID: {}", ids);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - 批量删除失败, ID: {}", ids, e);
            throw new ServiceException("删除仓库出库单失败");
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public OutboundItem queryItemById(Long itemId) {
        return itemMapper.queryById(itemId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<OutboundItem> queryItemByOutboundId(Long outboundId) {
        OutboundItemBo bo = new OutboundItemBo();
        bo.setOutboundId(outboundId);
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<OutboundItem> queryItemList(OutboundItemBo bo) {
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<OutboundItemVo> result = MapstructUtils.convert(itemMapper.queryPageList(pageQuery.build(), queryWrapper), OutboundItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<OutboundItem> buildQueryWrapperWith(OutboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<OutboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getOutboundId() != null, "item.outbound_id", bo.getOutboundId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getSourceId() != null, "item.source_id", bo.getSourceId());
        if (bo.getSourceType() != null) {
            wrapper.eq("item.source_type", bo.getSourceType());
        }
        wrapper.eq(bo.getDirectSourceId() != null, "item.direct_source_id", bo.getDirectSourceId());
        if (bo.getDirectSourceType() != null) {
            wrapper.eq("item.direct_source_type", bo.getDirectSourceType());
        }
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Outbound> queryCompleteByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return baseMapper.selectList(new LambdaQueryWrapper<Outbound>().eq(Outbound::getDirectSourceId, directSourceId).eq(Outbound::getDirectSourceType, directSourceType).eq(Outbound::getOutboundStatus, OutboundStatus.COMPLETED));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return baseMapper.existsByDirectSourceId(directSourceId, directSourceType);
    }

    /**
     * 从销售出库单创建仓库出库单
     *
     * @param saleOutbound 已确认的销售出库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromSaleOutbound(SaleOutbound saleOutbound) {
        try {

            boolean exists = existsByDirectSourceId(saleOutbound.getOutboundId(), DirectSourceType.SALE_OUTBOUND);
            if (exists) {
                throw new ServiceException("该销售出库单已生成仓库出库单，不能重复自动生成");
            }
            Outbound add = new Outbound();
            add.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));
            add.setSourceId(saleOutbound.getSourceId());
            add.setSourceCode(saleOutbound.getSourceCode());
            add.setSourceType(saleOutbound.getSourceType());
            add.setDirectSourceId(saleOutbound.getOutboundId());
            add.setDirectSourceCode(saleOutbound.getOutboundCode());
            add.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);

            add.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            add.setOutboundTime(LocalDateTime.now());

            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                add.setPackerId(loginUser.getUserId());
                add.setPackerName(loginUser.getNickname());
            }
            add.setSummary("[销售出库单" + saleOutbound.getOutboundCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<OutboundItem> outboundItems = new ArrayList<>();
            for (SaleOutboundItem saleOutboundItem : saleOutbound.getItems()) {
                OutboundItem item = new OutboundItem();
                item.setOutboundId(add.getOutboundId());

                item.setSourceId(saleOutbound.getSourceId());
                item.setSourceCode(saleOutbound.getSourceCode());
                item.setSourceType(saleOutbound.getSourceType());

                item.setDirectSourceId(saleOutbound.getOutboundId());
                item.setDirectSourceCode(saleOutbound.getOutboundCode());
                item.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);
                item.setDirectSourceItemId(saleOutboundItem.getItemId());

                item.setProductId(saleOutboundItem.getProductId());
                item.setProductCode(saleOutboundItem.getProductCode());
                item.setProductName(saleOutboundItem.getProductName());
                item.setUnitId(saleOutboundItem.getUnitId());
                item.setUnitCode(saleOutboundItem.getUnitCode());
                item.setUnitName(saleOutboundItem.getUnitName());

                item.setLocationId(saleOutboundItem.getLocationId());
                item.setLocationCode(saleOutboundItem.getLocationCode());
                item.setLocationName(saleOutboundItem.getLocationName());

                item.setQuantity(saleOutboundItem.getQuantity());
                item.setFinishQuantity(saleOutboundItem.getFinishQuantity());
                item.setCostPrice(saleOutboundItem.getPriceExclusiveTax());
                item.setRemark("[销售出库单" + saleOutbound.getOutboundCode() + "]");
                outboundItems.add(item);
            }
            if (!itemMapper.insertBatch(outboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从采购退货单创建仓库出库单
     *
     * @param purchaseReturn 已确认的采购退货单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseReturn(PurchaseReturn purchaseReturn) {
        try {
            boolean exists = existsByDirectSourceId(purchaseReturn.getReturnId(), DirectSourceType.PURCHASE_RETURN);
            if (exists) {
                throw new ServiceException("该采购退货单已生成仓库出库单，不能重复自动生成");
            }
            Outbound add = new Outbound();
            add.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));

            add.setSourceId(purchaseReturn.getSourceId());
            add.setSourceCode(purchaseReturn.getSourceCode());
            add.setSourceType(purchaseReturn.getSourceType());
            add.setDirectSourceId(purchaseReturn.getReturnId());
            add.setDirectSourceCode(purchaseReturn.getReturnCode());
            add.setDirectSourceType(DirectSourceType.PRODUCTION_RETURN);

            add.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            add.setOutboundTime(LocalDateTime.now());

            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                add.setPackerId(loginUser.getUserId());
                add.setPackerName(loginUser.getNickname());
            }
            add.setSummary("[采购退货单" + purchaseReturn.getReturnCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<OutboundItem> outboundItems = new ArrayList<>();
            for (PurchaseReturnItem purchaseReturnItem : purchaseReturn.getItems()) {
                OutboundItem item = new OutboundItem();
                item.setOutboundId(add.getOutboundId());

                item.setSourceId(purchaseReturn.getSourceId());
                item.setSourceCode(purchaseReturn.getSourceCode());
                item.setSourceType(purchaseReturn.getSourceType());

                item.setDirectSourceId(purchaseReturn.getReturnId());
                item.setDirectSourceCode(purchaseReturn.getReturnCode());
                item.setDirectSourceType(DirectSourceType.PRODUCTION_RETURN);
                item.setDirectSourceItemId(purchaseReturnItem.getItemId());

                item.setProductId(purchaseReturnItem.getProductId());
                item.setProductCode(purchaseReturnItem.getProductCode());
                item.setProductName(purchaseReturnItem.getProductName());
                item.setUnitId(purchaseReturnItem.getUnitId());
                item.setUnitCode(purchaseReturnItem.getUnitCode());
                item.setUnitName(purchaseReturnItem.getUnitName());

                item.setLocationId(purchaseReturnItem.getLocationId());
                item.setLocationCode(purchaseReturnItem.getLocationCode());
                item.setLocationName(purchaseReturnItem.getLocationName());

                item.setQuantity(purchaseReturnItem.getQuantity());
                item.setFinishQuantity(purchaseReturnItem.getFinishQuantity());
                item.setCostPrice(purchaseReturnItem.getPriceExclusiveTax());
                item.setRemark("[采购退货单" + purchaseReturn.getReturnCode() + "]");
                outboundItems.add(item);
            }
            if (!itemMapper.insertBatch(outboundItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 从仓库移库单创建仓库出库单
     *
     * @param transfer 已确认的仓库移库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromTransfer(Transfer transfer) {
        try {
            boolean exists = existsByDirectSourceId(transfer.getTransferId(), DirectSourceType.TRANSFER);
            if (exists) {
                throw new ServiceException("该移库单已生成仓库出库单，不能重复自动生成");
            }
            Outbound add = new Outbound();
            add.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));

            add.setSourceId(transfer.getTransferId());
            add.setSourceCode(transfer.getTransferCode());
            add.setSourceType(SourceType.TRANSFER);

            add.setDirectSourceId(transfer.getTransferId());
            add.setDirectSourceCode(transfer.getTransferCode());
            add.setDirectSourceType(DirectSourceType.TRANSFER);

            add.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            add.setOutboundTime(transfer.getTransferTime());

            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                add.setPackerId(loginUser.getUserId());
                add.setPackerName(loginUser.getNickname());
            }
            add.setSummary("由仓库移库单[" + transfer.getTransferCode() + "]");

            if (baseMapper.insert(add) <= 0) {
                throw new ServiceException("创建主记录失败");
            }

            List<OutboundItem> items = new ArrayList<>();
            for (TransferItem transferItem : transfer.getItems()) {
                OutboundItem item = new OutboundItem();
                item.setOutboundId(add.getOutboundId());
                item.setSourceId(transfer.getTransferId());
                item.setSourceCode(transfer.getTransferCode());
                item.setSourceType(SourceType.TRANSFER);

                item.setDirectSourceId(transfer.getTransferId());
                item.setDirectSourceCode(transfer.getTransferCode());
                item.setDirectSourceType(DirectSourceType.TRANSFER);
                item.setDirectSourceItemId(transferItem.getItemId());

                item.setProductId(transferItem.getProductId());
                item.setProductCode(transferItem.getProductCode());
                item.setProductName(transferItem.getProductName());
                item.setLocationId(transferItem.getFromLocationId());
                item.setLocationCode(transferItem.getFromLocationCode());
                item.setLocationName(transferItem.getFromLocationName());
                item.setUnitId(transferItem.getUnitId());
                item.setUnitCode(transferItem.getUnitCode());
                item.setUnitName(transferItem.getUnitName());
                item.setQuantity(transferItem.getQuantity());
                item.setCostPrice(transferItem.getCostPrice());
                item.setRemark("由仓库移库单[" + transfer.getTransferCode() + "]");

                if (transferItem.getBatches() != null && !transferItem.getBatches().isEmpty()) {
                    List<OutboundItemBatch> itemBatches = new ArrayList<>();
                    for (TransferItemBatch transferBatch : transferItem.getBatches()) {
                        OutboundItemBatch batch = new OutboundItemBatch();
                        batch.setOutboundId(add.getOutboundId());
                        batch.setInternalBatchNumber(transferBatch.getInternalBatchNumber());
                        batch.setSupplierBatchNumber(transferBatch.getSupplierBatchNumber());
                        batch.setSerialNumber(transferBatch.getSerialNumber());
                        batch.setProductId(transferBatch.getProductId());
                        batch.setProductCode(transferBatch.getProductCode());
                        batch.setProductName(transferBatch.getProductName());
                        batch.setUnitId(transferBatch.getUnitId());
                        batch.setUnitCode(transferBatch.getUnitCode());
                        batch.setUnitName(transferBatch.getUnitName());
                        batch.setLocationId(transferBatch.getFromLocationId());
                        batch.setLocationCode(transferBatch.getFromLocationCode());
                        batch.setLocationName(transferBatch.getFromLocationName());
                        batch.setQuantity(transferBatch.getQuantity());
                        batch.setCostPrice(transferBatch.getCostPrice());
                        batch.setRemark("由仓库移库单[" + transfer.getTransferCode() + "]");
                        itemBatches.add(batch);
                    }
                    item.setBatches(itemBatches);
                }
                items.add(item);
            }

            if (!itemMapper.insertBatch(items)) {
                throw new ServiceException("创建明细记录失败");
            }

            List<OutboundItemBatch> allBatches = new ArrayList<>();
            for (OutboundItem item : items) {
                if (item.getBatches() != null && !item.getBatches().isEmpty()) {
                    item.getBatches().forEach(batch -> {
                        batch.setItemId(item.getItemId());
                        allBatches.add(batch);
                    });
                }
            }
            if (!allBatches.isEmpty() && !batchMapper.insertBatch(allBatches)) {
                throw new ServiceException("创建批次记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 校验库存充足性
     *
     * @param outboundItems 出库明细列表
     */
    private void validateInventoryAvailability(List<OutboundItem> outboundItems) {
        for (OutboundItem ioutboundItemem : outboundItems) {
            // 校验库存数量是否充足
            BigDecimal availableQuantity = inventoryService.sumAvailableQuantityByProductId(ioutboundItemem.getProductId());
            if (availableQuantity == null) {
                throw new ServiceException(String.format("产品【%s】的库存为空，需要：%s，可用：%s", ioutboundItemem.getProductName(), ioutboundItemem.getQuantity(), BigDecimal.ZERO));
            }
            // 校验批次库存数量是否充足
            for (OutboundItemBatch batch : ioutboundItemem.getBatches()) {
                BigDecimal batchAvailableQuantity = inventoryService.sumAvailableQuantityByProduct(batch.getProductId(), batch.getLocationId());
                if (batchAvailableQuantity == null) {
                    throw new ServiceException(String.format("产品【%s】在库位【%s】的库存为空，需要：%s，可用：%s", batch.getProductName(), batch.getLocationName(), batch.getQuantity(), BigDecimal.ZERO));
                } else if (batchAvailableQuantity.compareTo(batch.getQuantity()) < 0) {
                    throw new ServiceException(String.format("产品【%s】在库位【%s】的可用库存不足，需要：%s，可用：%s", batch.getProductName(), batch.getLocationName(), batch.getQuantity(), batchAvailableQuantity));
                }
            }
        }
    }

    /**
     * 确认仓库出库单
     *
     * @param outboundId 待确认的仓库出库单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmOutbound(Long outboundId) {
        try {
            R<Outbound> validOutbound = validOutbound(outboundId, false);
            if (R.isError(validOutbound)) {
                throw new ServiceException(validOutbound.getMsg());
            }
            Outbound outbound = validOutbound.getData();

            if (OutboundStatus.PENDING_PICKING != outbound.getOutboundStatus()) {
                throw new ServiceException("只有待拣货状态的仓库出库单才能确认");
            }

            Outbound update = new Outbound();
            update.setOutboundId(outbound.getOutboundId());
            update.setOutboundStatus(OutboundStatus.PICKING_IN_PROGRESS);
            String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
            update.setRemark(currentRemark + " [确认时间：" + DateUtils.getTime() + "]");

            if (baseMapper.updateById(update) <= 0) {
                throw new ServiceException("确认仓库出库单失败");
            }
            log.info("[confirmOutbound] - 确认成功: {}", outbound.getOutboundCode());
            return true;
        } catch (Exception e) {
            log.error("[confirmOutbound] - 确认失败, ID: {}", outboundId, e);
            throw new ServiceException("确认仓库出库单失败");
        }
    }

    /**
     * 批量确认仓库出库单
     *
     * @param outboundIds 待确认的仓库出库单ID集合
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmOutbounds(Collection<Long> outboundIds) {
        if (outboundIds == null || outboundIds.isEmpty()) {
            throw new ServiceException("仓库出库单ID集合不能为空");
        }
        int successCount = 0;
        for (Long outboundId : outboundIds) {
            try {
                confirmOutbound(outboundId);
                successCount++;
            } catch (Exception e) {
                log.warn("[batchConfirmOutbounds] - 单个确认失败, ID: {}, 错误: {}", outboundId, e.getMessage());
            }
        }
        if (successCount == 0) {
            throw new ServiceException("批量确认失败，没有仓库出库单被成功确认");
        }
        log.info("[batchConfirmOutbounds] - 批量确认完成: 成功 {}/{}", successCount, outboundIds.size());
        return true;
    }

    /**
     * 完成仓库出库操作
     *
     * @param outboundId 待完成的仓库出库单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeOutbound(Long outboundId) {
        try {
            R<Outbound> validOutbound = validOutbound(outboundId, true);
            if (R.isError(validOutbound)) {
                throw new ServiceException(validOutbound.getMsg());
            }
            Outbound outbound = validOutbound.getData();

            updateInventoryRecords(outbound);

            Outbound update = new Outbound();
            update.setOutboundId(outbound.getOutboundId());
            update.setOutboundStatus(OutboundStatus.COMPLETED);
            String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
            update.setRemark(currentRemark + " [完成仓库出库时间：" + DateUtils.getTime() + "]");

            if (baseMapper.updateById(update) <= 0) {
                throw new ServiceException("完成仓库出库失败");
            }
            log.info("[completeOutbound] - 完成成功: {}", outbound.getOutboundCode());

            // TODO: [仓库出库完成后，回调ERP] - 参考文档 docs/design/README_FLOW.md
            notifyUpstreamSystemOnOutboundCompletion(outbound);
            return true;
        } catch (Exception e) {
            log.error("[completeOutbound] - 完成失败, ID: {}", outboundId, e);
            throw new ServiceException("完成仓库出库失败");
        }
    }

    /**
     * 取消仓库出库单
     *
     * @param outboundId   待取消的仓库出库单ID
     * @param cancelReason 取消原因
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelOutbound(Long outboundId, String cancelReason) {
        try {
            Outbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("仓库出库单不存在");
            }

            if (OutboundStatus.CANCELLED == outbound.getOutboundStatus()) {
                throw new ServiceException("仓库出库单已取消，不能重复操作");
            }
            if (OutboundStatus.COMPLETED == outbound.getOutboundStatus()) {
                throw new ServiceException("仓库出库单已完成，不能取消");
            }

            validateOutboundForCancel(outbound);

            outbound.setOutboundStatus(OutboundStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
                outbound.setRemark(currentRemark + " [取消原因：" + cancelReason + "，取消时间：" + DateUtils.getTime() + "]");
            }

            if (baseMapper.updateById(outbound) <= 0) {
                throw new ServiceException("取消仓库出库单失败");
            }

            log.info("[cancelOutbound] - 取消成功: {}, 原因: {}", outbound.getOutboundCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("[cancelOutbound] - 取消失败, ID: {}", outboundId, e);
            throw new ServiceException("取消仓库出库单失败");
        }
    }


    /**
     * 校验仓库出库单是否可以确认或完成
     *
     * @param outboundId 仓库出库单ID
     * @param isComplete 是否为完成操作的校验
     * @return 校验通过的出库单实体
     */
    private R<Outbound> validOutbound(Long outboundId, boolean isComplete) {
        if (outboundId == null) {
            return R.fail("仓库出库单ID不能为空");
        }
        Outbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            return R.fail("仓库出库单不存在");
        }
        List<OutboundItem> outboundItems = queryItemByOutboundId(outboundId);
        if (outboundItems == null || outboundItems.isEmpty()) {
            return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】缺少仓库出库明细");
        }

        for (OutboundItem outboundItem : outboundItems) {
            if (outboundItem.getQuantity() == null || outboundItem.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细通知通知出库数量不能小于等于0");
            }
            if (isComplete) {
                if (outboundItem.getFinishQuantity() == null || outboundItem.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细已拣货数量不能小于等于0");
                }
                if (outboundItem.getQuantity().compareTo(outboundItem.getFinishQuantity()) != 0) {
                    return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细已拣货数量与应出库数量不符");
                }

                if (outboundItem.getBatches() == null || outboundItem.getBatches().isEmpty()) {
                    return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + outboundItem.getProductName() + "】缺少拣货信息");
                }
                BigDecimal totalQuantity = BigDecimal.ZERO;
                for (OutboundItemBatch outboundItemBatch : outboundItem.getBatches()) {
                    if (outboundItemBatch.getQuantity() == null || outboundItemBatch.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + outboundItem.getProductName() + "】拣货数量不能小于等于0");
                    }
                    if (outboundItemBatch.getLocationId() == null) {
                        return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + outboundItem.getProductName() + "】未选择仓库出库位置");
                    }
                    totalQuantity = totalQuantity.add(outboundItemBatch.getQuantity());
                }
                if (!totalQuantity.equals(outboundItem.getFinishQuantity())) {
                    return R.fail("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + outboundItem.getProductName() + "】拣货数量总和与已拣货数量不一致");
                }
            }

        }
        validateInventoryAvailability(outboundItems);
        log.debug("[validOutbound] - 校验通过: {}", outbound.getOutboundCode());
        outbound.setItems(outboundItems);
        return R.ok(outbound);
    }

    /**
     * 校验仓库出库单是否可以取消
     *
     * @param outbound 仓库出库单
     */
    private void validateOutboundForCancel(Outbound outbound) {
        // TODO: [WMS取消出库校验] - 检查是否已有库存变动，若有则需特殊处理
        log.debug("[validateOutboundForCancel] - 取消校验通过: {}", outbound.getOutboundCode());
    }

    /**
     * 仓库出库更新库存记录
     *
     * @param outbound 仓库出库单
     */
    private void updateInventoryRecords(Outbound outbound) {
        try {
            List<OutboundItem> outboundItems = queryItemByOutboundId(outbound.getOutboundId());
            for (OutboundItem outboundItem : outboundItems) {
                for (OutboundItemBatch outboundItemBatch : outboundItem.getBatches()) {
                    InventoryBo inventoryBo = new InventoryBo();
                    inventoryBo.setInventoryId(outboundItemBatch.getInventoryId());
                    inventoryBo.setManagementType(InventoryManagementType.BATCH);
                    inventoryBo.setInternalBatchNumber(outboundItemBatch.getInternalBatchNumber());
                    inventoryBo.setSupplierBatchNumber(outboundItemBatch.getSupplierBatchNumber());
                    inventoryBo.setSerialNumber(outboundItemBatch.getSerialNumber());

                    inventoryBo.setSourceId(outboundItem.getSourceId());
                    inventoryBo.setSourceCode(outboundItem.getSourceCode());
                    inventoryBo.setSourceType(outboundItem.getSourceType());

                    inventoryBo.setDirectSourceId(outboundItem.getDirectSourceId());
                    inventoryBo.setDirectSourceCode(outboundItem.getDirectSourceCode());
                    inventoryBo.setDirectSourceType(outboundItem.getDirectSourceType());
                    inventoryBo.setDirectSourceItemId(outboundItem.getItemId());
                    inventoryBo.setDirectSourceBatchId(outboundItemBatch.getBatchId());

                    inventoryBo.setProductId(outboundItem.getProductId());
                    inventoryBo.setProductCode(outboundItem.getProductCode());
                    inventoryBo.setProductName(outboundItem.getProductName());
                    inventoryBo.setUnitId(outboundItem.getUnitId());
                    inventoryBo.setUnitCode(outboundItem.getUnitCode());
                    inventoryBo.setUnitName(outboundItem.getUnitName());
                    inventoryBo.setLocationId(outboundItemBatch.getLocationId());
                    inventoryBo.setLocationCode(outboundItemBatch.getLocationCode());
                    inventoryBo.setLocationName(outboundItemBatch.getLocationName());
                    inventoryBo.setQuantity(outboundItemBatch.getQuantity().negate());
                    inventoryBo.setCostPrice(outboundItemBatch.getCostPrice());

                    inventoryBo.setExpiryTime(outboundItemBatch.getExpiryTime());
                    inventoryBo.setInventoryTime(outbound.getOutboundTime());

                    inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE);
                    inventoryBo.setRemark("仓库出库单：" + outbound.getOutboundCode() + "-" + outboundItemBatch.getProductName());

                    if (!inventoryService.updateByBo(inventoryBo)) {
                        throw new ServiceException("更新库存失败");
                    }
                    log.debug("[updateInventoryRecords] - 更新库存成功: Product={}, Quantity={}", outboundItem.getProductCode(), inventoryBo.getQuantity());
                }
            }
        } catch (Exception e) {
            log.error("[updateInventoryRecords] - 更新库存记录失败, ID: {}", outbound.getOutboundId(), e);
            throw new ServiceException("仓库出库更新库存记录失败");
        }
    }

    /**
     * 填充冗余字段（暂未使用）
     *
     * @param bo 业务对象
     */
    private void fillRedundantFields(OutboundBo bo) {
        // 该方法用于未来可能的冗余字段填充，当前版本暂无实现
    }

    // TODO: [WMS→ERP状态回传方法实现] - 优先级: HIGH - 参考文档 docs/design/README_FLOW.md

    /**
     * 出库完成事件通知
     *
     * @param outbound 已完成的出库单
     */
    private void notifyUpstreamSystemOnOutboundCompletion(Outbound outbound) {
        try {
            OutboundEvent event = new OutboundEvent();
            event.setOutbound(outbound);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error("[notifyUpstream] - 通知上游系统失败, Code: {}", outbound.getOutboundCode(), e);
        }
    }

    /**
     * 统一处理上游系统通知逻辑
     *
     * @param notifier       通知操作
     * @param systemName     上游系统名称
     * @param directSourceId 直接来源ID
     */
    private void handleUpstreamNotification(java.util.function.Supplier<Boolean> notifier, String systemName, Long directSourceId) {
        try {
            if (notifier.get()) {
                log.info("[notifyUpstream] - 成功通知'{}'状态更新, ID: {}", systemName, directSourceId);
            } else {
                log.error("[notifyUpstream] - 通知'{}'状态更新失败, ID: {}", systemName, directSourceId);
            }
        } catch (Exception e) {
            log.error("[notifyUpstream] - 通知'{}'状态更新异常, ID: {}", systemName, directSourceId, e);
        }
    }
}
