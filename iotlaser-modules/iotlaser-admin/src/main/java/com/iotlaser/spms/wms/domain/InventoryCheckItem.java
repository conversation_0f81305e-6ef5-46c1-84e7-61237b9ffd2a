package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 库存盘点明细对象 wms_inventory_check_item
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_inventory_check_item")
public class InventoryCheckItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 账面数量
     */
    private BigDecimal bookQuantity;

    /**
     * 实盘数量
     */
    private BigDecimal actualQuantity;

    /**
     * 盈亏数量
     */
    private BigDecimal differenceQuantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 盈亏金额
     */
    private BigDecimal differenceAmount;

    /**
     * 盘点状态
     */
    private String itemStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
