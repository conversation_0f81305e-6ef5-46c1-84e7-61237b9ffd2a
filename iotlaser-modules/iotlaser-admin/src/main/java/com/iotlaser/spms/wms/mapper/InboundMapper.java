package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 产品入库Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface InboundMapper extends BaseMapperPlus<Inbound, InboundVo> {

    default Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return exists(new LambdaQueryWrapper<Inbound>().eq(Inbound::getDirectSourceId, directSourceId).eq(Inbound::getDirectSourceType, directSourceType));
    }
}
