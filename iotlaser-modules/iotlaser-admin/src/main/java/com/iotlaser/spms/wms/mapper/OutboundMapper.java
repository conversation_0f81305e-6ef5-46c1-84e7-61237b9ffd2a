package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 产品出库Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface OutboundMapper extends BaseMapperPlus<Outbound, OutboundVo> {
    default Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return exists(new LambdaQueryWrapper<Outbound>().eq(Outbound::getDirectSourceId, directSourceId).eq(Outbound::getDirectSourceType, directSourceType));
    }
}
