package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 采购入库单API接口
 *
 * <AUTHOR> Kai
 * @version 1.2
 * @since 2025-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseInbound")
public class PurchaseInboundController extends BaseController {

    private final IPurchaseInboundService purchaseInboundService;

    /**
     * 查询采购入库列表
     */
    @SaCheckPermission("erp:purchaseInbound:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseInboundVo> list(PurchaseInboundBo bo, PageQuery pageQuery) {
        return purchaseInboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购入库列表
     */
    @SaCheckPermission("erp:purchaseInbound:export")
    @Log(title = "采购入库单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseInboundBo bo, HttpServletResponse response) {
        List<PurchaseInboundVo> list = purchaseInboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购入库", PurchaseInboundVo.class, response);
    }

    /**
     * 获取采购入库详细信息
     *
     * @param inboundId 采购入库单主键
     */
    @SaCheckPermission("erp:purchaseInbound:query")
    @GetMapping("/{inboundId}")
    public R<PurchaseInboundVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long inboundId) {
        return R.ok(purchaseInboundService.queryById(inboundId));
    }

    /**
     * 新增采购入库
     */
    @SaCheckPermission("erp:purchaseInbound:add")
    @Log(title = "采购入库单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<PurchaseInboundVo> add(@Validated(AddGroup.class) @RequestBody PurchaseInboundBo bo) {
        return R.ok(purchaseInboundService.insertByBo(bo));
    }

    /**
     * 修改采购入库
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<PurchaseInboundVo> edit(@Validated(EditGroup.class) @RequestBody PurchaseInboundBo bo) {
        return R.ok(purchaseInboundService.updateByBo(bo));
    }

    /**
     * 删除采购入库
     *
     * @param inboundIds 待删除的采购入库单ID串
     */
    @SaCheckPermission("erp:purchaseInbound:remove")
    @Log(title = "采购入库单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] inboundIds) {
        return toAjax(purchaseInboundService.deleteWithValidByIds(List.of(inboundIds), true));
    }

    /**
     * 确认采购入库单
     *
     * @param inboundId 采购入库单ID
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{inboundId}")
    public R<Void> confirm(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.confirmInbound(inboundId));
    }

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 采购入库单ID数组
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "入库单ID不能为空") @RequestBody Long[] inboundIds) {
        return toAjax(purchaseInboundService.batchConfirmInbounds(Arrays.asList(inboundIds)));
    }

    /**
     * 完成采购入库
     *
     * @param inboundId 采购入库单ID
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{inboundId}")
    public R<Void> complete(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.completeInbound(inboundId));
    }

    /**
     * 创建仓库入库单
     *
     * @param inboundId 采购入库ID
     */
    @SaCheckPermission("erp:purchaseInbound:edit")
    @Log(title = "采购入库单", businessType = BusinessType.INSERT)
    @PostMapping("/createInbound/{inboundId}")
    public R<Void> createInbound(@NotNull(message = "入库ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.createInbound(inboundId));
    }

    /**
     * 创建采购退货单
     *
     * @param inboundId 采购入库ID
     */
    @SaCheckPermission("erp:purchaseReturn:add")
    @Log(title = "采购入库单", businessType = BusinessType.INSERT)
    @PostMapping("/createPurchaseReturn/{inboundId}")
    public R<Void> createPurchaseReturn(@NotNull(message = "入库ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.createPurchaseReturn(inboundId));
    }

    /**
     * 创建财务应付单
     *
     * @param inboundId 采购入库ID
     */
    @SaCheckPermission("erp:purchaseReturn:add")
    @Log(title = "采购入库单", businessType = BusinessType.INSERT)
    @PostMapping("/createFinApInvoice/{inboundId}")
    public R<Void> createFinApInvoice(@NotNull(message = "入库ID不能为空") @PathVariable Long inboundId) {
        return toAjax(purchaseInboundService.createFinApInvoice(inboundId));
    }

    // TODO: [采购入库批量操作接口] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
    // 需要添加批量操作接口：
    // 批量确认: POST /batchConfirm - 批量确认多个入库单
    // 批量完成: POST /batchComplete - 批量完成多个入库单
    // 批量取消: POST /batchCancel - 批量取消多个入库单
    // 实现思路：接收入库单ID列表，循环调用单个操作方法，支持部分成功的结果返回

    // TODO: [采购入库质检集成接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加质检相关接口：
    // 创建质检单: POST /createQualityCheck/{inboundId} - 为入库单创建质检单
    // 质检完成回调: POST /qualityCheckComplete/{inboundId} - 质检完成后的回调处理
    // 实现思路：与 QMS 质量管理模块集成，支持入库质检流程

}
