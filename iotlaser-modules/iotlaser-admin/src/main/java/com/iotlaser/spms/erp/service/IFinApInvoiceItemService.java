package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.FinApInvoiceItem;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 应付单明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinApInvoiceItemService {

    /**
     * 查询应付明细
     *
     * @param itemId 主键
     * @return 应付明细
     */
    FinApInvoiceItemVo queryById(Long itemId);

    /**
     * 分页查询应付明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应付明细分页列表
     */
    TableDataInfo<FinApInvoiceItemVo> queryPageList(FinApInvoiceItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应付明细列表
     *
     * @param bo 查询条件
     * @return 应付明细列表
     */
    List<FinApInvoiceItemVo> queryList(FinApInvoiceItemBo bo);

    /**
     * 新增应付明细
     *
     * @param bo 应付明细
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApInvoiceItemBo bo);

    /**
     * 修改应付明细
     *
     * @param bo 应付明细
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApInvoiceItemBo bo);

    /**
     * 校验并批量删除应付明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据发票ID获取明细列表
     *
     * @param invoiceId 发票ID
     * @return 明细列表
     */
    List<FinApInvoiceItem> getItemsByInvoiceId(Long invoiceId);

    /**
     * 检查是否存在发票明细
     *
     * @param invoiceId 发票ID
     * @return 是否存在明细
     */
    Boolean existsByInvoiceId(Long invoiceId);

    /**
     * 根据发票ID获取明细ID列表
     *
     * @param invoiceId 发票ID
     * @return 明细ID列表
     */
    List<Long> getItemIdsByInvoiceId(Long invoiceId);
}
