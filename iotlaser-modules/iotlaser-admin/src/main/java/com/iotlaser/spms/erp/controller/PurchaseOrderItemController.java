package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.service.IPurchaseOrderItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购订单明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseOrderItem")
public class PurchaseOrderItemController extends BaseController {

    private final IPurchaseOrderItemService purchaseOrderItemService;

    /**
     * 查询采购订单明细列表
     */
    @SaCheckPermission("erp:purchaseOrderItem:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseOrderItemVo> list(PurchaseOrderItemBo bo, PageQuery pageQuery) {
        return purchaseOrderItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购订单明细列表
     */
    @SaCheckPermission("erp:purchaseOrderItem:export")
    @Log(title = "采购订单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseOrderItemBo bo, HttpServletResponse response) {
        List<PurchaseOrderItemVo> list = purchaseOrderItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购订单明细", PurchaseOrderItemVo.class, response);
    }

    /**
     * 获取采购订单明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:purchaseOrderItem:query")
    @GetMapping("/{itemId}")
    public R<PurchaseOrderItemVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long itemId) {
        return R.ok(purchaseOrderItemService.queryById(itemId));
    }

    /**
     * 新增采购订单明细
     */
    @SaCheckPermission("erp:purchaseOrderItem:add")
    @Log(title = "采购订单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<PurchaseOrderItemBo> bos) {
        return toAjax(purchaseOrderItemService.insertOrUpdateBatch(bos));
    }

    /**
     * 新增采购订单明细
     */
    @SaCheckPermission("erp:purchaseOrderItem:add")
    @Log(title = "采购订单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseOrderItemBo bo) {
        return toAjax(purchaseOrderItemService.insertByBo(bo));
    }

    /**
     * 修改采购订单明细
     */
    @SaCheckPermission("erp:purchaseOrderItem:edit")
    @Log(title = "采购订单明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseOrderItemBo bo) {
        return toAjax(purchaseOrderItemService.updateByBo(bo));
    }

    /**
     * 删除采购订单明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:purchaseOrderItem:remove")
    @Log(title = "采购订单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(purchaseOrderItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

}
