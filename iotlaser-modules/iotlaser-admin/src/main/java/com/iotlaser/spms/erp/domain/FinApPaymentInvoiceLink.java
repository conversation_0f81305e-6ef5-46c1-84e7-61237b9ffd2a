package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 付款单与应付单核销关系对象 erp_fin_ap_payment_invoice_link
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ap_payment_invoice_link")
public class FinApPaymentInvoiceLink extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @TableId(value = "link_id")
    private Long linkId;

    /**
     * 付款ID
     */
    private Long paymentId;

    /**
     * 应付ID
     */
    private Long invoiceId;

    /**
     * 核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
