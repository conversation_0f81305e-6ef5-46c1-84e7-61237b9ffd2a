package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionOrder;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产订单业务对象 mes_production_order
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionOrder.class, reverseConvertGenerate = false)
public class ProductionOrderBo extends BaseEntity {

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 生产订单编码
     */
    @NotBlank(message = "生产订单编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderCode;

    /**
     * 生产订单类型
     */
    @NotBlank(message = "生产订单类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderType;

    /**
     * 源头单据ID (整个业务流程的最初发起单据)
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头单据编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头单据类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游单据ID (当前单据的直接创建来源单据)
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游单据编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游单据类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * BOMID
     */
    @NotNull(message = "BOMID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 计划生产数量
     */
    @NotNull(message = "计划生产数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已完工入库数量
     */
    private BigDecimal finishQuantity;

    /**
     * 计划开始
     */
    @NotNull(message = "计划开始不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDate plannedStartDate;

    /**
     * 计划结束
     */
    @NotNull(message = "计划结束不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDate plannedEndDate;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 订单状态
     */
    @NotBlank(message = "订单状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private ProductionOrderStatus orderStatus;

    /**
     * 计划员ID
     */
    private Long plannerId;

    /**
     * 计划员
     */
    private String plannerName;

    /**
     * 车间主管 ID
     */
    private Long supervisorId;

    /**
     * 车间主管
     */
    private String supervisorName;

    /**
     * 工单下达时间
     */
    private LocalDateTime releaseTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
