package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinArReceivableItem;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableItemBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableItemVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.mapper.FinArReceivableItemMapper;
import com.iotlaser.spms.erp.service.IFinArReceivableItemService;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 应收单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceivableItemServiceImpl implements IFinArReceivableItemService {

    private final FinArReceivableItemMapper baseMapper;
    @Lazy
    @Autowired
    private IFinArReceivableService finArReceivableService;

    /**
     * 查询应收单明细
     *
     * @param itemId 主键
     * @return 应收单明细
     */
    @Override
    public FinArReceivableItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询应收单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收单明细分页列表
     */
    @Override
    public TableDataInfo<FinArReceivableItemVo> queryPageList(FinArReceivableItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceivableItem> lqw = buildQueryWrapper(bo);
        Page<FinArReceivableItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应收单明细列表
     *
     * @param bo 查询条件
     * @return 应收单明细列表
     */
    @Override
    public List<FinArReceivableItemVo> queryList(FinArReceivableItemBo bo) {
        LambdaQueryWrapper<FinArReceivableItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceivableItem> buildQueryWrapper(FinArReceivableItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceivableItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinArReceivableItem::getItemId);
        lqw.eq(bo.getReceivableId() != null, FinArReceivableItem::getReceivableId, bo.getReceivableId());
        lqw.eq(bo.getSourceId() != null, FinArReceivableItem::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), FinArReceivableItem::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(FinArReceivableItem::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, FinArReceivableItem::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), FinArReceivableItem::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(FinArReceivableItem::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getDirectSourceItemId() != null, FinArReceivableItem::getDirectSourceItemId, bo.getDirectSourceItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), FinArReceivableItem::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(bo.getProductId() != null, FinArReceivableItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), FinArReceivableItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), FinArReceivableItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, FinArReceivableItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), FinArReceivableItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), FinArReceivableItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getQuantity() != null, FinArReceivableItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPrice() != null, FinArReceivableItem::getPrice, bo.getPrice());
        lqw.eq(bo.getPriceExclusiveTax() != null, FinArReceivableItem::getPriceExclusiveTax, bo.getPriceExclusiveTax());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinArReceivableItem::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxRate() != null, FinArReceivableItem::getTaxRate, bo.getTaxRate());
        lqw.eq(bo.getTaxAmount() != null, FinArReceivableItem::getTaxAmount, bo.getTaxAmount());
        lqw.eq(bo.getAmount() != null, FinArReceivableItem::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceivableItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增应收单明细
     *
     * @param bo 应收单明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinArReceivableItemBo bo) {
        // 计算价税分离字段
        calculateTaxFields(bo);

        FinArReceivableItem add = MapstructUtils.convert(bo, FinArReceivableItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改应收单明细
     *
     * @param bo 应收单明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinArReceivableItemBo bo) {
        // 重新计算价税分离字段
        calculateTaxFields(bo);

        FinArReceivableItem update = MapstructUtils.convert(bo, FinArReceivableItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceivableItem entity) {
        // 数据校验：检查唯一约束和必填字段
        validateUniqueConstraint(entity);
        validateRequiredFields(entity);
    }

    /**
     * 校验并批量删除应收单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验应收单明细是否可以删除
            List<FinArReceivableItem> items = baseMapper.selectByIds(ids);
            for (FinArReceivableItem item : items) {
                // TODO: 检查主表状态，只有草稿状态的应收单明细才能删除
                FinArReceivableVo receivable = finArReceivableService.queryById(item.getReceivableId());
                if (receivable != null && !"PENDING".equals(receivable.getReceivableStatus())) {
                    throw new ServiceException("应收单明细所属应收单【" + receivable.getReceivableCode() +
                        "】状态为【" + receivable.getReceivableStatus() + "】，不允许删除明细");
                }

                log.info("删除应收单明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除应收单明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除应收单明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除应收单明细失败：" + e.getMessage());
        }
    }

    /**
     * 计算价税分离字段
     *
     * @param bo 业务对象
     */
    private void calculateTaxFields(FinArReceivableItemBo bo) {
        // 校验必要字段
        if (bo.getQuantity() == null || bo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }

        if (bo.getTaxRate() == null) {
            bo.setTaxRate(BigDecimal.ZERO); // 默认税率为0
        }

        // 校验税率范围 (0-100%)
        if (bo.getTaxRate().compareTo(BigDecimal.ZERO) < 0 ||
            bo.getTaxRate().compareTo(new BigDecimal("100")) > 0) {
            throw new ServiceException("税率必须在0-100%之间");
        }

        // 根据输入的字段计算其他字段
        if (bo.getPriceExclusiveTax() != null) {
            // 基于单价(不含税)计算
            calculateFromPriceExclusiveTax(bo);
        } else if (bo.getPrice() != null) {
            // 基于单价(含税)计算
            calculateFromPrice(bo);
        } else if (bo.getAmountExclusiveTax() != null) {
            // 基于金额(不含税)计算
            calculateFromAmountExclusiveTax(bo);
        } else if (bo.getAmount() != null) {
            // 基于金额(含税)计算
            calculateFromTotalAmount(bo);
        } else {
            throw new ServiceException("必须提供单价或金额信息");
        }
    }

    /**
     * 基于单价(不含税)计算其他字段
     */
    private void calculateFromPriceExclusiveTax(FinArReceivableItemBo bo) {
        BigDecimal priceExclusiveTax = bo.getPriceExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额(不含税) = 单价(不含税) × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额(不含税) × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算单价(含税) = 单价(不含税) × (1 + 税率)
        BigDecimal price = priceExclusiveTax.multiply(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额(含税) = 金额(不含税) + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);
    }

    /**
     * 基于单价(含税)计算其他字段
     */
    private void calculateFromPrice(FinArReceivableItemBo bo) {
        BigDecimal price = bo.getPrice();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算金额(含税) = 单价(含税) × 数量
        BigDecimal amount = price.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmount(amount);

        // 计算单价(不含税) = 单价(含税) ÷ (1 + 税率)
        BigDecimal priceExclusiveTax = price.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额(不含税) = 单价(不含税) × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity)
            .setScale(2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额(含税) - 金额(不含税)
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }

    /**
     * 基于金额(不含税)计算其他字段
     */
    private void calculateFromAmountExclusiveTax(FinArReceivableItemBo bo) {
        BigDecimal amountExclusiveTax = bo.getAmountExclusiveTax();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价(不含税) = 金额(不含税) ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额(不含税) × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.HALF_UP);
        bo.setTaxAmount(taxAmount);

        // 计算金额(含税) = 金额(不含税) + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        bo.setAmount(amount);

        // 计算单价(含税) = 金额(含税) ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);
    }

    /**
     * 基于金额(含税)计算其他字段
     */
    private void calculateFromTotalAmount(FinArReceivableItemBo bo) {
        BigDecimal amount = bo.getAmount();
        BigDecimal quantity = bo.getQuantity();
        BigDecimal taxRate = bo.getTaxRate();

        // 计算单价(含税) = 金额(含税) ÷ 数量
        BigDecimal price = amount.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPrice(price);

        // 计算金额(不含税) = 金额(含税) ÷ (1 + 税率)
        BigDecimal amountExclusiveTax = amount.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);
        bo.setAmountExclusiveTax(amountExclusiveTax);

        // 计算单价(不含税) = 金额(不含税) ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(quantity, 2, RoundingMode.HALF_UP);
        bo.setPriceExclusiveTax(priceExclusiveTax);

        // 计算税额 = 金额(含税) - 金额(不含税)
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        bo.setTaxAmount(taxAmount);
    }

    /**
     * 校验唯一约束
     *
     * @param entity 实体对象
     */
    private void validateUniqueConstraint(FinArReceivableItem entity) {
        // 检查同一应收单下的产品是否重复
        LambdaQueryWrapper<FinArReceivableItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceivableItem::getReceivableId, entity.getReceivableId());
        wrapper.eq(FinArReceivableItem::getProductId, entity.getProductId());

        // 如果是更新操作，排除当前记录
        if (entity.getItemId() != null) {
            wrapper.ne(FinArReceivableItem::getItemId, entity.getItemId());
        }

        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("该应收单中已存在相同产品的明细记录");
        }
    }

    /**
     * 校验必填字段
     *
     * @param entity 实体对象
     */
    private void validateRequiredFields(FinArReceivableItem entity) {
        if (entity.getReceivableId() == null) {
            throw new ServiceException("应收单ID不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品ID不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }
    }

    /**
     * 根据应收单ID获取明细列表
     *
     * @param receivableId 应收单ID
     * @return 明细列表
     */
    @Override
    public List<FinArReceivableItem> getItemsByReceivableId(Long receivableId) {
        LambdaQueryWrapper<FinArReceivableItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinArReceivableItem::getReceivableId, receivableId);
        wrapper.orderByAsc(FinArReceivableItem::getItemId);
        return baseMapper.selectList(wrapper);
    }
}
