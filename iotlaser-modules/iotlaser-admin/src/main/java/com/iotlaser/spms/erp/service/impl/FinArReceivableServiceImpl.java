package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.bo.FinArReceivableBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.enums.FinArReceivableStatus;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.event.SaleOutboundEvent;
import com.iotlaser.spms.erp.mapper.FinArReceiptReceivableLinkMapper;
import com.iotlaser.spms.erp.mapper.FinArReceivableItemMapper;
import com.iotlaser.spms.erp.mapper.FinArReceivableMapper;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_FIN_AR_RECEIVABLE_CODE;

/**
 * 应收单服务实现 (Account Receivable Service Implementation)
 * <p>
 * 核心职责: 作为应收单聚合根的管理者，本服务封装了应收单从创建到核销完成的全生命周期业务规则。
 * 它确保了所有操作的原子性、一致性和业务逻辑的正确性，是财务应收模块的标准实现。
 * <p>
 * 主要功能:
 * <ul>
 *     <li><b>生命周期管理</b>: 负责应收单的创建 ({@link #insertByBo}), 更新 ({@link #updateByBo}), 删除 ({@link #deleteWithValidByIds})。</li>
 *     <li><b>状态流转</b>: 控制订单状态的合法转换，如确认 ({@link #confirmReceivable}), 取消 ({@link #cancelReceivable})。</li>
 *     <li><b>业务协同</b>: 响应上游（如销售出库）事件，自动创建应收单 ({@link #createFromSaleOutbound})。</li>
 *     <li><b>数据一致性</b>: 通过回写机制 ({@link #updateAppliedAmountAndStatus})，在被核销后保持与收款单的数据同步。</li>
 *     <li><b>查询服务</b>: 提供丰富的查询接口 ({@link #queryById}, {@link #queryPageList})。</li>
 * </ul>
 *
 * <AUTHOR> Kai
 * @version 1.1
 * @see com.iotlaser.spms.erp.service.IFinArReceivableService
 * @see com.iotlaser.spms.erp.domain.FinArReceivable
 * @see docs/design/README_FINANCE.md#2-销售与应收-order-to-cash
 * @see docs/design/README_STATE.md#74-应收单-erpfinarreceivable
 * @since 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceivableServiceImpl implements IFinArReceivableService {
    // =================================================================================================================
    // [DDD-TODO] 跨聚合调用优化 - 目标: 实现最终一致性，降低服务间耦合
    // 当前为保持业务流程的同步与完整性，暂时直接依赖其他聚合根的Service。
    // 理想架构应通过领域事件(Domain Event)或应用服务层(Application Service)进行解耦。
    // 例如：核销完成后，发布`ReceivableAppliedEvent`，由总账上下文的监听器异步订阅并创建流水。
    // -----------------------------------------------------------------------------------------------------------------
    // 参考文档: docs/design/README_OVERVIEW.md
    // TODO: [REFACTOR] - [HIGH] - 将此处的服务直接依赖重构为领域事件模式。
    // =================================================================================================================
    private final FinArReceivableMapper baseMapper;
    private final FinArReceivableItemMapper itemMapper;
    private FinArReceiptReceivableLinkMapper linkMapper;
    private final Gen gen;
    private final ICompanyService companyService;

    /**
     * {@inheritDoc}
     */
    public FinArReceivableVo queryById(Long receivableId) {
        return baseMapper.selectVoById(receivableId);
    }

    /**
     * {@inheritDoc}
     */
    public TableDataInfo<FinArReceivableVo> queryPageList(FinArReceivableBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceivable> lqw = buildQueryWrapper(bo);
        Page<FinArReceivableVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    public List<FinArReceivableVo> queryList(FinArReceivableBo bo) {
        LambdaQueryWrapper<FinArReceivable> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceivable> buildQueryWrapper(FinArReceivableBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceivable> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(FinArReceivable::getReceivableId);

        lqw.eq(StringUtils.isNotBlank(bo.getReceivableCode()), FinArReceivable::getReceivableCode, bo.getReceivableCode());
        lqw.eq(bo.getCustomerId() != null, FinArReceivable::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), FinArReceivable::getCustomerName, bo.getCustomerName());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), FinArReceivable::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getInvoiceDate() != null, FinArReceivable::getInvoiceDate, bo.getInvoiceDate());
        if (bo.getReceivableStatus() != null) {
            lqw.eq(FinArReceivable::getReceivableStatus, bo.getReceivableStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceivable::getStatus, bo.getStatus());
        // 范围查询
        lqw.between(params.get("beginInvoiceDate") != null && params.get("endInvoiceDate") != null,
            FinArReceivable::getInvoiceDate, params.get("beginInvoiceDate"), params.get("endInvoiceDate"));
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinArReceivableVo insertByBo(FinArReceivableBo bo) {
        final String currentUser = LoginHelper.getUsername();
        log.info("[insertByBo] - 开始. 操作人: {}, 请求参数: {}", currentUser, bo);

        try {
            // 1. 初始化编码和日期
            if (StringUtils.isEmpty(bo.getReceivableCode())) {
                bo.setReceivableCode(gen.code(ERP_FIN_AR_RECEIVABLE_CODE));
            }
            if (bo.getInvoiceDate() == null) {
                bo.setInvoiceDate(LocalDate.now());
            }
            // 2. 设置初始状态为草稿
            bo.setReceivableStatus(FinArReceivableStatus.DRAFT);
            // 3. 填充客户名称等冗余字段
            fillRedundantFields(bo);
            // 4. 转换为实体并进行保存前校验
            FinArReceivable add = MapstructUtils.convert(bo, FinArReceivable.class);
            validEntityBeforeSave(add);
            // 5. 插入主表记录
            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                log.error("[insertByBo] - 失败. 数据库插入返回false. 操作人: {}, 数据: {}", currentUser, add);
                throw new ServiceException("创建应收单失败，数据库操作异常");
            }

            // 6. 回填单据来源信息
            if (add.getSourceType() == null) { // 如果是手工创建
                add.setSourceId(add.getReceivableId());
                add.setSourceCode(add.getReceivableCode());
                add.setSourceType(SourceType.RECEIVABLE);
            }
            if (add.getDirectSourceType() == null) {
                add.setDirectSourceId(add.getReceivableId());
                add.setDirectSourceCode(add.getReceivableCode());
                add.setDirectSourceType(DirectSourceType.RECEIVABLE);
            }
            baseMapper.updateById(add);

            log.info("[insertByBo] - 成功. 操作人: {}, 单号: {}, ID: {}", currentUser, add.getReceivableCode(), add.getReceivableId());
            return MapstructUtils.convert(add, FinArReceivableVo.class);
        } catch (ServiceException se) {
            log.warn("[insertByBo] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[insertByBo] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("新增应收单时发生未知系统错误");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinArReceivableVo updateByBo(FinArReceivableBo bo) {
        final String currentUser = LoginHelper.getUsername();
        log.info("[updateByBo] - 开始. 操作人: {}, ID: {}", currentUser, bo.getReceivableId());

        try {
            // 1. 校验单据是否存在且状态是否允许修改
            FinArReceivable existing = baseMapper.selectById(bo.getReceivableId());
            if (existing == null) {
                throw new ServiceException("修改失败：ID为 [" + bo.getReceivableId() + "] 的应收单不存在");
            }
            if (existing.getReceivableStatus() != FinArReceivableStatus.DRAFT) {
                throw new ServiceException("修改失败：只有草稿状态的应收单才能修改");
            }
            // 2. 填充冗余字段
            fillRedundantFields(bo);
            // 3. 转换为实体并校验
            FinArReceivable update = MapstructUtils.convert(bo, FinArReceivable.class);
            validEntityBeforeSave(update);
            // 5. 更新数据库
            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("[updateByBo] - 成功. 操作人: {}, 单号: {}", currentUser, update.getReceivableCode());
            }
            return MapstructUtils.convert(update, FinArReceivableVo.class);
        } catch (ServiceException se) {
            log.warn("[updateByBo] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getReceivableId(), se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[updateByBo] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getReceivableId(), e.getMessage(), e);
            throw new ServiceException("修改应收单时发生未知系统错误");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceivable entity) {
        // 校验应收编号唯一性
        if (StringUtils.isNotBlank(entity.getReceivableCode())) {
            LambdaQueryWrapper<FinArReceivable> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceivable::getReceivableCode, entity.getReceivableCode());
            if (entity.getReceivableId() != null) {
                wrapper.ne(FinArReceivable::getReceivableId, entity.getReceivableId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("操作失败：应收单编码 [" + entity.getReceivableCode() + "] 已被使用");
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        final String currentUser = LoginHelper.getUsername();
        log.info("[deleteWithValidByIds] - 开始. 操作人: {}, 请求ID: {}, 是否校验: {}", currentUser, ids, isValid);

        try {
            if (isValid) {
                List<FinArReceivable> receivables = baseMapper.selectByIds(ids);
                for (FinArReceivable receivable : receivables) {
                    if (receivable.getReceivableStatus() != FinArReceivableStatus.DRAFT) {
                        throw new ServiceException("删除失败：应收单【" + receivable.getReceivableCode() + "】状态为“" + receivable.getReceivableStatus().getDesc() + "”，仅草稿状态可删除");
                    }
                    if (linkMapper.existsByReceivableId(receivable.getReceivableId())) {
                        throw new ServiceException("删除失败：应收单【" + receivable.getReceivableCode() + "】存在收款核销记录，不允许删除");
                    }
                }
            }
            boolean itemResult = itemMapper.deleteByReceivableIds(ids) > 0;
            if (itemResult) {
                log.info("[deleteWithValidByIds] - 成功删除应收单明细。操作人: {}, 删除明细数: {}", currentUser, itemResult);
            } else {
                log.warn("[deleteWithValidByIds] - 警告. 删除的应收单ID列表: {}", ids);
            }

            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("[deleteWithValidByIds] - 成功. 操作人: {}, 删除ID列表: {}", currentUser, ids);
            } else {
                log.warn("[deleteWithValidByIds] - 警告. 数据库操作未影响任何行。操作人: {}", currentUser);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("[deleteWithValidByIds] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("删除应收单时发生未知系统错误");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId) {
        LambdaQueryWrapper<FinArReceivable> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinArReceivable::getDirectSourceId, directSourceId);
        return baseMapper.exists(lqw);
    }

    /**
     * {@inheritDoc}
     */
    public List<FinArReceivableVo> queryBySourceId(Long sourceId) {
        LambdaQueryWrapper<FinArReceivable> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinArReceivable::getSourceId, sourceId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * {@inheritDoc}
     */
    public List<FinArReceivableVo> queryByDirectSourceId(Long directSourceId) {
        LambdaQueryWrapper<FinArReceivable> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinArReceivable::getDirectSourceId, directSourceId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Boolean updateReceivableStatus(Long receivableId, String newStatus) {
        //TODO
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long generateReceiptOrderFromReceivable(Long receivableId, BigDecimal receiptAmount, Long accountId, Long operatorId, String operatorName) {
        //TODO
        return 0L;
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmReceivable(Long receivableId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【应收单】[确认] - 开始. 操作人: {}, ID: {}", currentUser, receivableId);

        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("确认失败：ID为 [" + receivableId + "] 的应收单不存在");
            }
            if (receivable.getReceivableStatus() != FinArReceivableStatus.DRAFT) {
                throw new ServiceException("确认失败：应收单 [" + receivable.getReceivableCode() + "] 状态为“" + receivable.getReceivableStatus().getDesc() + "”，仅草稿状态可确认");
            }

            // 更新状态
            FinArReceivable update = new FinArReceivable();
            update.setReceivableId(receivableId);
            update.setReceivableStatus(FinArReceivableStatus.UNPAID); // 确认后进入“未支付”状态
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【应收单】[确认] - 成功. 操作人: {}, 单号: {}", currentUser, receivable.getReceivableCode());
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【应收单】[确认] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, receivableId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【应收单】[确认] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, receivableId, e.getMessage(), e);
            throw new ServiceException("确认应收单时发生未知系统错误");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReceivable(Long receivableId, String reason) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【应收单】[取消] - 开始. 操作人: {}, ID: {}, 原因: {}", currentUser, receivableId, reason);

        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("取消失败：ID为 [" + receivableId + "] 的应收单不存在");
            }

            // 只有草稿和未支付状态可以取消
            if (receivable.getReceivableStatus() != FinArReceivableStatus.DRAFT && receivable.getReceivableStatus() != FinArReceivableStatus.UNPAID) {
                throw new ServiceException("取消失败：应收单 [" + receivable.getReceivableCode() + "] 状态为“" + receivable.getReceivableStatus().getDesc() + "”，无法取消");
            }
            // 如果已核销，则不能取消
            if (receivable.getReceivedAmount() != null && receivable.getReceivedAmount().compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("取消失败：应收单 [" + receivable.getReceivableCode() + "] 已有核销记录，无法取消");
            }

            // 更新状态和备注
            FinArReceivable update = new FinArReceivable();
            update.setReceivableId(receivableId);
            update.setReceivableStatus(FinArReceivableStatus.CANCELLED);
            if (StringUtils.isNotBlank(reason)) {
                String newRemark = StringUtils.isNotBlank(receivable.getRemark()) ? receivable.getRemark() + " | " : "";
                newRemark += "取消原因: " + reason;
                update.setRemark(newRemark);
            }
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【应收单】[取消] - 成功. 操作人: {}, 单号: {}", currentUser, receivable.getReceivableCode());
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【应收单】[取消] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, receivableId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【应收单】[取消] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, receivableId, e.getMessage(), e);
            throw new ServiceException("取消应收单时发生未知系统错误");
        }
    }


    // ==================== 应收发票明细处理方法（TODO） ====================

    /**
     * 校验应收发票明细与主表金额一致性
     * TODO: 当前方法为框架方法，待应收发票明细表创建后完善实现
     *
     * @param receivableId 应收单ID
     * @return 是否一致
     */
    private boolean validateReceivableItemsConsistency(Long receivableId) {
        try {
            // TODO: 实现明细与主表一致性校验
            // 获取应收单主表金额
            // FinArReceivable receivable = baseMapper.selectById(receivableId);
            //
            // 计算明细汇总金额
            // List<FinArReceivableItem> items = receivableItemService.getItemsByReceivableId(receivableId);
            // BigDecimal itemsTotal = items.stream()
            //    .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            //    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //
            // 比较金额差异
            // BigDecimal difference = receivable.getAmount().subtract(itemsTotal).abs();
            // boolean isConsistent = difference.compareTo(new BigDecimal("0.01")) <= 0;
            //
            // if (!isConsistent) {
            //    log.warn("应收发票金额不一致 - 应收单: {}, 主表金额: {}, 明细汇总: {}, 差异: {}",
            //        receivable.getReceivableCode(), receivable.getAmount(), itemsTotal, difference);
            // }
            //
            // return isConsistent;

            log.debug("应收发票明细一致性校验 - 应收单ID: {} (当前为TODO实现，返回true)", receivableId);
            return true; // 临时返回true

        } catch (Exception e) {
            log.error("校验应收发票明细一致性失败 - 应收单ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateAppliedAmountAndStatus(Long receivableId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【应收单】[更新核销金额与状态] - 开始. 操作人: {}, ID: {}", currentUser, receivableId);

        try {
            FinArReceivable receivable = baseMapper.selectById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在: " + receivableId);
            }

            // 1. 从核销关联表汇总已核销金额
            BigDecimal totalAppliedAmount = linkMapper.getAppliedAmountByReceivableId(receivableId);
            if (totalAppliedAmount == null) {
                totalAppliedAmount = BigDecimal.ZERO;
            }
            BigDecimal outstandingAmount = receivable.getAmount().subtract(totalAppliedAmount);

            // 2. 根据金额确定新状态
            FinArReceivableStatus newStatus = determineReceivableStatusAfterPayment(receivable.getAmount(), totalAppliedAmount);

            // 3. 如果状态或金额有变化，则更新
            if (newStatus != receivable.getReceivableStatus() || receivable.getReceivedAmount().compareTo(totalAppliedAmount) != 0) {
                FinArReceivable update = new FinArReceivable();
                update.setReceivableId(receivableId);
                update.setReceivableStatus(newStatus);
                update.setReceivedAmount(totalAppliedAmount);
                update.setOutstandingAmount(outstandingAmount);

                boolean result = baseMapper.updateById(update) > 0;
                if (!result) {
                    throw new ServiceException("更新应收单状态和金额失败");
                }
                log.info("【应收单】[更新核销金额与状态] - 成功. ID: {}, 状态由 [{}] -> [{}], 已核销: {}",
                    receivableId, receivable.getReceivableStatus().getDesc(), newStatus.getDesc(), totalAppliedAmount);
            } else {
                log.info("【应收单】[更新核销金额与状态] - 无需更新. ID: {}, 状态与金额未发生变化。", receivableId);
            }
            return true;
        } catch (ServiceException se) {
            log.warn("【应收单】[更新核销金额与状态] - 业务异常. ID: {}, 错误: {}", receivableId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【应收单】[更新核销金额与状态] - 系统异常. ID: {}, 错误: {}", receivableId, e.getMessage(), e);
            throw new ServiceException("更新应收单状态失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromSaleOutbound(SaleOutbound saleOutbound) {
        try {
            if (SaleOutboundStatus.COMPLETED != saleOutbound.getOutboundStatus()) {
                throw new ServiceException("创建失败：销售出库单状态 [" + saleOutbound.getOutboundStatus().getName() + "] ，不能创建");
            }
            if (existsByDirectSourceId(saleOutbound.getOutboundId())) {
                throw new ServiceException("创建失败：销售出库单 [" + saleOutbound.getOutboundCode() + "] 已生成过应收单，不能重复创建");
            }
            FinArReceivable add = new FinArReceivable();
            add.setSourceId(saleOutbound.getSourceId());
            add.setSourceCode(saleOutbound.getSourceCode());
            add.setSourceType(saleOutbound.getSourceType());
            add.setDirectSourceId(saleOutbound.getOutboundId());
            add.setDirectSourceCode(saleOutbound.getOutboundCode());
            add.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);
            add.setCustomerId(saleOutbound.getCustomerId());
            add.setCustomerName(saleOutbound.getCustomerName());
            add.setAmountExclusiveTax(saleOutbound.getAmountExclusiveTax());
            add.setTaxAmount(saleOutbound.getTaxAmount());
            add.setAmount(saleOutbound.getAmount());
            add.setReceivedAmount(BigDecimal.ZERO);
            add.setOutstandingAmount(saleOutbound.getAmount());
            add.setReceivableStatus(FinArReceivableStatus.DRAFT);
            add.setSummary("[销售出库单" + saleOutbound.getOutboundCode() + "]");

            // 3. 插入主表
            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                throw new ServiceException("应收单主表生成失败");
            }

            // 4. 批量插入明细
            List<FinArReceivableItem> items = new ArrayList<>();
            for (SaleOutboundItem saleOutboundItem : saleOutbound.getItems()) {
                FinArReceivableItem item = new FinArReceivableItem();
                item.setReceivableId(add.getReceivableId());

                item.setSourceId(saleOutbound.getSourceId());
                item.setSourceCode(saleOutbound.getSourceCode());
                item.setSourceType(saleOutbound.getSourceType());
                item.setDirectSourceId(saleOutbound.getOutboundId());
                item.setDirectSourceCode(saleOutbound.getOutboundCode());
                item.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);

                item.setDirectSourceItemId(saleOutboundItem.getItemId());

                item.setProductId(saleOutboundItem.getProductId());
                item.setProductCode(saleOutboundItem.getProductCode());
                item.setProductName(saleOutboundItem.getProductName());
                item.setUnitId(saleOutboundItem.getUnitId());
                item.setUnitCode(saleOutboundItem.getUnitCode());
                item.setUnitName(saleOutboundItem.getUnitName());

                item.setPrice(saleOutboundItem.getPrice());
                item.setPriceExclusiveTax(saleOutboundItem.getPriceExclusiveTax());
                item.setAmount(saleOutboundItem.getAmount());
                item.setAmountExclusiveTax(saleOutboundItem.getAmountExclusiveTax());
                item.setTaxRate(saleOutboundItem.getTaxRate());
                item.setTaxAmount(saleOutboundItem.getTaxAmount());
                item.setRemark("[销售出库单" + saleOutbound.getOutboundCode() + "]");
                items.add(item);
            }
            boolean insertResult = itemMapper.insertBatch(items);
            if (!insertResult) {
                throw new ServiceException("应收单明细保存失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromSaleOrder(SaleOrder saleOrder) {
        // 此方法保留，用于支持直接从销售订单创建应收单的特殊业务场景
        final String currentUser = LoginHelper.getUsername();
        log.info("【应收单】[从销售订单创建] - 开始. 操作人: {}, 源订单: {}", currentUser, saleOrder.getOrderCode());
        try {
            if (SaleOrderStatus.COMPLETED != saleOrder.getOrderStatus()) {
                throw new ServiceException("创建失败：销售订单状态 [" + saleOrder.getOrderStatus().getName() + "] ，不能创建");
            }
            if (existsByDirectSourceId(saleOrder.getOrderId())) {
                throw new ServiceException("创建失败：销售订库单 [" + saleOrder.getOrderCode() + "] 已生成过应收单，不能重复创建");
            }
            FinArReceivable add = new FinArReceivable();
            add.setCustomerId(saleOrder.getCustomerId());
            add.setCustomerName(saleOrder.getCustomerName());
            add.setAmountExclusiveTax(saleOrder.getAmountExclusiveTax());
            add.setTaxAmount(saleOrder.getTaxAmount());
            add.setAmount(saleOrder.getAmount());
            add.setSourceId(saleOrder.getOrderId());
            add.setSourceCode(saleOrder.getOrderCode());
            add.setSourceType(SourceType.SALE_ORDER);
            add.setDirectSourceId(saleOrder.getOrderId());
            add.setDirectSourceCode(saleOrder.getOrderCode());
            add.setDirectSourceType(DirectSourceType.SALE_ORDER);
            add.setSummary("[销售订单" + saleOrder.getOrderCode() + "]");

            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                throw new ServiceException("应收单主表生成失败");
            }

            List<FinArReceivableItem> items = new ArrayList<>();
            for (SaleOrderItem saleOrderItem : saleOrder.getItems()) {
                FinArReceivableItem receivableItem = new FinArReceivableItem();
                receivableItem.setReceivableId(add.getReceivableId());

                receivableItem.setSourceId(saleOrder.getOrderId());
                receivableItem.setSourceCode(saleOrder.getOrderCode());
                receivableItem.setSourceType(saleOrder.getSourceType());
                receivableItem.setDirectSourceId(saleOrder.getOrderId());
                receivableItem.setDirectSourceCode(saleOrder.getOrderCode());
                receivableItem.setDirectSourceType(DirectSourceType.SALE_ORDER);

                receivableItem.setDirectSourceItemId(saleOrderItem.getItemId());

                receivableItem.setProductId(saleOrderItem.getProductId());
                receivableItem.setProductCode(saleOrderItem.getProductCode());
                receivableItem.setProductName(saleOrderItem.getProductName());
                receivableItem.setUnitId(saleOrderItem.getUnitId());
                receivableItem.setUnitCode(saleOrderItem.getUnitCode());
                receivableItem.setUnitName(saleOrderItem.getUnitName());
                receivableItem.setQuantity(saleOrderItem.getQuantity());
                receivableItem.setPrice(saleOrderItem.getPrice());
                receivableItem.setPriceExclusiveTax(saleOrderItem.getPriceExclusiveTax());
                receivableItem.setAmount(saleOrderItem.getAmount());
                receivableItem.setAmountExclusiveTax(saleOrderItem.getAmountExclusiveTax());
                receivableItem.setTaxRate(saleOrderItem.getTaxRate());
                receivableItem.setTaxAmount(saleOrderItem.getTaxAmount());
                receivableItem.setRemark("[销售订单" + saleOrder.getOrderCode() + "]");
                items.add(receivableItem);
            }
            boolean insertResult = itemMapper.insertBatch(items);
            if (!insertResult) {
                throw new ServiceException("应收单明细保存失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 销售出库完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeSaleOutboundEvent(SaleOutboundEvent event) {
        try {
            log.info("[completeSaleOutboundEvent] - 销售出库完成事件. event: {}", event);
            SaleOutbound saleOutbound = event.getSaleOutbound();
            boolean result = createFromSaleOutbound(saleOutbound);
            if (result) {
                log.info("[completeSaleOutboundEvent] - 销售出库完成事件处理成功. event: {}", event);
            } else {
                log.error("[completeSaleOutboundEvent] - 销售出库完成事件处理失败. event: {}", event);
            }
        } catch (Exception e) {
            log.error("[completeSaleOutboundEvent] - 销售出库完成事件处理错误. event: {}", event, e);
        }
    }


    private FinArReceivableStatus determineReceivableStatusAfterPayment(BigDecimal totalAmount, BigDecimal appliedAmount) {
        if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return FinArReceivableStatus.UNPAID;
        } else if (appliedAmount.compareTo(totalAmount) >= 0) {
            return FinArReceivableStatus.FULLY_PAID;
        } else {
            return FinArReceivableStatus.PARTIALLY_PAID;
        }
    }

    /**
     * 填充订单中的冗余字段
     */
    private void fillRedundantFields(FinArReceivableBo bo) {
        if (bo.getCustomerId() != null && StringUtils.isEmpty(bo.getCustomerName())) {
            CompanyVo customer = companyService.queryById(bo.getCustomerId());
            if (customer != null) {
                bo.setCustomerName(customer.getCompanyName());
            }
        }

    }
}
