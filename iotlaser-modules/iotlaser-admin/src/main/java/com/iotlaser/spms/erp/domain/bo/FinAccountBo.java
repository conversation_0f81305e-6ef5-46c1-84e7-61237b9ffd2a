package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinAccount;
import com.iotlaser.spms.erp.enums.FinAccountStatus;
import com.iotlaser.spms.erp.enums.FinAccountType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 账户业务对象 erp_fin_account
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinAccount.class, reverseConvertGenerate = false)
public class FinAccountBo extends BaseEntity {

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private FinAccountType accountType;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号/支付账号
     */
    private String accountNumber;

    /**
     * 币种
     */
    private String currency;

    /**
     * 期初余额
     */
    private BigDecimal initialBalance;

    /**
     * 当前余额(由流水实时更新)
     */
    private BigDecimal currentBalance;

    /**
     * 账户状态
     */
    private FinAccountStatus accountStatus;

    /**
     * 摘要
     */
    @NotBlank(message = "摘要不能为空", groups = {AddGroup.class, EditGroup.class})
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
