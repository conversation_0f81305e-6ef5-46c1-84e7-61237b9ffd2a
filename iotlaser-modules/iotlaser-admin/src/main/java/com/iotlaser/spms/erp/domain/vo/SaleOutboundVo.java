package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 销售出库视图对象 erp_sale_outbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleOutbound.class)
public class SaleOutboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @ExcelProperty(value = "出库单ID")
    private Long outboundId;

    /**
     * 出库单编号
     */
    @ExcelProperty(value = "出库单编号")
    private String outboundCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 出库时间
     */
    @ExcelProperty(value = "出库时间")
    private LocalDateTime outboundTime;

    /**
     * 出库状态
     */
    @ExcelProperty(value = "出库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_sale_outbound_status")
    private SaleOutboundStatus outboundStatus;

    /**
     * 发货负责人ID
     */
    @ExcelProperty(value = "发货负责人ID")
    private Long handlerId;

    /**
     * 发货负责人
     */
    @ExcelProperty(value = "发货负责人")
    private String handlerName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<SaleOutboundItemVo> items;

}
