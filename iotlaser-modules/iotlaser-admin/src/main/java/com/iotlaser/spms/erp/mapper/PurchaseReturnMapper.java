package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购退货Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface PurchaseReturnMapper extends BaseMapperPlus<PurchaseReturn, PurchaseReturnVo> {

    default Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return exists(new LambdaQueryWrapper<PurchaseReturn>().eq(PurchaseReturn::getDirectSourceId, directSourceId).eq(PurchaseReturn::getDirectSourceType, directSourceType));
    }

    default List<PurchaseReturn> queryByDirectSourceId(Long directSourceId) {
        return selectList(new LambdaQueryWrapper<PurchaseReturn>().eq(PurchaseReturn::getDirectSourceId, directSourceId));
    }

}
