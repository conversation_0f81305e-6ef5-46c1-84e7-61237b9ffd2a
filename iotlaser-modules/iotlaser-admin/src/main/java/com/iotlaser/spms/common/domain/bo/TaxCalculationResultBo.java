package com.iotlaser.spms.common.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
// 注意：PriceCalculationResult 在同一个包中，不需要导入

/**
 * 价税分离计算结果业务对象
 * <p>
 * 统一的价税分离计算结果封装，确保跨模块数据传输的一致性
 *
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaxCalculationResultBo {

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单价(含税)
     */
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率(%)
     * 注意：此处使用百分比表示，如13表示13%
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;


}
