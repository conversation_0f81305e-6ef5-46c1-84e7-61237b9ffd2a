package com.iotlaser.spms.common.domain.bo;

import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.pro.domain.bo.ProductBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SourceItem.class, reverseConvertGenerate = false)
public class SourceItemBo extends BaseEntity {

    /**
     * 产品入库明细ID
     */
    private Long itemId;

    /**
     * 来源类型
     */
    private String type;

    /**
     * ID
     */
    private Long mainId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 待完成数量
     */
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    private BigDecimal finishQuantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 产品查询条件
     */
    private ProductBo product;

    /**
     * 批次查询条件
     */
    private SourceItemBatchBo batchBo;

    /**
     * 批次
     */
    private List<SourceItemBatchBo> batches;

    /**
     * 排除产品ID
     */
    private String excludeProductIds;

}
