package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionReturn;
import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 生产退料视图对象 mes_production_return
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionReturn.class)
public class ProductionReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退料单ID
     */
    @ExcelProperty(value = "退料单ID")
    private Long returnId;

    /**
     * 退料单编号
     */
    @ExcelProperty(value = "退料单编号")
    private String returnCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 退料时间
     */
    @ExcelProperty(value = "退料时间")
    private LocalDateTime returnTime;

    /**
     * 退料状态
     */
    @ExcelProperty(value = "退料状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_return_status")
    private ProductionReturnStatus returnStatus;

    /**
     * 退料申请人ID
     */
    @ExcelProperty(value = "退料申请人ID")
    private Long applicantId;

    /**
     * 退料申请人
     */
    @ExcelProperty(value = "退料申请人")
    private String applicantName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
