package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.service.IInboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理仓库入库单的完整生命周期，包括创建、执行、完成以及与ERP系统的集成。
 * <p>
 * 支持多种入库类型：采购入库、生产入库、销售退货入库、调拨入库等。
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inbound")
public class InboundController extends BaseController {

    private final IInboundService inboundService;

    /**
     * 查询入库单列表
     */
    @SaCheckPermission("wms:inbound:list")
    @GetMapping("/list")
    public TableDataInfo<InboundVo> list(InboundBo bo, PageQuery pageQuery) {
        return inboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出入库单列表
     */
    @SaCheckPermission("wms:inbound:export")
    @Log(title = "入库单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InboundBo bo, HttpServletResponse response) {
        List<InboundVo> list = inboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "入库单", InboundVo.class, response);
    }

    /**
     * 获取入库单详细信息
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("wms:inbound:query")
    @GetMapping("/{inboundId}")
    public R<InboundVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long inboundId) {
        return R.ok(inboundService.queryById(inboundId));
    }

    /**
     * 新增入库单
     */
    @SaCheckPermission("wms:inbound:add")
    @Log(title = "入库单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<InboundVo> add(@Validated(AddGroup.class) @RequestBody InboundBo bo) {
        return R.ok(inboundService.insertByBo(bo));
    }

    /**
     * 修改入库单
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "入库单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<InboundVo> edit(@Validated(EditGroup.class) @RequestBody InboundBo bo) {
        return R.ok(inboundService.updateByBo(bo));
    }

    /**
     * 删除入库单
     *
     * @param inboundIds 主键串
     */
    @SaCheckPermission("wms:inbound:remove")
    @Log(title = "入库单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] inboundIds) {
        return toAjax(inboundService.deleteWithValidByIds(List.of(inboundIds), true));
    }

    /**
     * 获取入库单明细及关联的详细信息
     *
     * @param id 明细ID
     */
    @SaCheckPermission("wms:inbound:query")
    @GetMapping("item/{id}")
    public R<InboundItemVo> queryItemById(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(MapstructUtils.convert(inboundService.queryItemById(id), InboundItemVo.class));
    }

    /**
     * 查询入库单明细列表及关联的详细信息
     */
    @SaCheckPermission("wms:inbound:list")
    @GetMapping("item/list")
    public TableDataInfo<InboundItemVo> queryItemPageList(InboundItemBo bo, PageQuery pageQuery) {
        return inboundService.queryItemPageList(bo, pageQuery);
    }

    /**
     * 取消入库单
     *
     * @param inboundId 入库单ID
     * @param reason    取消原因
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "入库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{inboundId}")
    public R<Void> cancel(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId, @RequestParam(required = false) String reason) {
        return toAjax(inboundService.cancelInbound(inboundId, reason));
    }

    /**
     * 完成入库单
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "完成入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{inboundId}")
    public R<Void> complete(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(inboundService.completeInbound(inboundId));
    }

    // TODO: [仓库入库状态流转接口] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
    // 需要添加以下状态流转接口：
    // 开始收货: POST /startReceiving/{inboundId} - 将状态从 PENDING_RECEIPT 转为 PARTIALLY_RECEIVED
    // 暂停收货: POST /pauseReceiving/{inboundId} - 暂停收货操作，记录暂停原因
    // 恢复收货: POST /resumeReceiving/{inboundId} - 恢复收货操作
    // 强制完成: POST /forceComplete/{inboundId} - 强制完成入库（处理异常情况）
    // 实现思路：每个接口包含状态校验、业务逻辑和日志记录

    // TODO: [仓库入库批量操作接口] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
    // 需要添加批量操作接口：
    // 批量开始收货: POST /batchStartReceiving - 批量开始多个入库单的收货
    // 批量完成入库: POST /batchComplete - 批量完成多个入库单
    // 批量取消入库: POST /batchCancel - 批量取消多个入库单
    // 实现思路：接收入库单ID列表，循环调用单个操作方法，支持部分成功的结果返回

    // TODO: [仓库入库质检集成接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加质检相关接口：
    // 创建质检任务: POST /createQualityTask/{inboundId} - 为入库单创建质检任务
    // 质检完成回调: POST /qualityTaskComplete/{inboundId} - 质检完成后的回调处理
    // 质检异常处理: POST /handleQualityException/{inboundId} - 处理质检异常情况
    // 实现思路：与 QMS 质量管理模块集成，支持入库质检流程

}
