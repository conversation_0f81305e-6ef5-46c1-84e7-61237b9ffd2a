package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionInboundBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundVo;
import com.iotlaser.spms.mes.service.IProductionInboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产入库
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionInbound")
public class ProductionInboundController extends BaseController {

    private final IProductionInboundService productionInboundService;

    /**
     * 查询生产入库列表
     */
    @SaCheckPermission("mes:productionInbound:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionInboundVo> list(ProductionInboundBo bo, PageQuery pageQuery) {
        return productionInboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产入库列表
     */
    @SaCheckPermission("mes:productionInbound:export")
    @Log(title = "生产入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionInboundBo bo, HttpServletResponse response) {
        List<ProductionInboundVo> list = productionInboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产入库", ProductionInboundVo.class, response);
    }

    /**
     * 获取生产入库详细信息
     *
     * @param inboundId 主键
     */
    @SaCheckPermission("mes:productionInbound:query")
    @GetMapping("/{inboundId}")
    public R<ProductionInboundVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long inboundId) {
        return R.ok(productionInboundService.queryById(inboundId));
    }

    /**
     * 新增生产入库
     */
    @SaCheckPermission("mes:productionInbound:add")
    @Log(title = "生产入库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<ProductionInboundVo> add(@Validated(AddGroup.class) @RequestBody ProductionInboundBo bo) {
        return R.ok(productionInboundService.insertByBo(bo));
    }

    /**
     * 修改生产入库
     */
    @SaCheckPermission("mes:productionInbound:edit")
    @Log(title = "生产入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<ProductionInboundVo> edit(@Validated(EditGroup.class) @RequestBody ProductionInboundBo bo) {
        return R.ok(productionInboundService.updateByBo(bo));
    }

    /**
     * 删除生产入库
     *
     * @param inboundIds 主键串
     */
    @SaCheckPermission("mes:productionInbound:remove")
    @Log(title = "生产入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] inboundIds) {
        return toAjax(productionInboundService.deleteWithValidByIds(List.of(inboundIds), true));
    }

    /**
     * 确认生产入库单
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("mes:productionInbound:edit")
    @Log(title = "确认生产入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{inboundId}")
    public R<Void> confirm(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(productionInboundService.confirmInbound(inboundId));
    }

    /**
     * 批量确认生产入库单
     *
     * @param inboundIds 入库单ID集合
     */
    @SaCheckPermission("mes:productionInbound:edit")
    @Log(title = "批量确认生产入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "入库单ID不能为空") @RequestBody Long[] inboundIds) {
        return toAjax(productionInboundService.batchConfirmInbounds(List.of(inboundIds)));
    }

    /**
     * 完成生产入库
     *
     * @param inboundId 入库单ID
     */
    @SaCheckPermission("mes:productionInbound:edit")
    @Log(title = "完成生产入库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{inboundId}")
    public R<Void> complete(@NotNull(message = "入库单ID不能为空") @PathVariable Long inboundId) {
        return toAjax(productionInboundService.completeInbound(inboundId));
    }

    /**
     * 根据生产订单创建入库单
     *
     * @param productionOrderId 生产订单ID
     */
    @SaCheckPermission("mes:productionInbound:add")
    @Log(title = "从生产订单创建入库单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromProductionOrder/{productionOrderId}")
    public R<ProductionInboundVo> createFromProductionOrder(@NotNull(message = "生产订单ID不能为空") @PathVariable Long productionOrderId) {
        return R.ok(productionInboundService.createFromProductionOrder(productionOrderId));
    }
}
