package com.iotlaser.spms.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.iotlaser.spms.base.domain.MeasureUnit;
import com.iotlaser.spms.base.domain.bo.MeasureUnitBo;
import com.iotlaser.spms.base.domain.vo.MeasureUnitVo;
import com.iotlaser.spms.base.mapper.MeasureUnitMapper;
import com.iotlaser.spms.base.service.IMeasureUnitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 计量单位Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MeasureUnitServiceImpl implements IMeasureUnitService {

    private final MeasureUnitMapper baseMapper;

    /**
     * 查询计量单位
     *
     * @param unitId 主键
     * @return 计量单位
     */
    @Override
    public MeasureUnitVo queryById(Long unitId) {
        return baseMapper.selectVoById(unitId);
    }

    /**
     * 查询符合条件的计量单位列表
     *
     * @param bo 查询条件
     * @return 计量单位列表
     */
    @Override
    public List<MeasureUnitVo> queryList(MeasureUnitBo bo) {
        LambdaQueryWrapper<MeasureUnit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MeasureUnit> buildQueryWrapper(MeasureUnitBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeasureUnit> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MeasureUnit::getUnitId);
        lqw.eq(bo.getParentId() != null, MeasureUnit::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), MeasureUnit::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), MeasureUnit::getUnitName, bo.getUnitName());
        // ✅ 优化：移除单位比率和排序号的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getUnitRatio() != null, MeasureUnit::getUnitRatio, bo.getUnitRatio());
        // 原代码：lqw.eq(bo.getOrderNum() != null, MeasureUnit::getOrderNum, bo.getOrderNum());
        // TODO: 如需要可以后续添加比率和排序号的范围查询支持

        lqw.eq(StringUtils.isNotBlank(bo.getPrimaryFlag()), MeasureUnit::getPrimaryFlag, bo.getPrimaryFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MeasureUnit::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增计量单位
     *
     * @param bo 计量单位
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MeasureUnitBo bo) {
        try {
            MeasureUnit add = MapstructUtils.convert(bo, MeasureUnit.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增计量单位失败");
            }

            bo.setUnitId(add.getUnitId());
            log.info("新增计量单位成功：{}", add.getUnitName());
            return true;
        } catch (Exception e) {
            log.error("新增计量单位失败：{}", e.getMessage(), e);
            throw new ServiceException("新增计量单位失败：" + e.getMessage());
        }
    }

    /**
     * 修改计量单位
     *
     * @param bo 计量单位
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MeasureUnitBo bo) {
        try {
            MeasureUnit update = MapstructUtils.convert(bo, MeasureUnit.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改计量单位失败：单位不存在或数据未变更");
            }

            log.info("修改计量单位成功：{}", update.getUnitName());
            return true;
        } catch (Exception e) {
            log.error("修改计量单位失败：{}", e.getMessage(), e);
            throw new ServiceException("修改计量单位失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MeasureUnit entity) {
        MeasureUnitBo checkBo = new MeasureUnitBo();
        checkBo.setUnitId(entity.getUnitId());
        checkBo.setUnitCode(entity.getUnitCode());
        checkBo.setParentId(entity.getParentId());
        if (!checkProductMaterialUnitCodeUnique(checkBo)) {
            throw new ServiceException("产品计量单位编码'" + entity.getUnitCode() + "'已存在");
        }
    }

    /**
     * 校验并批量删除计量单位信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验计量单位是否有子单位
            List<MeasureUnit> units = baseMapper.selectByIds(ids);
            for (MeasureUnit unit : units) {
                // 检查是否有子单位
                LambdaQueryWrapper<MeasureUnit> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(MeasureUnit::getParentId, unit.getUnitId());
                if (baseMapper.exists(wrapper)) {
                    throw new ServiceException("计量单位【" + unit.getUnitName() + "】存在子单位，不能删除");
                }
                // 检查是否有产品使用该单位等
                log.info("删除计量单位校验：{}", unit.getUnitName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除计量单位成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除计量单位失败：{}", e.getMessage(), e);
            throw new ServiceException("删除计量单位失败：" + e.getMessage());
        }
    }

    @Override
    public boolean checkProductMaterialUnitCodeUnique(MeasureUnitBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<MeasureUnit>()
            .eq(MeasureUnit::getUnitCode, bo.getUnitCode())
            .eq(MeasureUnit::getParentId, bo.getParentId())
            .ne(ObjectUtil.isNotNull(bo.getUnitId()), MeasureUnit::getUnitId, bo.getUnitId()));
        return !exist;
    }

    @Override
    public List<Tree<Long>> selectTreeList(MeasureUnitBo bo) {
        // 构建查询条件
        LambdaQueryWrapper<MeasureUnit> lqw = buildQueryWrapper(bo);
        // 执行查询并获取商品类型列表
        List<MeasureUnitVo> results = baseMapper.selectVoList(lqw);
        // 构建并返回商品类型树形列表
        return buildTreeSelect(results);
    }

    @Override
    public List<Tree<Long>> buildTreeSelect(List<MeasureUnitVo> results) {
        // 如果类型列表为空，则直接返回一个空的新列表
        if (CollUtil.isEmpty(results)) {
            return CollUtil.newArrayList();
        }
        // 获取当前列表中每一个节点的parentId，然后在列表中查找是否有id与其parentId对应，若无对应，则表明此时节点列表中，该节点在当前列表中属于顶级节点
        List<Tree<Long>> treeList = CollUtil.newArrayList();
        for (MeasureUnitVo productMaterialUnitVo : results) {
            Long parentId = productMaterialUnitVo.getParentId();
            // 寻找当前类型列表中是否有与当前节点的parentId匹配的节点，如果没有找到，则认为当前节点是顶级节点
            MeasureUnitVo first = StreamUtils.findFirst(results, it -> it.getUnitId().longValue() == parentId);
            // 如果没有找到父节点，说明当前节点是顶级节点，构建树结构并添加到树列表中
            if (ObjectUtil.isNull(first)) {
                // 使用TreeBuildUtils工具类构建树结构
                List<Tree<Long>> trees = TreeBuildUtils.build(results, parentId, (vo, tree) ->
                    tree.setId(vo.getUnitId())
                        .setParentId(vo.getParentId())
                        .setName(vo.getUnitName())
                        .setWeight(vo.getOrderNum())
                        .putExtra("disabled", SystemConstants.DISABLE.equals(vo.getStatus())));
                // 找到当前节点在树结构中的位置，并将其添加到树列表中
                Tree<Long> tree = StreamUtils.findFirst(trees, it -> it.getId().longValue() == productMaterialUnitVo.getUnitId());
                treeList.add(tree);
            }
        }
        // 返回构建好的树结构列表
        return treeList;
    }

    /**
     * 数量单位转换
     *
     * @param quantity 数量
     * @param fromUnit 源单位编码
     * @param toUnit   目标单位编码
     * @return 转换后的数量
     */
    @Override
    public BigDecimal convertQuantity(BigDecimal quantity, String fromUnit, String toUnit) {
        try {
            if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("数量不能为空或负数");
            }
            if (StringUtils.isBlank(fromUnit) || StringUtils.isBlank(toUnit)) {
                throw new ServiceException("单位编码不能为空");
            }
            if (fromUnit.equals(toUnit)) {
                return quantity; // 相同单位直接返回
            }

            // 获取源单位和目标单位信息
            MeasureUnitVo fromUnitInfo = getByUnitCode(fromUnit);
            MeasureUnitVo toUnitInfo = getByUnitCode(toUnit);

            if (fromUnitInfo == null) {
                throw new ServiceException("源单位不存在：" + fromUnit);
            }
            if (toUnitInfo == null) {
                throw new ServiceException("目标单位不存在：" + toUnit);
            }

            // TODO: 这里需要实现单位转换逻辑
            // 简化实现：假设单位有转换率字段
            // BigDecimal conversionRate = getConversionRate(fromUnit, toUnit);
            // return quantity.multiply(conversionRate);

            log.warn("单位转换功能需要完善 - 从{}到{}，数量：{}", fromUnit, toUnit, quantity);
            return quantity; // 暂时返回原数量
        } catch (Exception e) {
            log.error("单位转换失败，从{}到{}，数量：{}，错误：{}", fromUnit, toUnit, quantity, e.getMessage(), e);
            throw new ServiceException("单位转换失败：" + e.getMessage());
        }
    }

    /**
     * 根据单位编码获取单位信息
     *
     * @param unitCode 单位编码
     * @return 单位信息
     */
    @Override
    public MeasureUnitVo getByUnitCode(String unitCode) {
        try {
            if (StringUtils.isBlank(unitCode)) {
                return null;
            }

            LambdaQueryWrapper<MeasureUnit> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(MeasureUnit::getUnitCode, unitCode);
            wrapper.eq(MeasureUnit::getStatus, "1");

            MeasureUnitVo result = baseMapper.selectVoOne(wrapper);
            log.debug("根据编码查询单位：{}", unitCode);
            return result;
        } catch (Exception e) {
            log.error("根据编码查询单位失败，编码：{}，错误：{}", unitCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据单位组获取单位列表
     *
     * @param groupCode 单位组编码
     * @return 单位列表
     */
    @Override
    public List<MeasureUnitVo> getUnitsByGroup(String groupCode) {
        try {
            if (StringUtils.isBlank(groupCode)) {
                return new ArrayList<>();
            }

            LambdaQueryWrapper<MeasureUnit> wrapper = Wrappers.lambdaQuery();
            // TODO: 这里需要根据实际的单位组字段进行查询
            // wrapper.eq(MeasureUnit::getUnitGroup, groupCode);
            wrapper.eq(MeasureUnit::getStatus, "1");
            wrapper.orderByAsc(MeasureUnit::getOrderNum);

            List<MeasureUnitVo> result = baseMapper.selectVoList(wrapper);
            log.debug("根据单位组查询单位成功，组：{}，数量：{}", groupCode, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据单位组查询单位失败，组：{}，错误：{}", groupCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取基础单位列表（没有父单位的单位）
     *
     * @return 基础单位列表
     */
    @Override
    public List<MeasureUnitVo> getBaseUnits() {
        try {
            LambdaQueryWrapper<MeasureUnit> wrapper = Wrappers.lambdaQuery();
            wrapper.isNull(MeasureUnit::getParentId).or().eq(MeasureUnit::getParentId, 0L);
            wrapper.eq(MeasureUnit::getStatus, "1");
            wrapper.orderByAsc(MeasureUnit::getOrderNum);

            List<MeasureUnitVo> result = baseMapper.selectVoList(wrapper);
            log.debug("查询基础单位成功，数量：{}", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询基础单位失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据精度四舍五入数值
     *
     * @param value    数值
     * @param unitCode 单位编码
     * @return 四舍五入后的数值
     */
    @Override
    public BigDecimal roundByUnitPrecision(BigDecimal value, String unitCode) {
        try {
            if (value == null) {
                return null;
            }
            if (StringUtils.isBlank(unitCode)) {
                return value;
            }

            MeasureUnitVo unitInfo = getByUnitCode(unitCode);
            if (unitInfo == null) {
                log.warn("单位不存在，使用默认精度：{}", unitCode);
                return value.setScale(2, RoundingMode.HALF_UP);
            }

            // TODO: 这里需要根据实际的精度字段进行处理
            // Integer precision = unitInfo.getPrecision();
            // if (precision != null && precision >= 0) {
            //    return value.setScale(precision, RoundingMode.HALF_UP);
            // }

            // 默认保留2位小数
            return value.setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("按单位精度四舍五入失败，值：{}，单位：{}，错误：{}", value, unitCode, e.getMessage(), e);
            return value;
        }
    }
}
