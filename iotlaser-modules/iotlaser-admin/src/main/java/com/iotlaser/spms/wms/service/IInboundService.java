package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 入库单服务接口
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface IInboundService {

    /**
     * 根据ID查询入库单
     *
     * @param inboundId 入库单ID
     * @return 入库单视图对象
     */
    InboundVo queryById(Long inboundId);

    /**
     * 分页查询入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 入库单分页列表
     */
    TableDataInfo<InboundVo> queryPageList(InboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的入库单列表
     *
     * @param bo 查询条件
     * @return 入库单列表
     */
    List<InboundVo> queryList(InboundBo bo);

    /**
     * 新增仓库入库单
     *
     * @param bo 包含新仓库入库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    InboundVo insertByBo(InboundBo bo);

    /**
     * 修改仓库入库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    InboundVo updateByBo(InboundBo bo);

    /**
     * 校验并批量删除仓库入库单
     *
     * @param ids     待删除的仓库入库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据采购订单创建入库单
     *
     * @param purchaseOrder 采购订单
     * @return 操作结果
     */
    Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder);

    /**
     * 根据采购入库单创建入库单
     *
     * @param purchaseInbound 采购入库单
     * @return 操作结果
     */
    Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound);

    /**
     * 根据销售退货单创建入库单
     *
     * @param saleReturn 销售退货单
     * @return 操作结果
     */
    Boolean createFromSaleReturn(SaleReturn saleReturn);

    /**
     * 根据出库单创建入库单
     *
     * @param outbound 出库单
     * @return 操作结果
     */
    Boolean createFromOutbound(Outbound outbound);

    /**
     * 取消入库单
     *
     * @param inboundId    入库单ID
     * @param cancelReason 取消原因
     * @return 操作结果
     */
    Boolean cancelInbound(Long inboundId, String cancelReason);

    /**
     * 完成入库单
     *
     * @param inboundId 入库单ID
     * @return 操作结果
     */
    Boolean completeInbound(Long inboundId);

    /**
     * 根据源单ID和源单类型查询入库单列表
     *
     * @param sourceId   源单ID
     * @param sourceType 源单类型
     * @return 入库单列表
     */
    List<InboundVo> queryBySourceId(Long sourceId, SourceType sourceType);


    /**
     * 根据ID查询入库单明细
     *
     * @param itemId 明细ID
     * @return 入库单明细
     */
    InboundItem queryItemById(Long itemId);

    /**
     * 根据入库单ID查询明细列表
     *
     * @param inboundId 入库单ID
     * @return 入库单明细列表
     */
    List<InboundItem> queryItemByInboundId(Long inboundId);

    /**
     * 根据直接源单ID和类型查询入库单明细列表
     *
     * @param directSourceId   直接源单ID
     * @param directSourceType 直接源单类型
     * @return 入库单明细列表
     */
    List<InboundItem> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 根据直接源单ID和类型查询已完成的入库单
     *
     * @param directSourceId   直接源单ID
     * @param directSourceType 直接源单类型
     * @return 已完成的入库单列表
     */
    List<Inbound> queryCompleteByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 检查是否存在指定直接源的入库单
     *
     * @param directSourceId   直接源单ID
     * @param directSourceType 直接源单类型
     * @return 是否存在
     */
    Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 查询入库单明细列表
     *
     * @param bo 查询条件
     * @return 入库单明细列表
     */
    List<InboundItem> queryItemList(InboundItemBo bo);

    /**
     * 分页查询入库单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 入库单明细分页列表
     */
    TableDataInfo<InboundItemVo> queryItemPageList(InboundItemBo bo, PageQuery pageQuery);

}
