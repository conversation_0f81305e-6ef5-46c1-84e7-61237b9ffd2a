package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionIssueBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueVo;
import com.iotlaser.spms.wms.domain.Outbound;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产领料Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-07
 */
public interface IProductionIssueService {

    /**
     * 查询生产领料
     *
     * @param issueId 主键
     * @return 生产领料
     */
    ProductionIssueVo queryById(Long issueId);

    /**
     * 分页查询生产领料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料分页列表
     */
    TableDataInfo<ProductionIssueVo> queryPageList(ProductionIssueBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产领料列表
     *
     * @param bo 查询条件
     * @return 生产领料列表
     */
    List<ProductionIssueVo> queryList(ProductionIssueBo bo);

    /**
     * 新增生产领料
     *
     * @param bo 生产领料
     * @return 创建的生产领料
     */
    ProductionIssueVo insertByBo(ProductionIssueBo bo);

    /**
     * 修改生产领料
     *
     * @param bo 生产领料
     * @return 修改后的生产领料
     */
    ProductionIssueVo updateByBo(ProductionIssueBo bo);

    /**
     * 校验并批量删除生产领料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认生产领料单
     *
     * @param issueId 领料单ID
     * @return 是否确认成功
     */
    Boolean confirmIssue(Long issueId);

    /**
     * 批量确认生产领料单
     *
     * @param issueIds 领料单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmIssues(Collection<Long> issueIds);

    /**
     * 完成生产领料
     *
     * @param issueId 领料单ID
     * @return 是否完成成功
     */
    Boolean completeIssue(Long issueId);

    /**
     * 取消生产领料单
     *
     * @param issueId 领料单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    Boolean cancelIssue(Long issueId, String reason);

    /**
     * 根据生产订单创建领料单
     *
     * @param productionOrderId 生产订单ID
     * @return 创建的领料单
     */
    ProductionIssueVo createFromProductionOrder(Long productionOrderId);

    /**
     * 仓库出库完成后状态回传
     * 由WMS系统调用，通知销售出库单已完成
     *
     * @param outbound 出库单
     * @return 是否更新成功
     */
    Boolean updateStatusByWms(Outbound outbound);

    /**
     * 仓库出库异常回传
     * 由WMS系统调用，通知销售出库单发生异常
     *
     * @param outboundId      销售出库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    Boolean handleWmsException(Long outboundId, String exceptionReason);
}
