package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.FinApInvoice;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 应付单（发票）Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinApInvoiceService {

    /**
     * 查询应付
     *
     * @param invoiceId 主键
     * @return 应付
     */
    FinApInvoiceVo queryById(Long invoiceId);

    /**
     * 分页查询应付列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应付分页列表
     */
    TableDataInfo<FinApInvoiceVo> queryPageList(FinApInvoiceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应付列表
     *
     * @param bo 查询条件
     * @return 应付列表
     */
    List<FinApInvoiceVo> queryList(FinApInvoiceBo bo);

    /**
     * 新增应付
     *
     * @param bo 应付
     * @return 是否新增成功
     */
    FinApInvoiceVo insertByBo(FinApInvoiceBo bo);

    /**
     * 修改应付
     *
     * @param bo 应付
     * @return 是否修改成功
     */
    FinApInvoiceVo updateByBo(FinApInvoiceBo bo);

    /**
     * 校验并批量删除应付信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 三单匹配 - 自动匹配
     *
     * @param invoiceId 发票ID
     * @return 匹配结果
     */
    Boolean autoMatchThreeWay(Long invoiceId);

    /**
     * 三单匹配 - 手工匹配
     *
     * @param invoiceId       发票ID
     * @param purchaseOrderId 采购订单ID
     * @param inboundId       入库单ID
     * @param matchedAmount   匹配金额
     * @param operatorId      操作人ID
     * @param operatorName    操作人姓名
     * @return 匹配结果
     */
    Boolean manualMatchThreeWay(Long invoiceId, Long purchaseOrderId, Long inboundId,
                                BigDecimal matchedAmount, Long operatorId, String operatorName);

    /**
     * 判断上游
     *
     * @param directSourceId 上游单据ID
     * @return 是否上游
     */
    Boolean existsByDirectSourceId(Long directSourceId);

    /**
     * 查询上游
     *
     * @param directSourceId 上游单据ID
     * @return 应付单列表
     */
    List<FinApInvoice> selectListByDirectSourceId(Long directSourceId);

    /**
     * 查询指定供应商可以继续核销的应付单
     *
     * @param payeeId 供应商ID
     * @return 应付单列表
     */
    List<FinApInvoice> queryByPayeeId(Long payeeId);

    /**
     * 更新发票状态
     *
     * @param invoiceId     发票ID
     * @param invoiceStatus 新状态
     * @return 是否更新成功
     */
    Boolean updateInvoiceStatus(Long invoiceId, FinApInvoiceStatus invoiceStatus);

    /**
     * 更新发票的付款金额
     * <p>
     * 此方法在付款核销或反核销后被调用，用于重新计算并更新应付单的已付金额。
     *
     * @param invoiceId     发票ID
     * @param appliedAmount 核销金额
     * @return 是否更新成功
     */
    Boolean updateInvoicePaymentStatus(Long invoiceId, BigDecimal appliedAmount);

    /**
     * 获取未付款的应付列表
     *
     * @param supplierId 供应商ID
     * @param date       日期
     * @return 未付款的应付列表
     */
    List<FinApInvoiceVo> getUnpaidInvoicesBefore(Long supplierId, LocalDate date);

    /**
     * 从采购订单生成应付单
     *
     * @param purchaseOrder 采购订单
     * @return 是否创建成功
     */
    Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder);

    /**
     * 从采购入库单生成应付单
     *
     * @param purchaseInbound 采购入库单
     * @return 是否创建成功
     */
    Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound);

    /**
     * 更新发票的付款状态
     * <p>
     * 此方法在付款核销或反核销后被调用，用于重新计算并更新应付单的已付金额和业务状态。
     *
     * @param invoiceId 发票ID
     * @return 是否更新成功
     */
    Boolean updateInvoicePaymentStatus(Long invoiceId);

    /**
     * 确认发票
     *
     * @param invoiceId 发票ID
     * @return 是否确认成功
     */
    Boolean confirmInvoice(Long invoiceId);
}
