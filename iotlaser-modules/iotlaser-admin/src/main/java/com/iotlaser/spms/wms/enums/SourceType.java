package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存记录单类型枚举
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Getter
@AllArgsConstructor
public enum SourceType implements IDictEnum<String> {

    PRODUCTION_ORDER("production_order", "生产订单", "生产订单"),
    PURCHASE_ORDER("purchase_order", "采购订单", "采购订单"),
    TRANSFER("transfer", "移库单", "移库单"),
    SALE_ORDER("sale_order", "销售订单", "销售订单"),
    INBOUND("inbound", "入库单", "入库单"),
    OUTBOUND("outbound", "出库单", "出库单"),
    RECEIPT_ORDER("receipt_order", "收款单", "收款单"),
    RECEIVABLE("receipt_order", "应收单", "应收单"),
    PAYMENT_ORDER("payment_order", "付款单", "付款单"),
    INVOICE("payment_order", "应付单", "应付单"),
    ;

    public final static String DICT_CODE = "wms_source_type";
    public final static String DICT_NAME = "库存记录源头类型";
    public final static String DICT_DESC = "定义库存变动记录的源头类型，用于追溯库存变动的业务源头";

    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 源头类型枚举
     */
    public static SourceType getByValue(String value) {
        for (SourceType sourceType : values()) {
            if (sourceType.getValue().equals(value)) {
                return sourceType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
