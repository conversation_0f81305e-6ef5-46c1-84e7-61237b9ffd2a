package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 库存盘点对象 wms_inventory_check
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_inventory_check")
public class InventoryCheck extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点ID
     */
    @TableId(value = "check_id")
    private Long checkId;

    /**
     * 盘点编码
     */
    private String checkCode;

    /**
     * 盘点名称
     */
    private String checkName;

    /**
     * 盘点类型
     */
    private String checkType;

    /**
     * 盘点范围
     */
    private String checkScope;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 盘点状态
     */
    private String checkStatus;

    /**
     * 盘点负责人 ID
     */
    private Long supervisorId;

    /**
     * 盘点负责人
     */
    private String supervisorName;

    /**
     * 参与盘点人员IDS
     */
    private String operatorIds;

    /**
     * 参与盘点人员
     */
    private String operatorNames;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
