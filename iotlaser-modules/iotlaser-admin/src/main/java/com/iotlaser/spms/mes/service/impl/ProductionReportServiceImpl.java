package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.ProductionReport;
import com.iotlaser.spms.mes.domain.bo.ProductionReportBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReportVo;
import com.iotlaser.spms.mes.mapper.ProductionReportMapper;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import com.iotlaser.spms.mes.service.IProductionReportService;
import com.iotlaser.spms.pro.service.IInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产报工记录Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionReportServiceImpl implements IProductionReportService {

    private final ProductionReportMapper baseMapper;
    private final IProductionOrderService productionOrderService;
    private final IInstanceService instanceService;

    /**
     * 查询生产报工记录
     *
     * @param reportId 主键
     * @return 生产报工记录
     */
    @Override
    public ProductionReportVo queryById(Long reportId) {
        return baseMapper.selectVoById(reportId);
    }

    /**
     * 分页查询生产报工记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产报工记录分页列表
     */
    @Override
    public TableDataInfo<ProductionReportVo> queryPageList(ProductionReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionReport> lqw = buildQueryWrapper(bo);
        Page<ProductionReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产报工记录列表
     *
     * @param bo 查询条件
     * @return 生产报工记录列表
     */
    @Override
    public List<ProductionReportVo> queryList(ProductionReportBo bo) {
        LambdaQueryWrapper<ProductionReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionReport> buildQueryWrapper(ProductionReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionReport> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionReport::getReportId);
        lqw.eq(bo.getOrderId() != null, ProductionReport::getOrderId, bo.getOrderId());
        lqw.eq(bo.getInstanceId() != null, ProductionReport::getInstanceId, bo.getInstanceId());
        lqw.eq(bo.getStepId() != null, ProductionReport::getStepId, bo.getStepId());
        lqw.eq(bo.getProcessId() != null, ProductionReport::getProcessId, bo.getProcessId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessCode()), ProductionReport::getProcessCode, bo.getProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), ProductionReport::getProcessName, bo.getProcessName());
        lqw.eq(StringUtils.isNotBlank(bo.getReportType()), ProductionReport::getReportType, bo.getReportType());
        lqw.eq(bo.getQuantityGood() != null, ProductionReport::getQuantityGood, bo.getQuantityGood());
        lqw.eq(bo.getQuantityBad() != null, ProductionReport::getQuantityBad, bo.getQuantityBad());
        lqw.eq(bo.getStartTime() != null, ProductionReport::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, ProductionReport::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionReport::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产报工记录
     *
     * @param bo 生产报工记录
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ProductionReportBo bo) {
        try {
            ProductionReport add = MapstructUtils.convert(bo, ProductionReport.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增生产报工记录失败");
            }

            bo.setReportId(add.getReportId());

            // 更新生产进度
            updateProductionProgress(add);

            log.info("新增生产报工记录成功：工序【{}】", add.getProcessName());
            return true;
        } catch (Exception e) {
            log.error("新增生产报工记录失败：{}", e.getMessage(), e);
            throw new ServiceException("新增生产报工记录失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产报工记录
     *
     * @param bo 生产报工记录
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ProductionReportBo bo) {
        try {
            ProductionReport update = MapstructUtils.convert(bo, ProductionReport.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改生产报工记录失败：记录不存在或数据未变更");
            }

            // 更新生产进度
            updateProductionProgress(update);

            log.info("修改生产报工记录成功：工序【{}】", update.getProcessName());
            return true;
        } catch (Exception e) {
            log.error("修改生产报工记录失败：{}", e.getMessage(), e);
            throw new ServiceException("修改生产报工记录失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionReport entity) {

    }

    /**
     * 校验并批量删除生产报工记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验报工记录是否可以删除
            List<ProductionReport> reports = baseMapper.selectByIds(ids);
            for (ProductionReport report : reports) {
                log.info("删除生产报工记录，工序：{}", report.getProcessName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 移动端开工报工
     * 集成产品实例创建逻辑，实现生产报工与产品实例的无缝集成
     *
     * @param orderId       生产订单ID
     * @param routingStepId 工艺步骤ID
     * @param operatorId    操作员ID
     * @return 产品实例编码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startProduction(Long orderId, Long routingStepId, Long operatorId) {
        try {
            // ✅ 启用：验证生产订单状态是否允许开工
            var productionOrder = productionOrderService.queryById(orderId);
            if (productionOrder == null) {
                throw new ServiceException("生产订单不存在：" + orderId);
            }

            // 验证生产订单状态
            if (!"RELEASED".equals(productionOrder.getOrderStatus().getValue()) &&
                !"IN_PROGRESS".equals(productionOrder.getOrderStatus().getValue())) {
                throw new ServiceException("生产订单【" + productionOrder.getOrderCode() +
                    "】状态为【" + productionOrder.getOrderStatus() + "】，不允许开工报工");
            }

            // 创建产品实例并生成唯一实例编码
            // 集成产品实例服务，基于生产订单信息创建产品实例
            String instanceCode = instanceService.createProductInstance(
                orderId,
                productionOrder.getProductId(),
                null // routingId暂时为null，待工艺路线模块完善后传入
            );

            // 根据实例编码查找实例ID，用于关联报工记录
            var instance = instanceService.getByInstanceCode(instanceCode);
            Long instanceId = (instance != null) ? instance.getInstanceId() : null;

            // 保存开工报工记录
            ProductionReport report = new ProductionReport();
            report.setOrderId(orderId);
            report.setStepId(routingStepId); // 使用stepId字段存储工艺步骤ID
            report.setInstanceId(instanceId); // 关联产品实例
            report.setReportType("START");
            report.setStartTime(LocalDateTime.now());

            // 在remark中记录操作人信息和实例编码，待实体完善后使用专门字段
            report.setRemark("移动端开工报工 - 操作人ID: " + operatorId +
                ", 实例编码: " + instanceCode +
                ", 工艺步骤ID: " + routingStepId);

            validEntityBeforeSave(report);
            int result = baseMapper.insert(report);
            if (result <= 0) {
                throw new ServiceException("开工报工记录保存失败");
            }

            // 更新产品实例状态为激活状态
            instanceService.updateInstanceStatus(instanceCode,
                com.iotlaser.spms.pro.enums.InstanceStatus.ACTIVE,
                "开工报工激活实例");

            // 更新生产进度
            updateProductionProgress(report);

            log.info("移动端开工报工成功：订单【{}】工序【{}】操作员【{}】实例【{}】",
                productionOrder.getOrderCode(), routingStepId, operatorId, instanceCode);

            return instanceCode;
        } catch (Exception e) {
            log.error("移动端开工报工失败：{}", e.getMessage(), e);
            throw new ServiceException("开工报工失败：" + e.getMessage());
        }
    }

    /**
     * 移动端完工报工
     *
     * @param instanceCode 产品实例编码
     * @param finishQty    完工数量
     * @param qualifiedQty 合格数量
     * @param operatorId   操作员ID
     * @return 是否报工成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishProduction(String instanceCode, Integer finishQty, Integer qualifiedQty, Long operatorId) {
        try {
            // 验证产品实例状态
            if (StringUtils.isBlank(instanceCode)) {
                throw new ServiceException("产品实例编码不能为空");
            }

            // 创建完工报工记录
            ProductionReport report = new ProductionReport();
            // TODO: ProductionReport实体中没有instanceCode字段，使用instanceId替代
            // report.setInstanceCode(instanceCode);
            report.setInstanceId(null); // 暂时设置为null，待实体完善后修正
            // TODO: ProductionReport实体中没有operatorId字段，在remark中记录
            // report.setOperatorId(operatorId);
            report.setReportType("FINISH");
            // TODO: ProductionReport实体中没有finishQuantity和qualifiedQuantity字段，使用quantityGood替代
            // report.setFinishQuantity(BigDecimal.valueOf(finishQty));
            // report.setQualifiedQuantity(BigDecimal.valueOf(qualifiedQty));
            report.setQuantityGood(BigDecimal.valueOf(qualifiedQty)); // 使用良品数量字段
            report.setQuantityBad(BigDecimal.valueOf(finishQty - qualifiedQty)); // 计算不良品数量
            // TODO: ProductionReport实体中没有reportTime字段，使用endTime替代
            // report.setReportTime(LocalDateTime.now());
            report.setEndTime(LocalDateTime.now()); // 使用Date类型的endTime字段
            // 在remark中记录操作人信息和实例编码
            report.setRemark("移动端完工报工 - 操作人ID: " + operatorId + ", 实例编码: " + instanceCode +
                ", 完工数量: " + finishQty + ", 合格数量: " + qualifiedQty);

            int result = baseMapper.insert(report);

            if (result > 0) {
                log.info("移动端完工报工成功：实例【{}】完工【{}】合格【{}】操作员【{}】",
                    instanceCode, finishQty, qualifiedQty, operatorId);
                return true;
            } else {
                throw new ServiceException("完工报工记录保存失败");
            }
        } catch (Exception e) {
            log.error("移动端完工报工失败：{}", e.getMessage(), e);
            throw new ServiceException("完工报工失败：" + e.getMessage());
        }
    }

    /**
     * 移动端暂停报工
     *
     * @param instanceCode 产品实例编码
     * @param pauseReason  暂停原因
     * @param operatorId   操作员ID
     * @return 是否暂停成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseProduction(String instanceCode, String pauseReason, Long operatorId) {
        try {
            if (StringUtils.isBlank(instanceCode)) {
                throw new ServiceException("产品实例编码不能为空");
            }
            if (StringUtils.isBlank(pauseReason)) {
                throw new ServiceException("暂停原因不能为空");
            }

            ProductionReport report = new ProductionReport();
            // TODO: ProductionReport实体中没有instanceCode字段，使用instanceId替代
            // report.setInstanceCode(instanceCode);
            report.setInstanceId(null); // 暂时设置为null，待实体完善后修正
            // TODO: ProductionReport实体中没有operatorId字段，在remark中记录
            // report.setOperatorId(operatorId);
            report.setReportType("PAUSE");
            // TODO: ProductionReport实体中没有reportTime字段，使用startTime替代
            // report.setReportTime(LocalDateTime.now());
            report.setStartTime(LocalDateTime.now()); // 使用Date类型的startTime字段
            // 在remark中记录操作人信息和实例编码
            report.setRemark("暂停原因：" + pauseReason + " - 操作人ID: " + operatorId + ", 实例编码: " + instanceCode);

            int result = baseMapper.insert(report);

            if (result > 0) {
                log.info("移动端暂停报工成功：实例【{}】原因【{}】操作员【{}】",
                    instanceCode, pauseReason, operatorId);
                return true;
            } else {
                throw new ServiceException("暂停报工记录保存失败");
            }
        } catch (Exception e) {
            log.error("移动端暂停报工失败：{}", e.getMessage(), e);
            throw new ServiceException("暂停报工失败：" + e.getMessage());
        }
    }

    /**
     * 移动端恢复报工
     *
     * @param instanceCode 产品实例编码
     * @param operatorId   操作员ID
     * @return 是否恢复成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resumeProduction(String instanceCode, Long operatorId) {
        try {
            if (StringUtils.isBlank(instanceCode)) {
                throw new ServiceException("产品实例编码不能为空");
            }

            ProductionReport report = new ProductionReport();
            // TODO: ProductionReport实体中没有instanceCode字段，使用instanceId替代
            // report.setInstanceCode(instanceCode);
            report.setInstanceId(null); // 暂时设置为null，待实体完善后修正
            // TODO: ProductionReport实体中没有operatorId字段，在remark中记录
            // report.setOperatorId(operatorId);
            report.setReportType("RESUME");
            // TODO: ProductionReport实体中没有reportTime字段，使用startTime替代
            // report.setReportTime(LocalDateTime.now());
            report.setStartTime(LocalDateTime.now());
            // 在remark中记录操作人信息和实例编码
            report.setRemark("移动端恢复生产 - 操作人ID: " + operatorId + ", 实例编码: " + instanceCode);

            int result = baseMapper.insert(report);

            if (result > 0) {
                log.info("移动端恢复报工成功：实例【{}】操作员【{}】", instanceCode, operatorId);
                return true;
            } else {
                throw new ServiceException("恢复报工记录保存失败");
            }
        } catch (Exception e) {
            log.error("移动端恢复报工失败：{}", e.getMessage(), e);
            throw new ServiceException("恢复报工失败：" + e.getMessage());
        }
    }

    /**
     * 物料消耗报工 - 记录物料消耗并创建使用记录
     *
     * @param instanceCode      产品实例编码
     * @param materialBatchCode 物料批次编码
     * @param consumeQuantity   消耗数量
     * @param operatorId        操作员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean reportMaterialConsume(String instanceCode, String materialBatchCode,
                                         BigDecimal consumeQuantity, Long operatorId) {
        try {
            // TODO: 集成产品实例查询、物料批次验证、消耗记录创建、库存更新
            // 需要实现：
            // 验证产品实例状态是否允许物料消耗
            // 检查物料批次的可用性和质量状态
            // 创建物料消耗记录并关联产品实例
            // 更新库存数量和批次状态
            // 记录物料追溯日志

            log.info("物料消耗报工：实例【{}】消耗批次【{}】数量【{}】",
                instanceCode, materialBatchCode, consumeQuantity);
            return true;
        } catch (Exception e) {
            log.error("物料消耗报工失败：{}", e.getMessage(), e);
            throw new ServiceException("物料消耗报工失败：" + e.getMessage());
        }
    }

    /**
     * 完工报工 - 记录工序完工并更新实例状态
     *
     * @param instanceCode  产品实例编码
     * @param routingStepId 工艺步骤ID
     * @param quantityGood  良品数量
     * @param quantityBad   不良品数量
     * @param operatorId    操作员ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeProduction(String instanceCode, Long routingStepId,
                                      BigDecimal quantityGood, BigDecimal quantityBad, Long operatorId) {
        try {
            // TODO: 集成产品实例查询、工序状态验证、完工记录创建、状态更新、入库触发
            // 需要实现：
            // 验证产品实例状态是否允许完工
            // 检查工序状态和前置工序完成情况
            // 创建完工记录并记录良品/不良品数量
            // 更新产品实例状态和工序进度
            // 判断是否为最后工序，触发入库流程

            log.info("完工报工：实例【{}】工序【{}】良品【{}】不良品【{}】",
                instanceCode, routingStepId, quantityGood, quantityBad);
            return true;
        } catch (Exception e) {
            log.error("完工报工失败：{}", e.getMessage(), e);
            throw new ServiceException("完工报工失败：" + e.getMessage());
        }
    }

    /**
     * 获取产品实例的完整追溯信息
     *
     * @param instanceCode 产品实例编码
     * @return 追溯信息
     */
    public Map<String, Object> getInstanceTraceability(String instanceCode) {
        try {
            Map<String, Object> traceInfo = new HashMap<>();

            // TODO: 集成产品实例、报工记录、物料消耗、质量检验等模块数据构建完整追溯链
            // 需要实现：
            // 获取产品实例的完整生产历程
            // 查询所有相关的报工记录和工序信息
            // 获取物料消耗记录和批次追溯信息
            // 查询质量检验记录和检验结果
            // 构建完整的产品追溯链和质量档案

            log.info("获取产品实例【{}】追溯信息", instanceCode);
            traceInfo.put("instanceCode", instanceCode);
            traceInfo.put("message", "追溯功能需要集成产品实例、报工记录、物料消耗等模块数据");

            return traceInfo;
        } catch (Exception e) {
            log.error("获取追溯信息失败：{}", e.getMessage(), e);
            throw new ServiceException("获取追溯信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新生产进度
     *
     * @param report 生产报工记录
     */
    private void updateProductionProgress(ProductionReport report) {
        try {
            if (report.getOrderId() == null) {
                log.warn("生产报工记录【{}】未关联生产订单，跳过进度更新", report.getReportId());
                return;
            }

            // 计算生产进度
            ProductionProgressInfo progressInfo = calculateProductionProgress(report.getOrderId());

            if (progressInfo != null) {
                // TODO: 将进度信息更新到生产订单
                // 由于不能新增字段，使用临时变量存储进度信息
                updateOrderProgress(report.getOrderId(), progressInfo);

                log.info("生产订单【{}】进度更新：总进度[{}%]，当前工序[{}]，工序进度[{}%]",
                    report.getOrderId(), progressInfo.getTotalProgress(),
                    progressInfo.getCurrentProcessName(), progressInfo.getCurrentProcessProgress());
            }

        } catch (Exception e) {
            log.error("更新生产进度失败：{}", e.getMessage(), e);
            // 进度更新失败不影响主流程，只记录日志
        }
    }

    /**
     * 计算生产进度信息
     *
     * @param orderId 生产订单ID
     * @return 进度信息
     */
    private ProductionProgressInfo calculateProductionProgress(Long orderId) {
        try {
            // 获取该订单的所有报工记录
            LambdaQueryWrapper<ProductionReport> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionReport::getOrderId, orderId);
            wrapper.orderByAsc(ProductionReport::getStepId, ProductionReport::getCreateTime);
            List<ProductionReport> reports = baseMapper.selectList(wrapper);

            if (reports.isEmpty()) {
                return null;
            }

            // 计算总工时和已完成工时
            BigDecimal totalWorkTime = BigDecimal.ZERO;
            BigDecimal completedWorkTime = BigDecimal.ZERO;
            String currentProcessName = "";
            BigDecimal currentProcessProgress = BigDecimal.ZERO;

            // 按工序分组统计
            Map<Long, List<ProductionReport>> processReports = reports.stream()
                .filter(r -> r.getProcessId() != null)
                .collect(Collectors.groupingBy(ProductionReport::getProcessId));

            for (Map.Entry<Long, List<ProductionReport>> entry : processReports.entrySet()) {
                List<ProductionReport> processReportList = entry.getValue();

                // 计算工序的工时
                BigDecimal processWorkTime = calculateProcessWorkTime(processReportList);
                totalWorkTime = totalWorkTime.add(processWorkTime);

                // 检查工序是否完成
                boolean isProcessCompleted = isProcessCompleted(processReportList);
                if (isProcessCompleted) {
                    completedWorkTime = completedWorkTime.add(processWorkTime);
                } else {
                    // 当前正在进行的工序
                    currentProcessName = getProcessName(processReportList);
                    currentProcessProgress = calculateCurrentProcessProgress(processReportList);
                }
            }

            // 计算总进度百分比
            BigDecimal totalProgress = BigDecimal.ZERO;
            if (totalWorkTime.compareTo(BigDecimal.ZERO) > 0) {
                totalProgress = completedWorkTime.divide(totalWorkTime, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            }

            // 创建进度信息对象
            ProductionProgressInfo progressInfo = new ProductionProgressInfo();
            progressInfo.setOrderId(orderId);
            progressInfo.setTotalProgress(totalProgress.setScale(2, RoundingMode.HALF_UP));
            progressInfo.setCurrentProcessName(currentProcessName);
            progressInfo.setCurrentProcessProgress(currentProcessProgress.setScale(2, RoundingMode.HALF_UP));
            progressInfo.setCompletedWorkTime(completedWorkTime);
            progressInfo.setTotalWorkTime(totalWorkTime);

            return progressInfo;

        } catch (Exception e) {
            log.error("计算生产进度失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算工序工时
     */
    private BigDecimal calculateProcessWorkTime(List<ProductionReport> reports) {
        BigDecimal workTime = BigDecimal.ZERO;
        for (ProductionReport report : reports) {
            if (report.getStartTime() != null && report.getEndTime() != null) {
                Duration duration = Duration.between(report.getStartTime(), report.getEndTime());
                workTime = workTime.add(BigDecimal.valueOf(duration.toHours())); // 转换为小时
            }
        }
        return workTime;
    }

    /**
     * 检查工序是否完成
     */
    private boolean isProcessCompleted(List<ProductionReport> reports) {
        return reports.stream().anyMatch(r -> "FINISH".equals(r.getReportType()));
    }

    /**
     * 获取工序名称
     */
    private String getProcessName(List<ProductionReport> reports) {
        return reports.stream()
            .filter(r -> StringUtils.isNotBlank(r.getProcessName()))
            .map(ProductionReport::getProcessName)
            .findFirst()
            .orElse("未知工序");
    }

    /**
     * 计算当前工序进度 - ✅ 启用：优化进度计算算法
     */
    private BigDecimal calculateCurrentProcessProgress(List<ProductionReport> reports) {
        if (reports.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 优化算法：基于工时和数量的综合计算
        BigDecimal totalWorkTime = BigDecimal.ZERO;
        BigDecimal completedWorkTime = BigDecimal.ZERO;
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal completedQuantity = BigDecimal.ZERO;

        for (ProductionReport report : reports) {
            // 累计工时
            // TODO: 在ProductionReport实体中添加getWorkTime()方法
            // if (report.getWorkTime() != null) {
            //    totalWorkTime = totalWorkTime.add(report.getWorkTime());

            // 如果是完工报工，计入完成工时
            //    if ("FINISH".equals(report.getReportType())) {
            //        completedWorkTime = completedWorkTime.add(report.getWorkTime());
            //    }
            // }

            // 累计数量
            // TODO: 在ProductionReport实体中添加getQuantity()方法
            // if (report.getQuantity() != null) {
            //    totalQuantity = totalQuantity.add(report.getQuantity());

            // 如果是完工报工，计入完成数量
            //    if ("FINISH".equals(report.getReportType()) && report.getQualifiedQuantity() != null) {
            //        completedQuantity = completedQuantity.add(report.getQualifiedQuantity());
            //    }
            // }
        }

        // 综合计算进度：工时进度权重60%，数量进度权重40%
        BigDecimal timeProgress = BigDecimal.ZERO;
        BigDecimal quantityProgress = BigDecimal.ZERO;

        if (totalWorkTime.compareTo(BigDecimal.ZERO) > 0) {
            timeProgress = completedWorkTime.divide(totalWorkTime, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }

        if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
            quantityProgress = completedQuantity.divide(totalQuantity, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }

        // 加权平均计算最终进度
        BigDecimal finalProgress = timeProgress.multiply(BigDecimal.valueOf(0.6))
            .add(quantityProgress.multiply(BigDecimal.valueOf(0.4)));

        // 确保进度不超过100%
        if (finalProgress.compareTo(BigDecimal.valueOf(100)) > 0) {
            finalProgress = BigDecimal.valueOf(100);
        }

        return finalProgress.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 更新订单进度
     */
    private void updateOrderProgress(Long orderId, ProductionProgressInfo progressInfo) {
        try {
            // TODO: 调用生产订单服务更新进度信息
            // 由于不能新增字段，这里只记录日志
            log.info("生产订单【{}】进度计算完成：总进度[{}%]，当前工序[{}]，工序进度[{}%]，已完成工时[{}]小时，总工时[{}]小时",
                orderId, progressInfo.getTotalProgress(), progressInfo.getCurrentProcessName(),
                progressInfo.getCurrentProcessProgress(), progressInfo.getCompletedWorkTime(),
                progressInfo.getTotalWorkTime());
        } catch (Exception e) {
            log.error("更新订单进度失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 生产进度信息类（临时变量）
     */
    private static class ProductionProgressInfo {
        private Long orderId;
        private BigDecimal totalProgress;
        private String currentProcessName;
        private BigDecimal currentProcessProgress;
        private BigDecimal completedWorkTime;
        private BigDecimal totalWorkTime;

        // Getters and Setters
        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public BigDecimal getTotalProgress() {
            return totalProgress;
        }

        public void setTotalProgress(BigDecimal totalProgress) {
            this.totalProgress = totalProgress;
        }

        public String getCurrentProcessName() {
            return currentProcessName;
        }

        public void setCurrentProcessName(String currentProcessName) {
            this.currentProcessName = currentProcessName;
        }

        public BigDecimal getCurrentProcessProgress() {
            return currentProcessProgress;
        }

        public void setCurrentProcessProgress(BigDecimal currentProcessProgress) {
            this.currentProcessProgress = currentProcessProgress;
        }

        public BigDecimal getCompletedWorkTime() {
            return completedWorkTime;
        }

        public void setCompletedWorkTime(BigDecimal completedWorkTime) {
            this.completedWorkTime = completedWorkTime;
        }

        public BigDecimal getTotalWorkTime() {
            return totalWorkTime;
        }

        public void setTotalWorkTime(BigDecimal totalWorkTime) {
            this.totalWorkTime = totalWorkTime;
        }
    }
}
