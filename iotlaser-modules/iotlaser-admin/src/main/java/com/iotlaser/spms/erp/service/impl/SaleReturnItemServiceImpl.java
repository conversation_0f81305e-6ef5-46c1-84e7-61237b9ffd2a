package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.bo.SaleReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.mapper.SaleReturnItemMapper;
import com.iotlaser.spms.erp.service.ISaleReturnItemService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售退货明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleReturnItemServiceImpl implements ISaleReturnItemService {

    private final SaleReturnItemMapper baseMapper;
    private final ILocationService locationService;
    @Lazy
    @Autowired
    private ISaleReturnService saleReturnService;

    /**
     * 查询销售退货明细
     *
     * @param itemId 主键
     * @return 销售退货明细
     */
    @Override
    public SaleReturnItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售退货明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货明细分页列表
     */
    @Override
    public TableDataInfo<SaleReturnItemVo> queryPageList(SaleReturnItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleReturnItem> lqw = buildQueryWrapper(bo);
        Page<SaleReturnItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售退货明细列表
     *
     * @param bo 查询条件
     * @return 销售退货明细列表
     */
    @Override
    public List<SaleReturnItemVo> queryList(SaleReturnItemBo bo) {
        LambdaQueryWrapper<SaleReturnItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleReturnItem> buildQueryWrapper(SaleReturnItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleReturnItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleReturnItem::getItemId);
        lqw.eq(bo.getReturnId() != null, SaleReturnItem::getReturnId, bo.getReturnId());
        lqw.eq(bo.getProductId() != null, SaleReturnItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleReturnItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleReturnItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, SaleReturnItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPrice() != null, SaleReturnItem::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleReturnItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        SaleReturnItem add = MapstructUtils.convert(bo, SaleReturnItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改销售退货明细
     *
     * @param bo 销售退货明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        SaleReturnItem update = MapstructUtils.convert(bo, SaleReturnItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleReturnItem entity) {
        // 校验同一退货单中产品不能重复
        if (entity.getReturnId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleReturnItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleReturnItem::getReturnId, entity.getReturnId());
            wrapper.eq(SaleReturnItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleReturnItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售退货单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除销售退货明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验销售退货明细是否可以删除
            List<SaleReturnItem> items = baseMapper.selectByIds(ids);
            for (SaleReturnItem item : items) {
                // 检查主表状态，只有草稿状态的退货明细才能删除
                SaleReturnVo saleReturn = saleReturnService.queryById(item.getReturnId());
                if (saleReturn != null && SaleReturnStatus.DRAFT != saleReturn.getReturnStatus()) {
                    throw new ServiceException("退货明细所属销售退货单【" + saleReturn.getReturnCode() +
                        "】状态为【" + saleReturn.getReturnStatus() + "】，不允许删除明细");
                }
                log.info("删除销售退货明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除销售退货明细失败：" + e.getMessage());
        }
    }

    /**
     * 根据退货单ID查询明细ID列表
     *
     * @param returnId 退货单ID
     * @return 明细ID列表
     */
    @Override
    public List<Long> selectItemIdsByReturnId(Long returnId) {
        LambdaQueryWrapper<SaleReturnItem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(SaleReturnItem::getItemId);
        wrapper.eq(SaleReturnItem::getReturnId, returnId);
        return baseMapper.selectList(wrapper).stream()
            .map(SaleReturnItem::getItemId)
            .collect(Collectors.toList());
    }

    /**
     * 批量插入或更新销售退货明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<SaleReturnItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<SaleReturnItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, SaleReturnItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售退货明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除销售退货明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售退货明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售退货明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleReturnItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }

}
