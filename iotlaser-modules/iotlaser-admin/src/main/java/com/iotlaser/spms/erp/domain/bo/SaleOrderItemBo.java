package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 销售订单明细业务对象 erp_sale_order_item
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleOrderItem.class, reverseConvertGenerate = false)
public class SaleOrderItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编out码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 销售数量
     */
    @NotNull(message = "销售数量不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "销售数量不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "销售数量不能大于*********", groups = {EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已发货数量
     */
    @NotNull(message = "已发货数量不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "已发货数量不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "已发货数量不能大于*********", groups = {EditGroup.class})
    private BigDecimal shippedQuantity;

    /**
     * 已退货数量
     */
    @NotNull(message = "已退货数量不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "已退货数量不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "已退货数量不能大于*********", groups = {EditGroup.class})
    private BigDecimal returnedQuantity;

    /**
     * 单价(含税)
     */
    @NotNull(message = "单价(含税)不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "单价(含税)不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "单价(含税)不能大于*********", groups = {EditGroup.class})
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    @NotNull(message = "单价(不含税)不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "单价(不含税)不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "单价(不含税)不能大于*********", groups = {EditGroup.class})
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    @NotNull(message = "金额(含税)不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "金额(含税)不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "金额(含税)不能大于*********", groups = {EditGroup.class})
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @NotNull(message = "金额(不含税)不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "金额(不含税)不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "金额(不含税)不能大于*********", groups = {EditGroup.class})
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @NotNull(message = "税率不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "税率不能小于0", groups = {EditGroup.class})
    @Max(value = 100, message = "税率不能大于100", groups = {EditGroup.class})
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @NotNull(message = "税额不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "税额不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "税额不能大于*********", groups = {EditGroup.class})
    private BigDecimal taxAmount;

    /**
     * 已开票数量
     */
    @NotNull(message = "已开票数量不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "已开票数量不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "已开票数量不能大于*********", groups = {EditGroup.class})
    private BigDecimal invoicedQuantity;

    /**
     * 已开票金额
     */
    @NotNull(message = "已开票金额不能为空", groups = {EditGroup.class})
    @Min(value = 0, message = "已开票金额不能小于0", groups = {EditGroup.class})
    @Max(value = *********, message = "已开票金额不能大于*********", groups = {EditGroup.class})
    private BigDecimal invoicedAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 排除的产品ID
     */
    private String excludeProductIds;


}
