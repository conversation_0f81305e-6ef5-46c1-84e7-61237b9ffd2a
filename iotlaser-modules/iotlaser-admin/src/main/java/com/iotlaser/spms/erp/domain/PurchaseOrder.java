package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购订单对象 erp_purchase_order
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_order")
public class PurchaseOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 采购订单明细
     */
    @TableField(exist = false)
    List<PurchaseOrderItem> items;
    /**
     * 订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderCode;
    /**
     * 源头ID
     */
    private Long sourceId;
    /**
     * 源头编码
     */
    private String sourceCode;
    /**
     * 源头类型
     */
    private SourceType sourceType;
    /**
     * 上游ID
     */
    private Long directSourceId;
    /**
     * 上游编码
     */
    private String directSourceCode;
    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 金额(含税)
     */
    private BigDecimal amount;
    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 下单日期
     */
    private LocalDate orderDate;
    /**
     * 订单状态
     */
    private PurchaseOrderStatus orderStatus;
    /**
     * 申请人ID
     */
    private Long applicantId;
    /**
     * 申请人
     */
    private String applicantName;
    /**
     * 采购员ID
     */
    private Long handlerId;
    /**
     * 采购员
     */
    private String handlerName;
    /**
     * 审批人ID
     */
    private Long approverId;
    /**
     * 审批人
     */
    private String approverName;
    /**
     * 审批时间
     */
    private LocalDateTime approveTime;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 备注
     */
    private String remark;
    /**
     * 有效状态
     */
    private String status;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
