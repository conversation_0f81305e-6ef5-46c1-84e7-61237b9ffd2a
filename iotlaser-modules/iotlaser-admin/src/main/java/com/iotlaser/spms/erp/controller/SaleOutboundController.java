package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售出库单
 *
 * <AUTHOR>
 */
@Tag(name = "销售出库单")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOutbound")
public class SaleOutboundController extends BaseController {

    private final ISaleOutboundService saleOutboundService;

    /**
     * 查询销售出库列表
     */
    @SaCheckPermission("erp:saleOutbound:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOutboundVo> list(SaleOutboundBo bo, PageQuery pageQuery) {
        return saleOutboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售出库列表
     */
    @SaCheckPermission("erp:saleOutbound:export")
    @Log(title = "销售出库单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOutboundBo bo, HttpServletResponse response) {
        List<SaleOutboundVo> list = saleOutboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售出库", SaleOutboundVo.class, response);
    }

    /**
     * 获取销售出库详细信息
     */
    @SaCheckPermission("erp:saleOutbound:query")
    @GetMapping("/{outboundId}")
    public R<SaleOutboundVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long outboundId) {
        return R.ok(saleOutboundService.queryById(outboundId));
    }

    /**
     * 新增销售出库
     */
    @SaCheckPermission("erp:saleOutbound:add")
    @Log(title = "销售出库单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<SaleOutboundVo> add(@Validated(AddGroup.class) @RequestBody SaleOutboundBo bo) {
        return R.ok(saleOutboundService.insertByBo(bo));
    }

    /**
     * 修改销售出库
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SaleOutboundVo> edit(@Validated(EditGroup.class) @RequestBody SaleOutboundBo bo) {
        return R.ok(saleOutboundService.updateByBo(bo));
    }

    /**
     * 删除销售出库
     */
    @SaCheckPermission("erp:saleOutbound:remove")
    @Log(title = "销售出库单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{outboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] outboundIds) {
        return toAjax(saleOutboundService.deleteWithValidByIds(List.of(outboundIds), true));
    }

    /**
     * 确认销售出库单
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{outboundId}")
    public R<Void> confirm(@NotNull(message = "出库单ID不能为空") @PathVariable Long outboundId) {
        return toAjax(saleOutboundService.confirmOutbound(outboundId));
    }

    /**
     * 完成销售出库单
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{outboundId}")
    public R<Void> complete(@NotNull(message = "出库单ID不能为空") @PathVariable Long outboundId) {
        return toAjax(saleOutboundService.completeOutbound(outboundId));
    }

    /**
     * 取消销售出库单
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{outboundId}")
    public R<Void> cancel(@NotNull(message = "出库单ID不能为空") @PathVariable Long outboundId,
                          @RequestParam(required = false) String reason) {
        return toAjax(saleOutboundService.cancelOutbound(outboundId, reason));
    }

    /**
     * 根据销售出库单创建仓库出库单
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.INSERT)
    @PostMapping("/createOutbound/{outboundId}")
    public R<Void> createOutbound(@NotNull(message = "销售出库单ID不能为空") @PathVariable Long outboundId) {
        return toAjax(saleOutboundService.createOutbound(outboundId));
    }

    /**
     * 根据销售出库单创建退货单
     */
    @SaCheckPermission("erp:saleOutbound:edit")
    @Log(title = "销售出库单", businessType = BusinessType.INSERT)
    @PostMapping("/createReturn/{outboundId}")
    public R<Void> createReturn(@NotNull(message = "销售出库单ID不能为空") @PathVariable Long outboundId) {
        return toAjax(saleOutboundService.createReturn(outboundId));
    }
}
