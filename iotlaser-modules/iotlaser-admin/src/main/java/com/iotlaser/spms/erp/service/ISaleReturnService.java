package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.bo.SaleReturnBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售退货单 服务层
 *
 * <AUTHOR> Kai
 */
public interface ISaleReturnService {

    /**
     * 查询销售退货单
     *
     * @param returnId 主键
     * @return 销售退货单
     */
    SaleReturnVo queryById(Long returnId);

    /**
     * 查询销售退货单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货单分页列表
     */
    TableDataInfo<SaleReturnVo> queryPageList(SaleReturnBo bo, PageQuery pageQuery);

    /**
     * 查询销售退货单列表
     *
     * @param bo 查询条件
     * @return 销售退货单列表
     */
    List<SaleReturnVo> queryList(SaleReturnBo bo);

    /**
     * 新增销售退货单
     *
     * @param bo 包含新销售退货单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    SaleReturnVo insertByBo(SaleReturnBo bo);

    /**
     * 修改销售退货单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    SaleReturnVo updateByBo(SaleReturnBo bo);

    /**
     * 校验并批量删除销售退货单
     *
     * @param ids     待删除的销售退货单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据上游单据ID查询销售退货单列表
     *
     * @param directSourceId 上游单据ID
     * @return 销售退货单列表
     */
    List<SaleReturn> queryByDirectSourceId(Long directSourceId);

    /**
     * 根据退货单ID查询明细项
     *
     * @param returnId 退货单ID
     * @return 退货单明细列表
     */
    List<SaleReturnItem> queryItemByReturnId(Long returnId);

    /**
     * 检查上游单据是否存在关联的销售退货单
     *
     * @param directSourceId 上游单据ID
     * @return 结果
     */
    Boolean existsByDirectSourceId(Long directSourceId);

    /**
     * 确认销售退货单
     *
     * @param returnId 待确认的退货单ID
     * @return 结果
     */
    Boolean confirmReturn(Long returnId);

    /**
     * 收货确认
     *
     * @param returnId 已收到货的退货单ID
     * @return 结果
     */
    Boolean createInbound(Long returnId);

    /**
     * 完成销售退货入库
     *
     * @param returnId 已完成入库的退货单ID
     * @return 结果
     */
    Boolean completeReturn(Long returnId);

    /**
     * 取消销售退货单
     *
     * @param returnId 待取消的退货单ID
     * @param reason   取消原因
     * @return 结果
     */
    Boolean cancelReturn(Long returnId, String reason);

    /**
     * 根据销售出库单创建退货单
     *
     * @param saleOutbound 销售出库单对象
     * @return 结果
     */
    Boolean createFromSaleOutbound(SaleOutbound saleOutbound);

}
