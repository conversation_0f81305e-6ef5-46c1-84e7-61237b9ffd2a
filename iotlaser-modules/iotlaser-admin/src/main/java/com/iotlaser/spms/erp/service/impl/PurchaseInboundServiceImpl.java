package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.event.InboundEvent;
import com.iotlaser.spms.erp.event.PurchaseInboundEvent;
import com.iotlaser.spms.erp.mapper.PurchaseInboundItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseInboundMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_INBOUND_CODE;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 采购入库服务实现
 *
 * <AUTHOR> Kai
 * @version 1.2
 * @since 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundServiceImpl implements IPurchaseInboundService {
    // =================================================================================================================
    // [DDD-TODO] 跨聚合调用优化 - 目标: 实现最终一致性，降低服务间耦合
    // 当前为保持业务流程的同步与完整性，暂时直接依赖其他聚合根的Service。
    // 理想架构应通过领域事件(Domain Event)或应用服务层(Application Service)进行解耦。
    // 例如：订单确认后，发布`PurchaseInboundConfirmedEvent`，由入库上下文的监听器异步订阅并创建入库单。
    // -----------------------------------------------------------------------------------------------------------------
    // 参考文档: docs/design/README_OVERVIEW.md
    // TODO: [REFACTOR] - [HIGH] - 将此处的服务直接依赖重构为领域事件模式。
    // =================================================================================================================
    private final PurchaseInboundMapper baseMapper;
    private final PurchaseInboundItemMapper itemMapper;
    private final Gen gen;
    private final IInboundService inboundService;
    private final IPurchaseReturnService purchaseReturnService;
    @Autowired
    @Lazy
    private IFinApInvoiceService finApInvoiceService;

    /**
     * 根据ID查询采购入库单
     *
     * @param inboundId 采购入库单的唯一主键ID
     * @return 采购入库单的详细视图对象 (VO)，若不存在则返回 {@code null}
     */
    @Override
    public PurchaseInboundVo queryById(Long inboundId) {
        return baseMapper.selectVoById(inboundId);
    }

    /**
     * 分页查询采购入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封装了分页结果的 TableDataInfo 对象
     */
    @Override
    public TableDataInfo<PurchaseInboundVo> queryPageList(PurchaseInboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库单列表
     *
     * @param bo 查询条件业务对象 (BO)
     * @return 采购入库单视图对象 (VO) 的列表
     */
    @Override
    public List<PurchaseInboundVo> queryList(PurchaseInboundBo bo) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseInbound> buildQueryWrapper(PurchaseInboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(PurchaseInbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), PurchaseInbound::getInboundCode, bo.getInboundCode());
        lqw.eq(bo.getSourceId() != null, PurchaseInbound::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseInbound::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseInbound::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseInbound::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseInbound::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseInbound::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseInbound::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseInbound::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getInboundTime() != null, PurchaseInbound::getInboundTime, bo.getInboundTime());
        if (bo.getInboundStatus() != null) {
            lqw.eq(PurchaseInbound::getInboundStatus, bo.getInboundStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginInboundTime") != null && params.get("endInboundTime") != null,
            PurchaseInbound::getInboundTime, params.get("beginInboundTime"), params.get("endInboundTime"));
        return lqw;
    }

    /**
     * 新增采购入库单
     *
     * @param bo 包含新采购入库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseInboundVo insertByBo(PurchaseInboundBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getInboundCode())) {
                bo.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));
            }
            if (bo.getInboundStatus() == null) {
                bo.setInboundStatus(PurchaseInboundStatus.DRAFT);
            }
            if (bo.getInboundTime() == null) {
                bo.setInboundTime(LocalDateTime.now());
            }
            fillRedundantFields(bo);
            PurchaseInbound add = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(add);

            boolean saved = baseMapper.insert(add) > 0;
            if (!saved) {
                throw new ServiceException("新增采购入库失败");
            }
            bo.setInboundId(add.getInboundId());
            return MapstructUtils.convert(add, PurchaseInboundVo.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 修改采购入库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseInboundVo updateByBo(PurchaseInboundBo bo) {
        try {
            fillRedundantFields(bo);
            PurchaseInbound update = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(update);

            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getInboundId());
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());

            boolean updated = baseMapper.updateById(update) > 0;
            if (!updated) {
                throw new ServiceException("修改采购入库失败");
            }
            return MapstructUtils.convert(update, PurchaseInboundVo.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体
     */
    private void validEntityBeforeSave(PurchaseInbound entity) {
        // 业务规则1: 校验退货单编码唯一性
        if (StringUtils.isNotBlank(entity.getInboundCode())) {
            LambdaQueryWrapper<PurchaseInbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseInbound::getInboundCode, entity.getInboundCode());
            if (entity.getInboundId() != null) {
                wrapper.ne(PurchaseInbound::getInboundId, entity.getInboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("采购入库单编码 [" + entity.getInboundCode() + "] 已存在");
            }
        }
    }

    /**
     * 校验并批量删除采购入库单
     *
     * @param ids     待删除的采购入库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        try {
            if (isValid) {
                List<PurchaseInbound> inbounds = baseMapper.selectByIds(ids);
                for (PurchaseInbound inbound : inbounds) {
                    if (PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
                        throw new ServiceException(String.format("单据【%s】状态非草稿，无法删除", inbound.getInboundCode()));
                    }
                }
            }
            // 删除明细
            itemMapper.deleteByInboundIds(ids);
            // 删除主表
            return baseMapper.deleteByIds(ids) > 0;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 检查是否存在采购入库单
     *
     * @param orderId 采购订单ID
     * @return 是否存在
     */
    @Override
    public Boolean existsByDirectSourceId(Long orderId) {
        return baseMapper.existsByDirectSourceId(orderId);
    }

    /**
     * 根据上游ID查询所有关联的采购入库单
     *
     * @param directSourceId 上游ID
     * @return 采购入库单实体列表
     */
    @Override
    public List<PurchaseInbound> queryByDirectSourceId(Long directSourceId) {
        return baseMapper.queryByDirectSourceId(directSourceId);
    }

    /**
     * 根据入库单ID查询其所有明细项
     *
     * @param inboundId 入库单ID
     * @return 入库单明细实体列表
     */
    @Override
    public List<PurchaseInboundItem> queryByInboundId(Long inboundId) {
        return itemMapper.queryByInboundId(inboundId);
    }

    /**
     * 确认采购入库单
     *
     * @param inboundId 待确认的入库单ID
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmInbound(Long inboundId) {
        try {
            R<PurchaseInbound> inboundValid = inboundValid(inboundId, false);
            // 校验退货单是否满足确认条件
            if (R.isError(inboundValid)) {
                throw new ServiceException(inboundValid.getMsg());
            }
            PurchaseInbound purchaseInbound = inboundValid.getData();
            if (PurchaseInboundStatus.DRAFT != purchaseInbound.getInboundStatus()) {
                throw new ServiceException("确认失败：采购入库单 [" + purchaseInbound.getInboundCode() + "] 状态为“" + purchaseInbound.getInboundStatus().getName() + "”，仅草稿状态可确认");
            }
            // 重新计算并更新总金额
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(inboundId);
            purchaseInbound.setAmount(calculated.getAmount());
            purchaseInbound.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            purchaseInbound.setTaxAmount(calculated.getTaxAmount());
            purchaseInbound.setInboundStatus(PurchaseInboundStatus.PENDING_WAREHOUSE);
            boolean confirmed = baseMapper.updateById(purchaseInbound) > 0;
            if (!confirmed) {
                throw new ServiceException("确认失败");
            }
            createInboundFromConfirm(purchaseInbound);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 待确认的入库单ID集合
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmInbounds(Collection<Long> inboundIds) {
        try {
            for (Long inboundId : inboundIds) {
                confirmInbound(inboundId);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 完成采购入库
     *
     * @param inboundId 已完成入库的采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeInbound(Long inboundId) {
        try {
            R<PurchaseInbound> validBefore = inboundValid(inboundId, true);
            if (R.isError(validBefore)) {
                log.error("前置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validBefore.getMsg());
            }
            PurchaseInbound beforeData = validBefore.getData();
            if (PurchaseInboundStatus.PENDING_WAREHOUSE != beforeData.getInboundStatus()) {
                throw new ServiceException("采购入库单 [" + beforeData.getInboundCode() + "] 状态不是待入库");
            }
            // 更新beforeData内明细的实收数量
            updateActualQuantities(beforeData);
            R<PurchaseInbound> validAfter = inboundValid(inboundId, true);
            if (R.isError(validAfter)) {
                log.error("后置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validAfter.getMsg());
            }
            PurchaseInbound afterData = validAfter.getData();
            // 更新采购入库单状态
            String remark = String.format(" [手动完成-%s]", DateUtils.getTime());
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            afterData.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            boolean completed = baseMapper.updateById(afterData) > 0;
            if (!completed) {
                throw new ServiceException("完成失败");
            }
            triggerNextStreamProcesses(afterData);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 采购入库单确认后创建仓库入库单
     *
     * @param inbound 采购入库单
     */
    private void createInboundFromConfirm(PurchaseInbound inbound) {
        try {
            log.info("开始创建仓库入库单, 来源: {}", inbound.getInboundCode());
            R<PurchaseInbound> inboundValid = inboundValid(inbound, false);
            if (R.isError(inboundValid)) {
                throw new ServiceException(inboundValid.getMsg());
            }
            boolean result = inboundService.createFromPurchaseInbound(inbound);
            if (!result) {
                throw new ServiceException("创建失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 手动从采购入库单创建仓库入库单
     *
     * @param purchaseInboundId 采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createInbound(Long purchaseInboundId) {
        try {
            log.info("开始 - 采购入库单ID: {}", purchaseInboundId);
            R<PurchaseInbound> inboundValid = inboundValid(purchaseInboundId, false);
            if (R.isError(inboundValid)) {
                throw new ServiceException(inboundValid.getMsg());
            }
            PurchaseInbound purchaseInbound = inboundValid.getData();
            boolean result = inboundService.createFromPurchaseInbound(purchaseInbound);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 手动从采购入库单创建采购退货单
     *
     * @param purchaseInboundId 已完成的采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createPurchaseReturn(Long purchaseInboundId) {
        try {
            log.info("开始 - 采购入库单ID: {}", purchaseInboundId);
            R<PurchaseInbound> inboundValid = inboundValid(purchaseInboundId, true);
            if (R.isError(inboundValid)) {
                throw new ServiceException(inboundValid.getMsg());
            }
            PurchaseInbound inboundVo = inboundValid.getData();
            boolean result = purchaseReturnService.createFromPurchaseInbound(inboundVo);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 手动从采购入库单创建财务应付单
     *
     * @param purchaseInboundId 已完成的采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFinApInvoice(Long purchaseInboundId) {
        try {
            R<PurchaseInbound> inboundValid = inboundValid(purchaseInboundId, true);
            if (R.isError(inboundValid)) {
                throw new ServiceException(inboundValid.getMsg());
            }
            PurchaseInbound purchaseInbound = inboundValid.getData();
            boolean result = finApInvoiceService.createFromPurchaseInbound(purchaseInbound);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 从采购订单创建采购入库单
     *
     * @param purchaseOrder 已确认的采购订单实体
     * @return 操作成功返回 {@code true}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder) {
        try {
            if (PurchaseOrderStatus.CONFIRMED != purchaseOrder.getOrderStatus() &&
                PurchaseOrderStatus.PARTIALLY_RECEIVED != purchaseOrder.getOrderStatus()) {
                throw new ServiceException("创建失败：采购订单状态 [" + purchaseOrder.getOrderStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(purchaseOrder.getOrderId());
            if (exists) {
                throw new ServiceException("创建失败：采购订单 [" + purchaseOrder.getOrderCode() + "] 已生成过采购入库单，不能重复创建");
            }
            LoginUser loginUser = getLoginUser();
            PurchaseInbound add = new PurchaseInbound();
            add.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));
            add.setSourceId(purchaseOrder.getSourceId());
            add.setSourceCode(purchaseOrder.getSourceCode());
            add.setSourceType(purchaseOrder.getSourceType());
            add.setDirectSourceId(purchaseOrder.getOrderId());
            add.setDirectSourceCode(purchaseOrder.getOrderCode());
            add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
            add.setSupplierId(purchaseOrder.getSupplierId());
            add.setSupplierName(purchaseOrder.getSupplierName());
            add.setInboundStatus(PurchaseInboundStatus.DRAFT);
            add.setInboundTime(LocalDateTime.now());
            if (loginUser != null) {
                add.setHandlerId(loginUser.getUserId());
                add.setHandlerName(loginUser.getNickname());
            }
            add.setSummary(String.format("基于采购订单【%s】创建", purchaseOrder.getOrderCode()));

            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                throw new ServiceException("创建主记录失败");
            }

            List<PurchaseInboundItem> inboundItems = new ArrayList<>();
            for (PurchaseOrderItem purchaseOrderItem : purchaseOrder.getItems()) {
                PurchaseInboundItem inboundItem = new PurchaseInboundItem();
                inboundItem.setInboundId(add.getInboundId());
                inboundItem.setProductId(purchaseOrderItem.getProductId());
                inboundItem.setProductCode(purchaseOrderItem.getProductCode());
                inboundItem.setProductName(purchaseOrderItem.getProductName());
                inboundItem.setUnitId(purchaseOrderItem.getUnitId());
                inboundItem.setUnitCode(purchaseOrderItem.getUnitCode());
                inboundItem.setUnitName(purchaseOrderItem.getUnitName());
                inboundItem.setQuantity(purchaseOrderItem.getQuantity());
                inboundItem.setFinishQuantity(BigDecimal.ZERO);
                inboundItem.setPrice(purchaseOrderItem.getPrice());
                inboundItem.setPriceExclusiveTax(purchaseOrderItem.getPriceExclusiveTax());
                inboundItem.setAmount(purchaseOrderItem.getAmount());
                inboundItem.setAmountExclusiveTax(purchaseOrderItem.getAmountExclusiveTax());
                inboundItem.setTaxRate(purchaseOrderItem.getTaxRate());
                inboundItem.setTaxAmount(purchaseOrderItem.getTaxAmount());
                inboundItems.add(inboundItem);
            }
            boolean itemResult = itemMapper.insertBatch(inboundItems);
            if (!itemResult) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
            // 记录异常并抛给上层处理
        }
    }

    /**
     * 填充冗余字段
     *
     * @param bo 业务对象
     */
    private void fillRedundantFields(PurchaseInboundBo bo) {
        // 获取当前登录用户信息
        LoginUser loginUser = getLoginUser();
        if (loginUser != null) {
            // 如果收货负责人为空，设置为当前用户
            if (bo.getHandlerId() == null) {
                bo.setHandlerId(loginUser.getUserId());
                bo.setHandlerName(loginUser.getNickname());
            }
        }
    }

    /**
     * 仓库入库完成事件
     *
     * @param event 事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeInboundEvent(InboundEvent event) {
        try {
            Inbound inbound = event.getInbound();
            if (inbound == null || inbound.getDirectSourceType() != DirectSourceType.PURCHASE_INBOUND) {
                return;
            }
            Long purchaseInboundId = inbound.getDirectSourceId();
            R<PurchaseInbound> validBefore = inboundValid(purchaseInboundId, false);
            if (R.isError(validBefore)) {
                log.error("前置验证失败: {}", validBefore.getMsg());
                return;
            }
            PurchaseInbound beforeData = validBefore.getData();
            if (beforeData.getInboundStatus() == PurchaseInboundStatus.COMPLETED) {
                return;
            }
            // 更新beforeData内明细的实收数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<PurchaseInbound> validAfter = inboundValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.error("后置验证失败: {}", validAfter.getMsg());
                return;
            }
            // 更新采购入库单状态为已完成
            PurchaseInbound afterData = validAfter.getData();
            String remark = String.format(" [自动完成-仓库入库-%s-%s]", inbound.getInboundCode(), DateUtils.getTime());
            afterData.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean complete = baseMapper.updateById(afterData) > 0;
            if (!complete) {
                throw new ServiceException("完成失败");
            }
            triggerNextStreamProcesses(afterData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 更新采购入库单明细实收数量
     *
     * @param purchaseInbound 采购入库单
     */
    private void updateActualQuantities(PurchaseInbound purchaseInbound) {
        try {
            List<Inbound> inbounds = inboundService.queryCompleteByDirectSourceId(purchaseInbound.getInboundId(), DirectSourceType.PURCHASE_INBOUND);
            Map<Long, BigDecimal> finishQuantityMap = new HashMap<>();
            for (Inbound inbound : inbounds) {
                List<InboundItem> inboundItems = inboundService.queryItemByInboundId(inbound.getInboundId());
                for (InboundItem inboundItem : inboundItems) {
                    finishQuantityMap.merge(inboundItem.getDirectSourceItemId(), inboundItem.getFinishQuantity(), BigDecimal::add);
                }
            }

            List<PurchaseInboundItem> updates = new ArrayList<>();
            for (PurchaseInboundItem purchaseInboundItem : purchaseInbound.getItems()) {
                BigDecimal finishQuantity = finishQuantityMap.getOrDefault(purchaseInboundItem.getItemId(), BigDecimal.ZERO);
                if (finishQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    purchaseInboundItem.setFinishQuantity(finishQuantity);
                    updates.add(purchaseInboundItem);
                }
            }

            boolean result = itemMapper.updateBatchById(updates);
            if (!result) {
                throw new ServiceException("更新失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 触发后续业务流程
     *
     * @param purchaseInbound 采购入库单
     */
    private void triggerNextStreamProcesses(PurchaseInbound purchaseInbound) {
        try {
            // 触发采购入库完成事件
            PurchaseInboundEvent event = new PurchaseInboundEvent();
            event.setPurchaseInbound(purchaseInbound);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }


    /**
     * 确认入库和创建入库前置验证
     *
     * @param inboundId  采购入库单ID
     * @param isComplete 是否为完成操作
     * @return 验证通过的采购入库单
     */
    private R<PurchaseInbound> inboundValid(Long inboundId, boolean isComplete) {
        if (inboundId == null) {
            return R.fail("采购入库ID不能为空");
        }
        PurchaseInbound inbound = baseMapper.selectById(inboundId);
        return inboundValid(inbound, isComplete);
    }

    /**
     * 确认入库和创建入库前置验证
     *
     * @param inbound    采购入库单
     * @param isComplete 是否为完成操作
     * @return 验证通过的采购入库单
     */
    private R<PurchaseInbound> inboundValid(PurchaseInbound inbound, boolean isComplete) {
        if (inbound == null) {
            return R.fail("采购入库单不存在");
        }
        List<PurchaseInboundItem> items = inbound.getItems();
        if (items == null || items.isEmpty()) {
            items = itemMapper.queryByInboundId(inbound.getInboundId());
            if (items == null || items.isEmpty()) {
                return R.fail("入库明细不能为空");
            }
        }
        for (PurchaseInboundItem item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("入库明细【" + item.getProductName() + "】应入库数量不能小于等于0");
            }
            if (isComplete) {
                if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("入库明细【" + item.getProductName() + "】实收数量不能小于等于0");
                }
                if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                    return R.fail("入库明细【" + item.getProductName() + "】实收数量不能大于或小于应入库数量");
                }
                if (item.getPrice() == null || item.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("入库明细【" + item.getProductName() + "】单价不能小于或等于0");
                }
                if (item.getAmount() == null || item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("入库明细【" + item.getProductName() + "】金额不能小于或等于0");
                }
            }
        }
        inbound.setItems(items);
        return R.ok(inbound);
    }
}
