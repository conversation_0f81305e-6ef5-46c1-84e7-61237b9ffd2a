package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 付款单与发票核销关系Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinApPaymentInvoiceLinkService {

    /**
     * 查询付款单与发票核销关系
     *
     * @param linkId 主键
     * @return 付款单与发票核销关系
     */
    FinApPaymentInvoiceLinkVo queryById(Long linkId);

    /**
     * 分页查询付款单与发票核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单与发票核销关系分页列表
     */
    TableDataInfo<FinApPaymentInvoiceLinkVo> queryPageList(FinApPaymentInvoiceLinkBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的付款单与发票核销关系列表
     *
     * @param bo 查询条件
     * @return 付款单与发票核销关系列表
     */
    List<FinApPaymentInvoiceLinkVo> queryList(FinApPaymentInvoiceLinkBo bo);

    /**
     * 新增付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApPaymentInvoiceLinkBo bo);

    /**
     * 修改付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApPaymentInvoiceLinkBo bo);

    /**
     * 校验并批量删除付款单与发票核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
