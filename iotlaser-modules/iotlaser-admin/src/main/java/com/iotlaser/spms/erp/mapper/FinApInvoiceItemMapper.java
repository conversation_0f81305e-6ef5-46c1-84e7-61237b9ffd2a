package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.FinApInvoiceItem;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 应付明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface FinApInvoiceItemMapper extends BaseMapperPlus<FinApInvoiceItem, FinApInvoiceItemVo> {

    default List<FinApInvoiceItemVo> queryByInvoiceId(Long invoiceId) {
        return selectVoList(new LambdaQueryWrapper<>(FinApInvoiceItem.class).eq(FinApInvoiceItem::getInvoiceId, invoiceId));
    }

    default int deleteByInvoiceIds(Collection<Long> invoiceIds) {
        return delete(new LambdaQueryWrapper<>(FinApInvoiceItem.class).in(FinApInvoiceItem::getInvoiceId, invoiceIds));
    }

    /**
     * 计算明细总金额
     *
     * @param invoiceId 应收单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long invoiceId) {
        List<FinApInvoiceItemVo> items = Optional.ofNullable(queryByInvoiceId(invoiceId)).orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (FinApInvoiceItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }
}
