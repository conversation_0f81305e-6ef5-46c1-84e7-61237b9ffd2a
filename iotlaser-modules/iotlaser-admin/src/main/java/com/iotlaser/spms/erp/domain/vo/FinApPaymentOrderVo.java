package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinApPaymentOrder;
import com.iotlaser.spms.erp.enums.FinAccountType;
import com.iotlaser.spms.erp.enums.FinApPaymentOrderStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 付款单视图对象 erp_fin_ap_payment_order
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApPaymentOrder.class)
public class FinApPaymentOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 付款ID
     */
    @ExcelProperty(value = "付款ID")
    private Long paymentId;

    /**
     * 付款编码
     */
    @ExcelProperty(value = "付款编码")
    private String paymentCode;

    /**
     * 收款方类型
     */
    @ExcelProperty(value = "收款方类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_ap_invoice_payee_type")
    private FinPayeeType payeeType;

    /**
     * 收款方ID
     */
    @ExcelProperty(value = "收款方ID")
    private Long payeeId;

    /**
     * 收款方名称
     */
    @ExcelProperty(value = "收款方名称")
    private String payeeName;

    /**
     * 账号ID
     */
    @ExcelProperty(value = "账号ID")
    private Long accountId;

    /**
     * 账户编码
     */
    @ExcelProperty(value = "账户编码")
    private String accountCode;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @ExcelProperty(value = "账户类型")
    private FinAccountType accountType;

    /**
     * 付款金额
     */
    @ExcelProperty(value = "付款金额")
    private BigDecimal paymentAmount;

    /**
     * 付款方式
     */
    @ExcelProperty(value = "付款方式")
    private FinPaymentMethod paymentMethod;

    /**
     * 付款时间
     */
    @ExcelProperty(value = "付款时间")
    private LocalDate paymentDate;

    /**
     * 银行交易流水号
     */
    @ExcelProperty(value = "银行交易流水号")
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    @ExcelProperty(value = "已核销金额")
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    @ExcelProperty(value = "未核销金额")
    private BigDecimal unappliedAmount;

    /**
     * 付款状态
     */
    @ExcelProperty(value = "付款状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_ap_payment_status")
    private FinApPaymentOrderStatus paymentStatus;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
