package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import com.iotlaser.spms.erp.service.IPurchaseReturnItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购退货明细
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseReturnItem")
public class PurchaseReturnItemController extends BaseController {

    private final IPurchaseReturnItemService purchaseReturnItemService;

    /**
     * 查询采购退货明细列表
     */
    @SaCheckPermission("erp:purchaseReturnItem:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseReturnItemVo> list(PurchaseReturnItemBo bo, PageQuery pageQuery) {
        return purchaseReturnItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购退货明细列表
     */
    @SaCheckPermission("erp:purchaseReturnItem:export")
    @Log(title = "采购退货明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseReturnItemBo bo, HttpServletResponse response) {
        List<PurchaseReturnItemVo> list = purchaseReturnItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购退货明细", PurchaseReturnItemVo.class, response);
    }

    /**
     * 获取采购退货明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:purchaseReturnItem:query")
    @GetMapping("/{itemId}")
    public R<PurchaseReturnItemVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long itemId) {
        return R.ok(purchaseReturnItemService.queryById(itemId));
    }

    /**
     * 新增采购退货明细
     */
    @SaCheckPermission("erp:purchaseReturnItem:add")
    @Log(title = "采购退货明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<PurchaseReturnItemBo> bos) {
        return toAjax(purchaseReturnItemService.insertOrUpdateBatch(bos));
    }

    /**
     * 新增采购退货明细
     */
    @SaCheckPermission("erp:purchaseReturnItem:add")
    @Log(title = "采购退货明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseReturnItemBo bo) {
        return toAjax(purchaseReturnItemService.insertByBo(bo));
    }

    /**
     * 修改采购退货明细
     */
    @SaCheckPermission("erp:purchaseReturnItem:edit")
    @Log(title = "采购退货明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseReturnItemBo bo) {
        return toAjax(purchaseReturnItemService.updateByBo(bo));
    }

    /**
     * 删除采购退货明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:purchaseReturnItem:remove")
    @Log(title = "采购退货明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(purchaseReturnItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

}
