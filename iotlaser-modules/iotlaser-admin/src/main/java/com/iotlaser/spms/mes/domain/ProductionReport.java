package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产报工记录对象 mes_production_report
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_report")
public class ProductionReport extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报工ID
     */
    @TableId(value = "report_id")
    private Long reportId;

    /**
     * 生产订单ID
     */
    private Long orderId;

    /**
     * 实例ID
     */
    private Long instanceId;

    /**
     * 步骤ID
     */
    private Long stepId;

    /**
     * 工序ID
     */
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 报工类型
     */
    private String reportType;

    /**
     * 良品数量
     */
    private BigDecimal quantityGood;

    /**
     * 不良品数量
     */
    private BigDecimal quantityBad;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 报工操作员ID
     */
    private Long operatorId;

    /**
     * 报工操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
