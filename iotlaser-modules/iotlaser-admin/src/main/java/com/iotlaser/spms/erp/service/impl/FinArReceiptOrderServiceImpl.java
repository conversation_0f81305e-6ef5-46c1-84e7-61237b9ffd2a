package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.FinArReceiptOrder;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptOrderVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.enums.FinArReceiptOrderStatus;
import com.iotlaser.spms.erp.mapper.FinArReceiptOrderMapper;
import com.iotlaser.spms.erp.mapper.FinArReceiptReceivableLinkMapper;
import com.iotlaser.spms.erp.service.IFinAccountLedgerService;
import com.iotlaser.spms.erp.service.IFinArReceiptOrderService;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_FIN_AR_RECEIPT_ORDER_CODE;

/**
 * 收款单服务实现
 * <p>
 * 核心职责: 作为收款单聚合根的管理者，本服务封装了收款单从创建到核销完成的全生命周期业务规则。
 * 它确保了所有操作的原子性、一致性和业务逻辑的正确性，是“销售到回款”流程的核心环节。
 * <p>
 * 主要功能:
 * <ul>
 *     <li><b>生命周期管理</b>: 负责收款单的创建 ({@link #insertByBo}), 更新 ({@link #updateByBo}), 删除 ({@link #deleteWithValidByIds})。</li>
 *     <li><b>状态流转</b>: 控制单据状态的合法转换，如确认 ({@link #confirmOrder}), 取消 ({@link #cancelOrder}), 关闭 ({@link #closeOrder})。</li>
 *     <li><b>核心业务 - 核销</b>: 实现收款单与应收单的核销 ({@link #applyToReceivable}) 与反核销 ({@link #reverseApply})。</li>
 *     <li><b>财务集成</b>: 在核销完成后，调用财务总账服务，生成真实的资金流水记录 ({@link #createAccountLedger})。</li>
 *     <li><b>查询服务</b>: 提供丰富的查询接口 ({@link #queryById}, {@link #queryPageList})。</li>
 * </ul>
 *
 * <AUTHOR> Kai
 * @version 1.1
 * @see com.iotlaser.spms.erp.service.IFinArReceiptOrderService
 * @see com.iotlaser.spms.erp.domain.FinArReceiptOrder
 * @see com.iotlaser.spms.erp.controller.FinArReceiptOrderController
 * @see docs/design/README_FINANCE.md#23-销售到应收集成流程图-integrated-process-flow
 * @see docs/design/README_STATE.md#75-收款单-erpfinarreceiptorder
 * @since 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceiptOrderServiceImpl implements IFinArReceiptOrderService {

    private final FinArReceiptOrderMapper baseMapper;

    // =================================================================================================================
    // [DDD-TODO] 跨聚合调用优化 - 目标: 实现最终一致性，降低服务间耦合
    // 当前为保持业务流程的同步与完整性，暂时直接依赖其他聚合根的Service。
    // 理想架构应通过领域事件(Domain Event)或应用服务层(Application Service)进行解耦。
    // 例如：核销完成后，发布`ReceiptAppliedEvent`，由总账上下文的监听器异步订阅并创建流水。
    // -----------------------------------------------------------------------------------------------------------------
    // 参考文档: docs/design/README_OVERVIEW.md
    // TODO: [REFACTOR] - [HIGH] - 将此处的服务直接依赖重构为领域事件模式。
    // =================================================================================================================

    /**
     * 应收单服务 (ERP财务层)
     * <p>
     * <b>[合理依赖]</b> 在核销流程中，需要查询和更新应收单的状态和金额。
     */
    private final IFinArReceivableService finArReceivableService;

    /**
     * 收款-应收关联服务 (ERP财务层)
     * <p>
     * <b>[合理依赖]</b> 用于创建、删除和查询核销的关联记录。
     */
    private final FinArReceiptReceivableLinkMapper linkMapper;

    /**
     * 财务总账服务 (ERP财务层)
     * <p>
     * <b>[临时依赖]</b> 用于在收款核销完成后，记录实际的资金流水。
     * <b>[重构方向]</b> 应替换为发布【收款已核销】的领域事件。
     */
    private final IFinAccountLedgerService finAccountLedgerService;

    /**
     * 公司/往来单位服务 (BASE层)
     * <p>
     * <b>[合理依赖]</b> 用于查询客户等基础数据，填充冗余字段以优化查询性能。
     */
    private final ICompanyService companyService;

    /**
     * 统一编码生成器 (BASE层)
     * <p>
     * <b>[合理依赖]</b> 用于生成符合业务规则的、全局唯一的收款单编码。
     */
    private final Gen gen;


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyToReceivable(Long receiptOrderId, Long receivableId, BigDecimal writeoffAmount, Long operatorId, String operatorName) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[核销] - 开始. 操作人: {}, 收款单ID: {}, 应收单ID: {}, 核销金额: {}", currentUser, receiptOrderId, receivableId, writeoffAmount);

        try {
            // 1. 校验核销金额
            if (writeoffAmount == null || writeoffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销失败：核销金额必须大于0");
            }

            // 2. 锁定并校验收款单
            FinArReceiptOrder receiptOrder = baseMapper.selectById(receiptOrderId);
            if (receiptOrder == null) {
                throw new ServiceException("核销失败：ID为 [" + receiptOrderId + "] 的收款单不存在");
            }
            if (receiptOrder.getUnappliedAmount().compareTo(writeoffAmount) < 0) {
                throw new ServiceException("核销失败：核销金额 [" + writeoffAmount + "] 不能大于收款单未核销金额 [" + receiptOrder.getUnappliedAmount() + "]");
            }
            // 状态必须是 UNAPPLIED 或 PARTIALLY_APPLIED
            if (receiptOrder.getReceiptStatus() != FinArReceiptOrderStatus.UNAPPLIED && receiptOrder.getReceiptStatus() != FinArReceiptOrderStatus.PARTIALLY_APPLIED) {
                throw new ServiceException("核销失败：收款单 [" + receiptOrder.getReceiptCode() + "] 当前状态 “" + receiptOrder.getReceiptStatus().getDesc() + "” 不允许核销");
            }

            // 3. 锁定并校验应收单
            FinArReceivableVo receivable = finArReceivableService.queryById(receivableId);
            if (receivable == null) {
                throw new ServiceException("核销失败：ID为 [" + receivableId + "] 的应收单不存在");
            }
            if (receivable.getOutstandingAmount().compareTo(writeoffAmount) < 0) {
                throw new ServiceException("核销失败：核销金额 [" + writeoffAmount + "] 不能大于应收单未付金额 [" + receivable.getOutstandingAmount() + "]");
            }

            // 4. 创建核销关联记录
            FinArReceiptReceivableLink link = new FinArReceiptReceivableLink();
            link.setReceiptId(receiptOrderId);
            link.setReceivableId(receivableId);
            link.setAppliedAmount(writeoffAmount);
            link.setCancellationDate(LocalDate.now());
            linkMapper.insert(link);
            log.info("【收款单】[核销] - 创建核销关联记录成功. 收款单ID: {}, 应收单ID: {}", receiptOrderId, receivableId);

            // 5. 更新收款单金额与状态
            updateReceiptStatusByAmount(receiptOrderId);

            // 6. 更新应收单金额与状态
            finArReceivableService.updateAppliedAmountAndStatus(receivableId);

            log.info("【收款单】[核销] - 成功. 操作人: {}, 收款单: {}, 应收单: {}", currentUser, receiptOrder.getReceiptCode(), receivable.getReceivableCode());
            return true;
        } catch (ServiceException se) {
            log.warn("【收款单】[核销] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[核销] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("核销操作时发生未知系统错误，请联系管理员");
        }
    }


    @Override
    public FinArReceiptOrderVo queryById(Long receiptId) {
        return baseMapper.selectVoById(receiptId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<FinArReceiptOrderVo> queryPageList(FinArReceiptOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceiptOrder> lqw = buildQueryWrapper(bo);
        Page<FinArReceiptOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<FinArReceiptOrderVo> queryList(FinArReceiptOrderBo bo) {
        LambdaQueryWrapper<FinArReceiptOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoPage(null, lqw);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchApplyToReceivables(List<FinArReceiptReceivableLink> writeoffItems,
                                                       Long operatorId, String operatorName) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[批量核销] - 开始. 操作人: {}, 核销项数: {}", currentUser, writeoffItems.size());
        int successCount = 0;
        int failureCount = 0;
        for (FinArReceiptReceivableLink item : writeoffItems) {
            try {
                boolean result = applyToReceivable(item.getReceiptId(), item.getReceivableId(), item.getAppliedAmount(), operatorId, operatorName);
                if (result) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("【收款单】[批量核销] - 单项失败. 收款单ID: {}, 应收单ID: {}, 错误: {}", item.getReceiptId(), item.getReceivableId(), e.getMessage());
                failureCount++;
            }
        }
        log.info("【收款单】[批量核销] - 完成. 操作人: {}, 成功: {}, 失败: {}", currentUser, successCount, failureCount);
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        return result;
    }

    @Override
    public Boolean cancelWriteoff(Long writeoffId, Long operatorId, String operatorName) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getWriteoffRecords(Long receiptOrderId) {
        return List.of();
    }

    @Override
    public List<FinArReceivableVo> getWriteoffableReceivables(Long customerId, BigDecimal amount) {
        return List.of();
    }

    @Override
    public Boolean updateReceiptOrderStatus(Long receiptOrderId, String newStatus) {
        return null;
    }

    private LambdaQueryWrapper<FinArReceiptOrder> buildQueryWrapper(FinArReceiptOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceiptOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(FinArReceiptOrder::getReceiptId);

        lqw.eq(StringUtils.isNotBlank(bo.getReceiptCode()), FinArReceiptOrder::getReceiptCode, bo.getReceiptCode());
        lqw.eq(bo.getCustomerId() != null, FinArReceiptOrder::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), FinArReceiptOrder::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getPaymentDate() != null, FinArReceiptOrder::getPaymentDate, bo.getPaymentDate());
        lqw.eq(bo.getPaymentAmount() != null, FinArReceiptOrder::getPaymentAmount, bo.getPaymentAmount());
        if (bo.getPaymentMethod() != null) {
            lqw.eq(FinArReceiptOrder::getPaymentMethod, bo.getPaymentMethod().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getBankSerialNumber()), FinArReceiptOrder::getBankSerialNumber, bo.getBankSerialNumber());
        lqw.eq(bo.getAppliedAmount() != null, FinArReceiptOrder::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getUnappliedAmount() != null, FinArReceiptOrder::getUnappliedAmount, bo.getUnappliedAmount());
        if (bo.getReceiptStatus() != null) {
            lqw.eq(FinArReceiptOrder::getReceiptStatus, bo.getReceiptStatus().getValue());
        }
        // TODO: FinArReceiptOrder实体需要新增handlerId和handlerName字段
        // lqw.eq(bo.getHandlerId() != null, FinArReceiptOrder::getHandlerId, bo.getHandlerId());
        // lqw.like(StringUtils.isNotBlank(bo.getHandlerName()), FinArReceiptOrder::getHandlerName, bo.getHandlerName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceiptOrder::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinArReceiptOrderVo insertByBo(FinArReceiptOrderBo bo) {


        try {
            // 1. 初始化单据编码
            if (StringUtils.isEmpty(bo.getReceiptCode())) {
                bo.setReceiptCode(gen.code(ERP_FIN_AR_RECEIPT_ORDER_CODE));

            }
            // 2. 设置初始状态为草稿
            if (bo.getReceiptStatus() == null) {
                bo.setReceiptStatus(FinArReceiptOrderStatus.DRAFT);
            }
            // 3. 设置收款日期为当前日期
            if (bo.getPaymentDate() == null) {
                bo.setPaymentDate(LocalDate.now());
            }
            // 4. 初始化核销金额
            bo.setAppliedAmount(BigDecimal.ZERO);
            bo.setUnappliedAmount(bo.getPaymentAmount());

            // 5. 填充冗余字段
            fillRedundantFields(bo);

            // 6. 转换为实体并进行保存前校验
            FinArReceiptOrder add = MapstructUtils.convert(bo, FinArReceiptOrder.class);
            validEntityBeforeSave(add);

            // 7. 插入主表记录
            boolean result = baseMapper.insert(add) > 0;
            if (!result) {
                throw new ServiceException("新增收款单失败");
            }

            log.info("新增收款单成功，收款单ID: {}, 收款编码: {}", add.getReceiptId(), add.getReceiptCode());
            return MapstructUtils.convert(add, FinArReceiptOrderVo.class);
        } catch (Exception e) {
            log.error("新增收款单失败: {}", e.getMessage(), e);
            throw new ServiceException("新增收款单失败: " + e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinArReceiptOrderVo updateByBo(FinArReceiptOrderBo bo) {


        try {
            // 1. 校验单据是否存在且状态是否允许修改
            FinArReceiptOrder existingOrder = baseMapper.selectById(bo.getReceiptId());
            if (existingOrder == null) {
                throw new ServiceException("修改失败：ID为 [" + bo.getReceiptId() + "] 的收款单不存在");
            }
            if (existingOrder.getReceiptStatus() != FinArReceiptOrderStatus.DRAFT) {
                throw new ServiceException("修改失败：只有草稿状态的收款单才能修改");
            }

            // 2. 填充可能变更的冗余字段
            fillRedundantFields(bo);

            // 3. 转换为实体并进行保存前校验
            FinArReceiptOrder update = MapstructUtils.convert(bo, FinArReceiptOrder.class);
            validEntityBeforeSave(update);

            // 4. 对于草稿状态的修改，未核销金额应等于总金额
            update.setAppliedAmount(BigDecimal.ZERO);
            update.setUnappliedAmount(update.getPaymentAmount());

            // 5. 更新主表
            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                throw new ServiceException("修改收款单失败");
            }

            log.info("修改收款单成功，收款单ID: {}, 收款编码: {}", update.getReceiptId(), update.getReceiptCode());
            return MapstructUtils.convert(update, FinArReceiptOrderVo.class);
        } catch (Exception e) {
            log.error("修改收款单失败: {}", e.getMessage(), e);
            throw new ServiceException("修改收款单失败: " + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验。
     *
     * @param entity 待保存的收款单实体。
     * @throws ServiceException 如果校验失败。
     */
    private void validEntityBeforeSave(FinArReceiptOrder entity) {
        // 规则1: 校验单据编码唯一性
        if (StringUtils.isNotBlank(entity.getReceiptCode())) {
            LambdaQueryWrapper<FinArReceiptOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceiptOrder::getReceiptCode, entity.getReceiptCode());
            if (entity.getReceiptId() != null) {
                wrapper.ne(FinArReceiptOrder::getReceiptId, entity.getReceiptId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("操作失败：收款单编码 [" + entity.getReceiptCode() + "] 已存在");
            }
        }
        // 规则2: 校验收款金额必须大于0
        if (entity.getPaymentAmount() == null || entity.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("操作失败：收款金额必须大于0");
        }
        // 规则3: 校验关联的客户是否存在
        if (entity.getCustomerId() != null) {
            CompanyVo customer = companyService.queryById(entity.getCustomerId());
            if (customer == null) {
                throw new ServiceException("操作失败：ID为 [" + entity.getCustomerId() + "] 的客户不存在");
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[删除] - 开始. 操作人: {}, 请求ID: {}, 是否校验: {}", currentUser, ids, isValid);

        try {
            if (isValid) {
                List<FinArReceiptOrder> orders = baseMapper.selectByIds(ids);
                for (FinArReceiptOrder order : orders) {
                    // 校验规则1: 只有草稿状态的单据才能删除
                    if (FinArReceiptOrderStatus.DRAFT != order.getReceiptStatus()) {
                        throw new ServiceException("删除失败：收款单 [" + order.getReceiptCode() + "] 状态为“" + order.getReceiptStatus().getDesc() + "”，仅草稿状态可删除");
                    }
                    // 校验规则2: 检查是否已有关联的核销记录
                    if (linkMapper.existsByReceiptId(order.getReceiptId())) {
                        throw new ServiceException("删除失败：收款单 [" + order.getReceiptCode() + "] 已存在核销记录，不允许删除");
                    }
                }
            } else {
                log.warn("【收款单】[删除] - 警告. 跳过业务校验直接删除数据。操作人: {}", currentUser);
            }

            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("【收款单】[批量删除] - 成功. 操作人: {}, 删除ID列表: {}", currentUser, ids);
            } else {
                log.warn("【收款单】[批量删除] - 警告. 数据库操作未影响任何行。操作人: {}, 尝试删除的ID: {}", currentUser, ids);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【收款单】[删除] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[删除] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("删除收款单时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * 填充单据中的冗余字段。
     *
     * @param bo 待填充的业务对象。
     */
    private void fillRedundantFields(FinArReceiptOrderBo bo) {
        // 填充客户名称
        if (bo.getCustomerId() != null && StringUtils.isEmpty(bo.getCustomerName())) {
            CompanyVo customer = companyService.queryById(bo.getCustomerId());
            if (customer != null) {
                bo.setCustomerName(customer.getCompanyName());
            }
        }
        // TODO: 实体添加handlerId, handlerName, handleTime字段后取消注释
        // 填充经办人信息
        // LoginUser loginUser = LoginHelper.getLoginUser();
        // if (loginUser != null) {
        //     bo.setHandlerId(loginUser.getUserId());
        //     bo.setHandlerName(loginUser.getNickname());
        //     bo.setHandleTime(LocalDateTime.now());
        // }
    }

    /**
     * 校验状态流转是否合法。
     * <p>
     * 这是实现状态机（State Machine）模式的核心逻辑。
     * 它定义了收款单在不同状态之间所有合法的转换路径。
     *
     * @param fromStatus 当前状态。
     * @param toStatus   目标状态。
     * @return {@code true} 表示流转合法，{@code false} 表示非法。
     * @see docs/design/README_STATE.md#75-收款单-erpfinarreceiptorder
     */
    private boolean isValidStatusTransition(FinArReceiptOrderStatus fromStatus, FinArReceiptOrderStatus toStatus) {
        if (fromStatus == null || toStatus == null || fromStatus == toStatus) {
            return true; // 状态未变，总是合法
        }
        // 定义合法的状态流转有向图
        return switch (fromStatus) {
            case DRAFT -> toStatus == FinArReceiptOrderStatus.UNAPPLIED || toStatus == FinArReceiptOrderStatus.CANCELLED;
            case UNAPPLIED -> toStatus == FinArReceiptOrderStatus.PARTIALLY_APPLIED ||
                toStatus == FinArReceiptOrderStatus.FULLY_APPLIED ||
                toStatus == FinArReceiptOrderStatus.CANCELLED;
            case PARTIALLY_APPLIED -> toStatus == FinArReceiptOrderStatus.FULLY_APPLIED ||
                toStatus == FinArReceiptOrderStatus.CANCELLED;
            case FULLY_APPLIED -> toStatus == FinArReceiptOrderStatus.CLOSED;
            case CANCELLED, CLOSED -> false; // 终态，不能再流转
            default -> false;
        };
    }

    // =================================================================================================================
    // 以下是待实现的业务方法，将根据计划逐步完成
    // =================================================================================================================

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[确认] - 开始. 操作人: {}, ID: {}", currentUser, orderId);

        try {
            FinArReceiptOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("确认失败：ID为 [" + orderId + "] 的收款单不存在");
            }
            if (order.getReceiptStatus() != FinArReceiptOrderStatus.DRAFT) {
                throw new ServiceException("确认失败：收款单 [" + order.getReceiptCode() + "] 状态为“" + order.getReceiptStatus().getDesc() + "”，仅草稿状态可确认");
            }

            FinArReceiptOrder update = new FinArReceiptOrder();
            update.setReceiptId(orderId);
            update.setReceiptStatus(FinArReceiptOrderStatus.UNAPPLIED); // 草稿 -> 待核销
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【收款单】[确认] - 成功. 操作人: {}, 单号: {}", currentUser, order.getReceiptCode());
            } else {
                log.warn("【收款单】[确认] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, orderId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【收款单】[确认] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[确认] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, e.getMessage(), e);
            throw new ServiceException("确认收款单时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelOrder(Long orderId, String reason) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[取消] - 开始. 操作人: {}, ID: {}, 原因: {}", currentUser, orderId, reason);

        try {
            FinArReceiptOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("取消失败：ID为 [" + orderId + "] 的收款单不存在");
            }

            // 校验规则1: 检查状态是否允许取消
            if (!isValidStatusTransition(order.getReceiptStatus(), FinArReceiptOrderStatus.CANCELLED)) {
                throw new ServiceException("取消失败：收款单 [" + order.getReceiptCode() + "] 状态为“" + order.getReceiptStatus().getDesc() + "”，无法取消");
            }

            // 校验规则2: 检查是否已有关联的核销记录
            if (order.getAppliedAmount() != null && order.getAppliedAmount().compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("取消失败：收款单 [" + order.getReceiptCode() + "] 已存在核销记录，无法取消，请先进行反核销操作");
            }

            FinArReceiptOrder update = new FinArReceiptOrder();
            update.setReceiptId(orderId);
            update.setReceiptStatus(FinArReceiptOrderStatus.CANCELLED);
            if (StringUtils.isNotBlank(reason)) {
                String newRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() + " | " : "";
                newRemark += "取消原因: " + reason;
                update.setRemark(newRemark);
            }

            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("【收款单】[取消] - 成功. 操作人: {}, 单号: {}", currentUser, order.getReceiptCode());
            } else {
                log.warn("【收款单】[取消] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, orderId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【收款单】[取消] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[取消] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, e.getMessage(), e);
            throw new ServiceException("取消收款单时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean closeOrder(Long orderId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[关闭] - 开始. 操作人: {}, ID: {}", currentUser, orderId);

        try {
            FinArReceiptOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("关闭失败：ID为 [" + orderId + "] 的收款单不存在");
            }

            // 校验规则: 只有完全核销的单据才能关闭
            if (order.getReceiptStatus() != FinArReceiptOrderStatus.FULLY_APPLIED) {
                throw new ServiceException("关闭失败：收款单 [" + order.getReceiptCode() + "] 状态为“" + order.getReceiptStatus().getDesc() + "”，仅完全核销状态可关闭");
            }

            FinArReceiptOrder update = new FinArReceiptOrder();
            update.setReceiptId(orderId);
            update.setReceiptStatus(FinArReceiptOrderStatus.CLOSED);
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【收款单】[关闭] - 成功. 操作人: {}, 单号: {}", currentUser, order.getReceiptCode());
            } else {
                log.warn("【收款单】[关闭] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, orderId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【收款单】[关闭] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[关闭] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, orderId, e.getMessage(), e);
            throw new ServiceException("关闭收款单时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean applyToReceivable(Long receiptOrderId, Long receivableId, BigDecimal writeoffAmount) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[核销] - 开始. 操作人: {}, 收款单ID: {}, 应收单ID: {}, 核销金额: {}", currentUser, receiptOrderId, receivableId, writeoffAmount);

        try {
            // 1. 校验核销金额
            if (writeoffAmount == null || writeoffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销失败：核销金额必须大于0");
            }

            // 2. 锁定并校验收款单
            FinArReceiptOrder receiptOrder = baseMapper.selectById(receiptOrderId);
            if (receiptOrder == null) {
                throw new ServiceException("核销失败：ID为 [" + receiptOrderId + "] 的收款单不存在");
            }
            if (receiptOrder.getUnappliedAmount().compareTo(writeoffAmount) < 0) {
                throw new ServiceException("核销失败：核销金额 [" + writeoffAmount + "] 不能大于收款单未核销金额 [" + receiptOrder.getUnappliedAmount() + "]");
            }
            // 状态必须是 UNAPPLIED 或 PARTIALLY_APPLIED
            if (receiptOrder.getReceiptStatus() != FinArReceiptOrderStatus.UNAPPLIED && receiptOrder.getReceiptStatus() != FinArReceiptOrderStatus.PARTIALLY_APPLIED) {
                throw new ServiceException("核销失败：收款单 [" + receiptOrder.getReceiptCode() + "] 当前状态 “" + receiptOrder.getReceiptStatus().getDesc() + "” 不允许核销");
            }

            // 3. 锁定并校验应收单
            FinArReceivableVo receivable = finArReceivableService.queryById(receivableId);
            if (receivable == null) {
                throw new ServiceException("核销失败：ID为 [" + receivableId + "] 的应收单不存在");
            }
            if (receivable.getOutstandingAmount().compareTo(writeoffAmount) < 0) {
                throw new ServiceException("核销失败：核销金额 [" + writeoffAmount + "] 不能大于应收单未付金额 [" + receivable.getOutstandingAmount() + "]");
            }

            // 4. 创建核销关联记录
            FinArReceiptReceivableLink linkBo = new FinArReceiptReceivableLink();
            linkBo.setReceiptId(receiptOrderId);
            linkBo.setReceivableId(receivableId);
            linkBo.setAppliedAmount(writeoffAmount);
            linkBo.setCancellationDate(LocalDate.now());
            linkMapper.insert(linkBo);
            log.info("【收款单】[核销] - 创建核销关联记录成功. 收款单ID: {}, 应收单ID: {}", receiptOrderId, receivableId);

            // 5. 更新收款单金额与状态
            updateReceiptStatusByAmount(receiptOrderId);

            // 6. 更新应收单金额与状态
            finArReceivableService.updateAppliedAmountAndStatus(receivableId);

            log.info("【收款单】[核销] - 成功. 操作人: {}, 收款单: {}, 应收单: {}", currentUser, receiptOrder.getReceiptCode(), receivable.getReceivableCode());
            return true;
        } catch (ServiceException se) {
            log.warn("【收款单】[核销] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[核销] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("核销操作时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean reverseApply(Long linkId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[反核销] - 开始. 操作人: {}, 核销记录ID: {}", currentUser, linkId);

        try {
            // 1. 获取并校验核销记录
            FinArReceiptReceivableLink link = linkMapper.selectById(linkId);
            if (link == null) {
                throw new ServiceException("反核销失败：ID为 [" + linkId + "] 的核销记录不存在");
            }

            // 2. 删除核销记录
            boolean deleted = linkMapper.deleteByIds(List.of(linkId)) > 0;
            if (!deleted) {
                throw new ServiceException("反核销失败：删除核销记录时发生错误");
            }
            log.info("【收款单】[反核销] - 删除核销记录成功. ID: {}", linkId);

            // 3. 重新计算并更新收款单的金额与状态
            updateReceiptStatusByAmount(link.getReceiptId());

            // 4. 重新计算并更新应收单的金额与状态
            finArReceivableService.updateAppliedAmountAndStatus(link.getReceivableId());

            log.info("【收款单】[反核销] - 成功. 操作人: {}, 原收款单ID: {}, 原应收单ID: {}", currentUser, link.getReceiptId(), link.getReceivableId());
            return true;
        } catch (ServiceException se) {
            log.warn("【收款单】[反核销] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【收款单】[反核销] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("反核销操作时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createAccountLedger(Long orderId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【收款单】[创建总账流水] - 开始. 操作人: {}, 收款单ID: {}", currentUser, orderId);

        try {
            FinArReceiptOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("操作失败：收款单不存在");
            }
            // 只有完全核销的单据才能生成总账流水
            if (order.getReceiptStatus() != FinArReceiptOrderStatus.FULLY_APPLIED) {
                throw new ServiceException("操作失败：只有完全核销的收款单才能生成总账流水");
            }

            // TODO: 调用 IFinAccountLedgerService 服务创建流水
            // finAccountLedgerService.createFromReceiptOrder(order);
//            if (!result) {
//                throw new ServiceException("创建失败");
//            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 根据核销金额，更新收款单的已核销/未核销金额和状态。
     *
     * @param receiptId 要更新的收款单ID。
     * @return 更新结果。
     */
    private Boolean updateReceiptStatusByAmount(Long receiptId) {
        FinArReceiptOrder order = baseMapper.selectById(receiptId);
        if (order == null) {
            log.error("【收款单】[状态更新] - 失败. 未找到ID为 {} 的收款单", receiptId);
            throw new ServiceException("更新收款单状态失败：单据不存在");
        }

        BigDecimal appliedAmount = linkMapper.getAppliedAmountByReceiptId(receiptId);
        BigDecimal totalAmount = order.getPaymentAmount();
        FinArReceiptOrderStatus newStatus;

        // 使用 compareTo 进行精确比较
        if (appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            newStatus = FinArReceiptOrderStatus.UNAPPLIED;
        } else if (appliedAmount.compareTo(totalAmount) >= 0) {
            newStatus = FinArReceiptOrderStatus.FULLY_APPLIED;
        } else {
            newStatus = FinArReceiptOrderStatus.PARTIALLY_APPLIED;
        }

        // 仅当状态或金额发生变化时才更新
        if (order.getReceiptStatus() != newStatus || order.getAppliedAmount().compareTo(appliedAmount) != 0) {
            FinArReceiptOrder update = new FinArReceiptOrder();
            update.setReceiptId(receiptId);
            update.setAppliedAmount(appliedAmount);
            update.setUnappliedAmount(totalAmount.subtract(appliedAmount));
            update.setReceiptStatus(newStatus);
            if (baseMapper.updateById(update) > 0) {
                log.info("【收款单】[状态更新] - 成功. ID: {}, 状态由 [{}] 更新为 [{}], 已核销金额: {}",
                    receiptId, order.getReceiptStatus().getDesc(), newStatus.getDesc(), appliedAmount);
                // 如果变为完全核销，并且之前不是完全核销状态，则触发创建总账流水
                if (newStatus == FinArReceiptOrderStatus.FULLY_APPLIED && order.getReceiptStatus() != FinArReceiptOrderStatus.FULLY_APPLIED) {
                    log.info("【收款单】[状态更新] - 单据已完全核销，准备触发总账流水生成。ID: {}", receiptId);
                    // [DDD-TODO] 此处应替换为发布领域事件
                    createAccountLedger(receiptId);
                }
            } else {
                log.error("【收款单】[状态更新] - 失败. 数据库更新未生效. ID: {}", receiptId);
                throw new ServiceException("更新收款单状态时数据库操作失败");
            }
        }
        return true;
    }
}
