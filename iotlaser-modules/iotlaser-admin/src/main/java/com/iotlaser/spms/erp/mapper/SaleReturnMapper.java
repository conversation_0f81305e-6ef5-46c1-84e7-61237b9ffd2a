package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售退货Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
public interface SaleReturnMapper extends BaseMapperPlus<SaleReturn, SaleReturnVo> {
    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<SaleReturn>().eq(SaleReturn::getDirectSourceId, directSourceId));
    }

    default List<SaleReturn> queryByDirectSourceId(Long directSourceId) {
        return selectList(new LambdaQueryWrapper<SaleReturn>().eq(SaleReturn::getDirectSourceId, directSourceId));
    }
}
