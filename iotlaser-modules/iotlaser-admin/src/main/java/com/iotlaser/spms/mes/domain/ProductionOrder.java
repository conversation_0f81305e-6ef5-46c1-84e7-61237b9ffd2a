package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产订单对象 mes_production_order
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_order")
public class ProductionOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 生产订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 生产订单编码
     */
    private String orderCode;

    /**
     * 生产订单类型
     */
    private String orderType;

    /**
     * 源头单据ID (整个业务流程的最初发起单据)
     */
    private Long sourceId;

    /**
     * 源头单据编码
     */
    private String sourceCode;

    /**
     * 源头单据类型
     */
    private SourceType sourceType;

    /**
     * 上游单据ID (当前单据的直接创建来源单据)
     */
    private Long directSourceId;

    /**
     * 上游单据编码
     */
    private String directSourceCode;

    /**
     * 上游单据类型
     */
    private DirectSourceType directSourceType;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * BOMID
     */
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 计划生产数量
     */
    private BigDecimal quantity;

    /**
     * 已完工入库数量
     */
    private BigDecimal finishQuantity;

    /**
     * 计划开始
     */
    private LocalDate plannedStartDate;

    /**
     * 计划结束
     */
    private LocalDate plannedEndDate;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 订单状态
     */
    private ProductionOrderStatus orderStatus;

    /**
     * 计划员ID
     */
    private Long plannerId;

    /**
     * 计划员
     */
    private String plannerName;

    /**
     * 车间主管 ID
     */
    private Long supervisorId;

    /**
     * 车间主管
     */
    private String supervisorName;

    /**
     * 工单下达时间
     */
    private LocalDateTime releaseTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
