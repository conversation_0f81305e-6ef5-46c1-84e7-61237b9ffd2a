package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinArReceivableItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 应收单明细视图对象 erp_fin_ar_receivable_item
 *
 * <AUTHOR> Kai
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinArReceivableItem.class)
public class FinArReceivableItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 应收ID
     */
    @ExcelProperty(value = "应收ID")
    private Long receivableId;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编号
     */
    @ExcelProperty(value = "源头编号")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编号
     */
    @ExcelProperty(value = "上游编号")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @ExcelProperty(value = "上游明细ID")
    private Long directSourceItemId;

    /**
     * 上游批次ID
     */
    @ExcelProperty(value = "上游批次ID")
    private Long directSourceBatchId;

    /**
     * 内部批次号
     */
    @ExcelProperty(value = "内部批次号")
    private String internalBatchNumber;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 单价(含税)
     */
    @ExcelProperty(value = "单价(含税)")
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    @ExcelProperty(value = "单价(不含税)")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
