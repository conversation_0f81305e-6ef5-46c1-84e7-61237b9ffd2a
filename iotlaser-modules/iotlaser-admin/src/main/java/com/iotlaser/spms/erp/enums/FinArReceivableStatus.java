package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应收单（发票）状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinArReceivableStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "应收单已录入，但未提交"),
    UNPAID("unpaid", "未收款", "应收单已确认，等待收款"),
    PARTIALLY_PAID("partially_paid", "部分收款", "已收到部分款项"),
    FULLY_PAID("fully_paid", "完全收款", "应收单已全部收回"),
    CANCELLED("cancelled", "已取消", "应收单被取消"),
    CLOSED("closed", "已关闭", "应收单完工且财务核销等结束，应收单归档");

    public final static String DICT_CODE = "erp_fin_ar_receivable_status";
    public final static String DICT_NAME = "应收单状态";
    public final static String DICT_DESC = "管理应收单的收款流程状态，从生成、确认到收款完成的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 应收单状态枚举
     */
    public static FinArReceivableStatus getByValue(String value) {
        for (FinArReceivableStatus receivableStatus : values()) {
            if (receivableStatus.getValue().equals(value)) {
                return receivableStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

}
