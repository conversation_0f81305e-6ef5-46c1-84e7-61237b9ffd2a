package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收款单状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinArReceiptOrderStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "收款单已创建，但未提交"),
    UNAPPLIED("unapplied", "未收款", "收款单已确认，可以进行核销"),
    ADVANCE_PAYMENT("advance_payment", "预收款单", "收款单作为预收款单处理"),
    PARTIALLY_APPLIED("partially_applied", "部分核销", "收款单金额的一部分已用于核销应收"),
    FULLY_APPLIED("fully_applied", "全部核销", "收款单金额已全部分配给一张或多张应收"),
    CANCELLED("cancelled", "已取消", "收款单在执行前被取消"),
    CLOSED("closed", "已关闭", "收款单完工且财务核销等结束，收款单归档");

    public final static String DICT_CODE = "erp_fin_ar_receipt_order_status";
    public final static String DICT_NAME = "收款单状态";
    public final static String DICT_DESC = "管理收款单的处理流程状态，从申请、审批到收款单确认的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 收款单状态枚举
     */
    public static FinArReceiptOrderStatus getByValue(String value) {
        for (FinArReceiptOrderStatus receiptStatus : values()) {
            if (receiptStatus.getValue().equals(value)) {
                return receiptStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

}
