package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 采购订单明细业务对象 erp_purchase_order_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PurchaseOrderItem.class, reverseConvertGenerate = false)
public class PurchaseOrderItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 订单数量
     */
    @NotNull(message = "订单数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已入库数量
     */
    @NotNull(message = "已入库数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal finishQuantity;

    /**
     * 已退货数量
     */
    private BigDecimal returnedQuantity;

    /**
     * 已收票数量
     */
    private BigDecimal invoicedQuantity;

    /**
     * 单价(含税)
     */
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 排除的产品ID
     */
    private String excludeProductIds;


}
