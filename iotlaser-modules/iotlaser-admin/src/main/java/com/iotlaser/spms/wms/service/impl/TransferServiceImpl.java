package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.wms.domain.*;
import com.iotlaser.spms.wms.domain.bo.TransferBo;
import com.iotlaser.spms.wms.domain.bo.TransferItemBo;
import com.iotlaser.spms.wms.domain.vo.TransferItemVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.enums.TransferStatus;
import com.iotlaser.spms.wms.mapper.TransferItemBatchMapper;
import com.iotlaser.spms.wms.mapper.TransferItemMapper;
import com.iotlaser.spms.wms.mapper.TransferMapper;
import com.iotlaser.spms.wms.service.IOutboundService;
import com.iotlaser.spms.wms.service.ITransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_TRANSFER_CODE;

/**
 * 产品移库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TransferServiceImpl implements ITransferService {

    private final TransferMapper baseMapper;
    private final TransferItemMapper itemMapper;
    private final TransferItemBatchMapper batchMapper;

    // TODO: [DDD重构-跨聚合调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，聚合根Service不应该直接依赖其他聚合根Service
    // 移库操作涉及出库和入库，应该通过应用服务层协调，或使用领域事件
    private final IOutboundService outboundService;    // TEMP: 跨聚合调用，需要重构
    private final Gen gen;

    /**
     * 查询产品移库
     *
     * @param transferId 主键
     * @return 产品移库
     */
    @Override
    public TransferVo queryById(Long transferId) {
        return baseMapper.selectVoById(transferId);
    }

    /**
     * 分页查询产品移库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库分页列表
     */
    @Override
    public TableDataInfo<TransferVo> queryPageList(TransferBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Transfer> lqw = buildQueryWrapper(bo);
        Page<TransferVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品移库列表
     *
     * @param bo 查询条件
     * @return 产品移库列表
     */
    @Override
    public List<TransferVo> queryList(TransferBo bo) {
        LambdaQueryWrapper<Transfer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Transfer> buildQueryWrapper(TransferBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Transfer> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Transfer::getTransferId);
        lqw.eq(StringUtils.isNotBlank(bo.getTransferCode()), Transfer::getTransferCode, bo.getTransferCode());
        lqw.like(StringUtils.isNotBlank(bo.getTransferName()), Transfer::getTransferName, bo.getTransferName());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferType()), Transfer::getTransferType, bo.getTransferType());
        lqw.eq(bo.getTransferStatus() != null, Transfer::getTransferStatus, bo.getTransferStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Transfer::getStatus, bo.getStatus());
        lqw.between(params.get("beginTransferTime") != null && params.get("endTransferTime") != null,
            Transfer::getTransferTime, params.get("beginTransferTime"), params.get("endTransferTime"));
        return lqw;
    }

    /**
     * 新增仓库移库单
     *
     * @param bo 包含新仓库移库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferVo insertByBo(TransferBo bo) {
        try {
            // 如果移库单号为空，则生成新的移库单号
            if (StringUtils.isEmpty(bo.getTransferCode())) {
                bo.setTransferCode(gen.code(WMS_TRANSFER_CODE));
            }
            // 设置移库状态为草稿
            if (bo.getTransferStatus() == null) {
                bo.setTransferStatus(TransferStatus.DRAFT);
            }
            if (bo.getTransferTime() == null) {
                bo.setTransferTime(LocalDateTime.now());
            }
            // 将BO对象转换为Transfer实体对象
            Transfer add = MapstructUtils.convert(bo, Transfer.class);
            // 保存前验证实体
            validEntityBeforeSave(add);

            // 插入Transfer实体，判断插入是否成功
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品移库失败");
            }

            bo.setTransferId(add.getTransferId());
            log.info("新增产品移库成功：{}", add.getTransferCode());
            return MapstructUtils.convert(add, TransferVo.class);
        } catch (Exception e) {
            log.error("新增产品移库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品移库失败：" + e.getMessage());
        }
    }

    /**
     * 修改仓库移库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferVo updateByBo(TransferBo bo) {
        try {
            // 将传入的TransferBo转换为Transfer对象
            Transfer update = MapstructUtils.convert(bo, Transfer.class);
            // 在保存前验证实体的合法性
            validEntityBeforeSave(update);
            // 更新实体，判断更新是否成功
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品移库失败：移库单不存在或数据未变更");
            }
            log.info("修改产品移库成功：{}", update.getTransferCode());
            return MapstructUtils.convert(update, TransferVo.class);
        } catch (Exception e) {
            log.error("修改产品移库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品移库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验：
     * 移库单编码唯一性检查
     * 批次产品的批次明细完整性校验
     *
     * @param entity 移库单实体
     */
    private void validEntityBeforeSave(Transfer entity) {
        // 校验移库单编码唯一性
        if (StringUtils.isNotBlank(entity.getTransferCode())) {
            LambdaQueryWrapper<Transfer> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Transfer::getTransferCode, entity.getTransferCode());
            if (entity.getTransferId() != null) {
                wrapper.ne(Transfer::getTransferId, entity.getTransferId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("移库单编码已存在：" + entity.getTransferCode());
            }
        }
    }

    /**
     * 校验并批量删除仓库移库单
     *
     * @param ids     待删除的仓库移库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验移库单是否可以删除
            List<Transfer> transfers = baseMapper.selectByIds(ids);
            for (Transfer transfer : transfers) {
                // 检查移库单状态，只有草稿状态的移库单才能删除
                if (transfer.getTransferStatus() != TransferStatus.DRAFT) {
                    throw new ServiceException("移库单【" + transfer.getTransferName() + "】状态为【" + transfer.getTransferStatus() + "】，不允许删除");
                }
                log.info("删除移库单校验通过：{}", transfer.getTransferName());
            }
        }

        try {
            int batchResult = batchMapper.deleteByTransferIds(ids);
            if (batchResult > 0) {
                log.info("批量删除入库明细批次成功，删除数量：{}", batchResult);
            }
            int itemResult = itemMapper.deleteByTransferIds(ids);
            if (itemResult > 0) {
                log.info("批量删除入库明细成功，删除数量：{}", itemResult);
            }
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除移库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除移库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除移库单失败：" + e.getMessage());
        }
    }

    /**
     * 查询产品移库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品移库明细表
     */
    @Override
    public TransferItemVo queryItemById(Long itemId) {
        return MapstructUtils.convert(itemMapper.queryById(itemId), TransferItemVo.class);
    }

    /**
     * 查询产品移库明细表列表及其关联信息
     *
     * @param transferId 移库ID
     * @return 产品移库明细表列表
     */
    @Override
    public List<TransferItem> queryItemByTransferId(Long transferId) {
        TransferItemBo bo = new TransferItemBo();
        bo.setTransferId(transferId);
        QueryWrapper<TransferItem> queryWrapper = buildQueryWrapperWith(bo);
        return itemMapper.queryPageList(null, queryWrapper);
    }

    /**
     * 查询产品移库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品移库明细
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TransferItemVo> queryItemList(TransferItemBo bo) {
        QueryWrapper<TransferItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(itemMapper.queryPageList(null, queryWrapper), TransferItemVo.class);
    }

    /**
     * 分页查询产品移库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库明细表分页列表
     */
    @Override
    public TableDataInfo<TransferItemVo> queryItemPageList(TransferItemBo bo, PageQuery pageQuery) {
        QueryWrapper<TransferItem> queryWrapper = buildQueryWrapperWith(bo);
        List<TransferItemVo> result = MapstructUtils.convert(itemMapper.queryPageList(pageQuery.build(), queryWrapper), TransferItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<TransferItem> buildQueryWrapperWith(TransferItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<TransferItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getTransferId() != null, "item.transfer_id", bo.getTransferId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getFromLocationId() != null, "item.from_location_id", bo.getFromLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getFromLocationCode()), "item.from_location_code", bo.getFromLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getFromLocationName()), "item.from_location_name", bo.getFromLocationName());
        wrapper.eq(bo.getToLocationId() != null, "item.to_location_id", bo.getToLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getToLocationCode()), "item.to_location_code", bo.getToLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getToLocationName()), "item.to_location_name", bo.getToLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 确认仓库移库单
     *
     * @param transferId 待确认的仓库移库单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmTransfer(Long transferId) {
        Transfer transfer = baseMapper.selectById(transferId);
        if (transfer == null) {
            throw new ServiceException("移库单不存在");
        }
        // 校验出库单状态
        if (TransferStatus.DRAFT != transfer.getTransferStatus()) {
            throw new ServiceException("只有草稿状态的出库单才能确认");
        }
        List<TransferItem> items = queryItemByTransferId(transferId);
        if (items.isEmpty()) {
            throw new ServiceException("移库明细不能为空");
        }
        // 更新出库单状态
        Transfer update = new Transfer();
        update.setTransferId(transferId);
        update.setTransferStatus(TransferStatus.PENDING);
        boolean result = baseMapper.updateById(update) > 0;

        // ✅ [提交出库单后，触发WMS任务] - 已完善
        // 确认成功后，调用WMS模块创建出库执行单
        if (result) {
            try {
                transfer.setItems(items);
                // 调用WMS服务创建出库执行单
                Boolean wmsResult = outboundService.createFromTransfer(transfer);
                if (wmsResult) {
                    log.info("移库单【{}】成功创建仓库出库执行单", transfer.getTransferCode());
                } else {
                    log.error("移库单【{}】创建仓库出库执行单失败", transfer.getTransferCode());
                    // 不抛出异常，允许ERP出库单确认成功，WMS任务可以后续重试
                }
            } catch (Exception e) {
                log.error("移库单【{}】创建仓库出库执行单异常: {}", transfer.getTransferCode(), e.getMessage(), e);
                // 不抛出异常，避免影响ERP出库单确认流程
            }
        }

        return result;
    }

    /**
     * 仓库出库完成后状态回传
     * 由WMS系统调用，通知移库出库单已完成
     *
     * @param outbound 已完成的仓库出库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByWms(Outbound outbound) {
        try {
            log.info("开始根据WMS状态更新移库出库单 - 出库单ID: {}, WMS编码: {}", outbound.getDirectSourceId(), outbound.getOutboundCode());
            // 查询出库单和明细
            Transfer transfer = baseMapper.selectById(outbound.getDirectSourceId());
            if (transfer == null) {
                throw new ServiceException("移库出库单不存在，ID: " + outbound.getDirectSourceId());
            }
            // 校验当前状态是否允许WMS回传
            if (transfer.getTransferStatus() != TransferStatus.PENDING) {
                log.warn("移库出库单【{}】当前状态【{}】不允许WMS状态回传", transfer.getTransferCode(), transfer.getTransferStatus());
                return false;
            }
            // ✅ 更新明细的实际出库数量
            updateActualQuantities(outbound);
            // ✅ 更新出库单状态为已完成
            Transfer update = new Transfer();
            update.setTransferId(transfer.getTransferId());
            update.setTransferStatus(TransferStatus.IN_PROGRESS);
            // 添加WMS回传信息到备注
            String wmsRemark = String.format(" [仓库出库完成 - WMS编码: %s, 回传时间: %s]", outbound.getOutboundCode(), LocalDateTime.now());
            String newRemark = StringUtils.isNotEmpty(transfer.getRemark()) ? transfer.getRemark() + wmsRemark : wmsRemark;
            update.setRemark(newRemark);
            int result = baseMapper.updateById(update);
            return result > 0;
        } catch (Exception e) {
            log.error("根据WMS状态更新移库出库单失败 - 出库单ID: {}, WMS编码: {}, 错误: {}", outbound.getDirectSourceId(), outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("根据WMS状态更新移库出库单失败: " + e.getMessage());
        }
    }

    /**
     * 仓库出库异常回传
     * 由WMS系统调用，通知ERP移库出库单发生异常
     *
     * @param outboundId      移库出库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleWmsOutboundException(Long outboundId, String exceptionReason) {
        try {
            log.info("开始处理移库出库单WMS异常 - 出库单ID: {}, 异常原因: {}", outboundId, exceptionReason);
            // 查询出库单
            Transfer transfer = baseMapper.selectById(outboundId);
            if (transfer == null) {
                throw new ServiceException("移库出库单不存在，ID: " + outboundId);
            }
            // 更新状态为异常
            Transfer update = new Transfer();
            update.setTransferId(outboundId);
            // TODO: 需要在 TransferStatus 枚举中添加 EXCEPTION 状态
            // update.setTransferStatus(TransferStatus.EXCEPTION);
            update.setRemark(StringUtils.isNotBlank(transfer.getRemark()) ? transfer.getRemark() + "; WMS异常: " + exceptionReason : "WMS异常: " + exceptionReason);
            int result = baseMapper.updateById(update);
            log.info("移库出库单WMS异常处理完成 - 出库单ID: {}, 更新结果: {}", outboundId, result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("处理移库出库单WMS异常失败 - 出库单ID: {}, 异常原因: {}, 错误: {}",
                outboundId, exceptionReason, e.getMessage(), e);
            throw new ServiceException("处理移库出库单WMS异常失败: " + e.getMessage());
        }
    }

    /**
     * 仓库入库完成后状态回传
     * 由WMS系统调用，通知WMS移库入库单已完成
     *
     * @param inbound 入库单
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByWms(Inbound inbound) {
        try {
            log.info("开始根据WMS状态更新移库入库单 - 出库单ID: {}, WMS编码: {}", inbound.getDirectSourceId(), inbound.getInboundCode());
            // 查询出库单和明细
            Transfer transfer = baseMapper.selectById(inbound.getDirectSourceId());
            if (transfer == null) {
                throw new ServiceException("移库入库单不存在，ID: " + inbound.getDirectSourceId());
            }
            // 校验当前状态是否允许WMS回传
            if (transfer.getTransferStatus() != TransferStatus.IN_PROGRESS) {
                log.warn("移库入库单【{}】当前状态【{}】不允许WMS状态回传", transfer.getTransferCode(), transfer.getTransferStatus());
                return false;
            }
            // ✅ 更新明细的实际出库数量
            updateActualQuantities(inbound);
            // ✅ 更新出库单状态为已完成
            Transfer update = new Transfer();
            update.setTransferId(transfer.getTransferId());
            update.setTransferStatus(TransferStatus.COMPLETED);
            // 添加WMS回传信息到备注
            String wmsRemark = String.format(" [仓库出库完成 - WMS编码: %s, 回传时间: %s]", inbound.getInboundCode(), LocalDateTime.now());
            String newRemark = StringUtils.isNotEmpty(transfer.getRemark()) ? transfer.getRemark() + wmsRemark : wmsRemark;
            update.setRemark(newRemark);
            int result = baseMapper.updateById(update);
            return result > 0;
        } catch (Exception e) {
            log.error("根据WMS状态更新移库入库单失败 - 出库单ID: {}, WMS编码: {}, 错误: {}", inbound.getDirectSourceId(), inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("根据WMS状态更新移库入库单失败: " + e.getMessage());
        }
    }

    /**
     * 仓库出库异常回传
     * 由WMS系统调用，通知ERP移库入库单发生异常
     *
     * @param inboundId       入库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleWmsInboundException(Long inboundId, String exceptionReason) {
        try {
            log.info("开始处理移库入库单WMS异常 - 出库单ID: {}, 异常原因: {}", inboundId, exceptionReason);
            // 查询出库单
            Transfer transfer = baseMapper.selectById(inboundId);
            if (transfer == null) {
                throw new ServiceException("移库入库单不存在，ID: " + inboundId);
            }
            // 更新状态为异常
            Transfer update = new Transfer();
            update.setTransferId(inboundId);
            // TODO: 需要在 TransferStatus 枚举中添加 EXCEPTION 状态
            // update.setTransferStatus(TransferStatus.EXCEPTION);
            update.setRemark(StringUtils.isNotBlank(transfer.getRemark()) ? transfer.getRemark() + "; WMS异常: " + exceptionReason : "WMS异常: " + exceptionReason);
            int result = baseMapper.updateById(update);
            log.info("移库入库单WMS异常处理完成 - 出库单ID: {}, 更新结果: {}", inboundId, result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("处理移库入库单WMS异常失败 - 出库单ID: {}, 异常原因: {}, 错误: {}", inboundId, exceptionReason, e.getMessage(), e);
            throw new ServiceException("处理移库入库单WMS异常失败: " + e.getMessage());
        }
    }

    /**
     * 更新明细的实际出库数量
     *
     * @param outbound 出库单
     */
    private void updateActualQuantities(Outbound outbound) {
        try {
            if (outbound.getItems() != null && !outbound.getItems().isEmpty()) {
                List<TransferItem> updates = new ArrayList<>();
                for (OutboundItem outboundItem : outbound.getItems()) {
                    TransferItem update = new TransferItem();
                    update.setItemId(outboundItem.getDirectSourceItemId());
                    update.setFinishQuantity(outboundItem.getFinishQuantity());
                    update.setRemark(String.format(" [实际出库 - 数量: %s]", outboundItem.getFinishQuantity()));
                    updates.add(update);
                }
                itemMapper.updateBatchById(updates);
                log.info("更新明细实际出库数量成功 - 明细数量: {}", outbound.getItems().size());
            }
        } catch (Exception e) {
            log.error("更新明细实际出库数量失败 - 出库单ID: {}, 错误: {}", outbound.getDirectSourceId(), e.getMessage(), e);
            throw new ServiceException("更新明细实际出库数量失败：" + e.getMessage());
        }
    }

    /**
     * 更新明细的实际入库数量
     *
     * @param inbound 入库单
     */
    private void updateActualQuantities(Inbound inbound) {
        try {
            if (inbound.getItems() != null && !inbound.getItems().isEmpty()) {
                List<TransferItem> updates = new ArrayList<>();
                for (InboundItem inboundItem : inbound.getItems()) {
                    TransferItem item = itemMapper.selectById(inboundItem.getDirectSourceItemId());
                    TransferItem update = new TransferItem();
                    update.setItemId(inboundItem.getDirectSourceItemId());
                    update.setFinishQuantity(inboundItem.getFinishQuantity());
                    String wmsRemark = String.format(" [实际入库 - 数量: %s]", inboundItem.getFinishQuantity());
                    String newRemark = StringUtils.isNotEmpty(item.getRemark()) ? item.getRemark() + wmsRemark : wmsRemark;
                    update.setRemark(newRemark);
                    updates.add(update);
                }
                itemMapper.updateBatchById(updates);
                log.info("更新明细实际入库数量成功 - 明细数量: {}", inbound.getItems().size());
            }
        } catch (Exception e) {
            log.error("更新明细实际入库数量失败 - 出库单ID: {}, 错误: {}", inbound.getDirectSourceId(), e.getMessage(), e);
            throw new ServiceException("更新明细实际入库数量失败：" + e.getMessage());
        }
    }

    /**
     * 完成仓库移库单
     * TODO: 需完善实现逻辑，验证出库和入库都已完成后更新移库状态
     *
     * @param transferId 待完成的仓库移库单ID
     */
    @Override
    public void finish(Long transferId) {
        /*Transfer update = baseMapper.selectById(transferId);
        OutboundBo outboundBo = new OutboundBo();
        outboundBo.setSourceId(transferId);
        Optional<OutboundVo> outboundVo = outboundService.queryList(outboundBo).stream().findFirst();
        if (outboundVo.isEmpty() || !outboundVo.get().getOutboundStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("请先完成出库单");
        }

        InboundBo bo = new InboundBo();
        bo.setDirectSourceId(transferId);
        Optional<InboundVo> inboundVo = inboundService.queryList(bo).stream().findFirst();
        if (inboundVo.isEmpty() || !inboundVo.get().getInboundStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("请先完成入库单");
        }

        update.setTransferStatus(TransferStatus.COMPLETED);
        baseMapper.updateById(update);*/
    }


}
