package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinStatement;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentOrderBo;
import com.iotlaser.spms.erp.domain.bo.FinStatementBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.domain.vo.FinStatementItemVo;
import com.iotlaser.spms.erp.domain.vo.FinStatementVo;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.FinStatementStatus;
import com.iotlaser.spms.erp.mapper.FinStatementMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import com.iotlaser.spms.erp.service.IFinStatementItemService;
import com.iotlaser.spms.erp.service.IFinStatementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 财务对账服务实现
 *
 * <AUTHOR> Kai
 * @version 1.2
 * @since 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinStatementServiceImpl implements IFinStatementService {

    private final FinStatementMapper baseMapper;
    private final IFinApInvoiceService finApInvoiceService;
    private final IFinApPaymentOrderService finApPaymentOrderService;
    private final IFinStatementItemService finStatementItemService;

    /**
     * 查询对账单
     *
     * @param statementId 主键
     * @return 对账单
     */
    @Override
    public FinStatementVo queryById(Long statementId) {
        return baseMapper.selectVoById(statementId);
    }

    /**
     * 分页查询对账单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 对账单分页列表
     */
    @Override
    public TableDataInfo<FinStatementVo> queryPageList(FinStatementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinStatement> lqw = buildQueryWrapper(bo);
        Page<FinStatementVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的对账单列表
     *
     * @param bo 查询条件
     * @return 对账单列表
     */
    @Override
    public List<FinStatementVo> queryList(FinStatementBo bo) {
        LambdaQueryWrapper<FinStatement> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinStatement> buildQueryWrapper(FinStatementBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinStatement> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinStatement::getStatementId);
        lqw.eq(StringUtils.isNotBlank(bo.getStatementCode()), FinStatement::getStatementCode, bo.getStatementCode());
        lqw.like(StringUtils.isNotBlank(bo.getStatementName()), FinStatement::getStatementName, bo.getStatementName());
        lqw.eq(bo.getPartnerId() != null, FinStatement::getPartnerId, bo.getPartnerId());
        lqw.like(StringUtils.isNotBlank(bo.getPartnerName()), FinStatement::getPartnerName, bo.getPartnerName());
        lqw.eq(bo.getStartDate() != null, FinStatement::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, FinStatement::getEndDate, bo.getEndDate());
        lqw.eq(bo.getOpeningBalance() != null, FinStatement::getOpeningBalance, bo.getOpeningBalance());
        // 修复类型转换：closingBalance是BigDecimal类型，应该检查是否为null而不是空字符串
        lqw.eq(bo.getClosingBalance() != null, FinStatement::getClosingBalance, bo.getClosingBalance());
        lqw.eq(bo.getStatementStatus() != null, FinStatement::getStatementStatus, bo.getStatementStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinStatement::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增财务对账单
     *
     * @param bo 包含新对账单所有信息的业务对象 (BO)
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinStatementBo bo) {
        try {
            FinStatement add = MapstructUtils.convert(bo, FinStatement.class);
            validEntityBeforeSave(add);

            boolean saved = baseMapper.insert(add) > 0;
            if (!saved) {
                throw new ServiceException("新增财务对账单失败");
            }

            bo.setStatementId(add.getStatementId());
            log.info("新增财务对账单成功，对账单ID: {}, 对账编码: {}", add.getStatementId(), add.getStatementCode());
            return true;
        } catch (Exception e) {
            log.error("新增财务对账单失败: {}", e.getMessage(), e);
            throw new ServiceException("新增财务对账单失败: " + e.getMessage());
        }
    }

    /**
     * 修改财务对账单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FinStatementBo bo) {
        try {
            FinStatement update = MapstructUtils.convert(bo, FinStatement.class);
            validEntityBeforeSave(update);

            boolean updated = baseMapper.updateById(update) > 0;
            if (!updated) {
                throw new ServiceException("修改财务对账单失败");
            }

            log.info("修改财务对账单成功，对账单ID: {}, 对账编码: {}", update.getStatementId(), update.getStatementCode());
            return true;
        } catch (Exception e) {
            log.error("修改财务对账单失败: {}", e.getMessage(), e);
            throw new ServiceException("修改财务对账单失败: " + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinStatement entity) {
        // 校验对账单编号唯一性
        if (StringUtils.isNotBlank(entity.getStatementCode())) {
            LambdaQueryWrapper<FinStatement> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinStatement::getStatementCode, entity.getStatementCode());
            if (entity.getStatementId() != null) {
                wrapper.ne(FinStatement::getStatementId, entity.getStatementId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("对账单编号已存在：" + entity.getStatementCode());
            }
        }

        // 校验日期范围
        if (entity.getStartDate() != null && entity.getEndDate() != null) {
            if (entity.getStartDate().isAfter(entity.getEndDate())) {
                throw new ServiceException("开始日期不能晚于结束日期");
            }
        }
    }

    /**
     * 校验并批量删除对账单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 生成客户对账单
     *
     * @param customerId   客户ID
     * @param customerCode 客户编码
     * @param customerName 客户名称
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 对账单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateCustomerStatement(Long customerId, String customerCode, String customerName,
                                          LocalDate startDate, LocalDate endDate,
                                          Long operatorId, String operatorName) {
        try {
            // 创建对账单主记录
            FinStatement statement = new FinStatement();
            statement.setStatementCode(generateStatementCode("AR"));
            statement.setStatementName("客户对账单-" + customerName);
            // TODO: FinStatement实体中没有statementType字段，需要重新设计对账类型记录
            // 暂时在remark中记录对账类型，待实体完善后修正
            // statement.setStatementType("AR"); // 应收对账
            // 修复字段名：FinStatement实体中使用partnerId、partnerCode、partnerName
            statement.setPartnerId(customerId);
            statement.setPartnerName(customerName);
            statement.setStartDate(startDate);
            statement.setEndDate(endDate);
            statement.setStatementStatus(FinStatementStatus.DRAFT);

            // 计算期初余额
            BigDecimal openingBalance = calculateOpeningBalance(customerId, startDate, "AR");
            statement.setOpeningBalance(openingBalance);

            // 插入对账单主记录
            baseMapper.insert(statement);
            Long statementId = statement.getStatementId();

            // 生成对账明细
            generateStatementItems(statementId, customerId, startDate, endDate, "AR");

            // 计算期末余额
            BigDecimal closingBalance = calculateClosingBalance(statementId, openingBalance);
            statement.setClosingBalance(closingBalance);
            baseMapper.updateById(statement);

            log.info("客户对账单生成成功 - 客户: {}, 期间: {} 至 {}, 操作人: {}",
                customerName, startDate, endDate, operatorName);

            return statementId;
        } catch (Exception e) {
            log.error("客户对账单生成失败 - 客户: {}, 错误: {}", customerName, e.getMessage(), e);
            throw new ServiceException("客户对账单生成失败：" + e.getMessage());
        }
    }

    /**
     * 生成供应商对账单
     *
     * @param supplierId   供应商ID
     * @param supplierCode 供应商编码
     * @param supplierName 供应商名称
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 对账单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateSupplierStatement(Long supplierId, String supplierCode, String supplierName,
                                          LocalDate startDate, LocalDate endDate,
                                          Long operatorId, String operatorName) {
        try {
            // 创建对账单主记录
            FinStatement statement = new FinStatement();
            statement.setStatementCode(generateStatementCode("AP"));
            statement.setStatementName("供应商对账单-" + supplierName);
            // TODO: FinStatement实体中没有statementType字段，需要重新设计对账类型记录
            // 暂时在remark中记录对账类型，待实体完善后修正
            // statement.setStatementType("AP"); // 应付对账
            // 修复字段名：FinStatement实体中使用partnerId、partnerCode、partnerName
            statement.setPartnerId(supplierId);
            statement.setPartnerName(supplierName);
            statement.setStartDate(startDate);
            statement.setEndDate(endDate);
            statement.setStatementStatus(FinStatementStatus.DRAFT);

            // 计算期初余额
            BigDecimal openingBalance = calculateOpeningBalance(supplierId, startDate, "AP");
            statement.setOpeningBalance(openingBalance);

            // 插入对账单主记录
            baseMapper.insert(statement);
            Long statementId = statement.getStatementId();

            // 生成对账明细
            generateStatementItems(statementId, supplierId, startDate, endDate, "AP");

            // 计算期末余额
            BigDecimal closingBalance = calculateClosingBalance(statementId, openingBalance);
            statement.setClosingBalance(closingBalance);
            baseMapper.updateById(statement);

            log.info("供应商对账单生成成功 - 供应商: {}, 期间: {} 至 {}, 操作人: {}",
                supplierName, startDate, endDate, operatorName);

            return statementId;
        } catch (Exception e) {
            log.error("供应商对账单生成失败 - 供应商: {}, 错误: {}", supplierName, e.getMessage(), e);
            throw new ServiceException("供应商对账单生成失败：" + e.getMessage());
        }
    }

    /**
     * 生成管理费用对账单
     *
     * @param departmentId   部门ID
     * @param departmentName 部门名称
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 对账单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateManagementExpenseStatement(Long departmentId, String departmentName,
                                                   LocalDate startDate, LocalDate endDate,
                                                   Long operatorId, String operatorName) {
        try {
            // 创建对账单主记录
            FinStatement statement = new FinStatement();
            statement.setStatementCode(generateStatementCode("ME"));
            statement.setStatementName("管理费用对账单-" + departmentName);

            // 使用partnerId存储部门ID，partnerName存储部门名称
            statement.setPartnerId(departmentId);
            statement.setPartnerName(departmentName);
            statement.setStartDate(startDate);
            statement.setEndDate(endDate);
            statement.setStatementStatus(FinStatementStatus.DRAFT);

            // 计算期初余额（管理费用通常没有期初余额）
            BigDecimal openingBalance = BigDecimal.ZERO;
            statement.setOpeningBalance(openingBalance);

            // 插入对账单主记录
            baseMapper.insert(statement);
            Long statementId = statement.getStatementId();

            // 生成管理费用对账明细
            generateManagementExpenseStatementItems(statementId, departmentId, startDate, endDate);

            // 计算期末余额
            BigDecimal closingBalance = calculateClosingBalance(statementId, openingBalance);
            statement.setClosingBalance(closingBalance);
            baseMapper.updateById(statement);

            log.info("管理费用对账单生成成功 - 部门: {}, 期间: {} 至 {}, 操作人: {}",
                departmentName, startDate, endDate, operatorName);

            return statementId;
        } catch (Exception e) {
            log.error("管理费用对账单生成失败 - 部门: {}, 错误: {}", departmentName, e.getMessage(), e);
            throw new ServiceException("管理费用对账单生成失败：" + e.getMessage());
        }
    }

    /**
     * 根据收款方类型生成对账单
     *
     * @param payeeType    收款方类型
     * @param payeeId      收款方ID
     * @param payeeName    收款方名称
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 对账单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateStatementByPayeeType(FinPayeeType payeeType, Long payeeId, String payeeName,
                                             LocalDate startDate, LocalDate endDate,
                                             Long operatorId, String operatorName) {
        switch (payeeType) {
            case SUPPLIER:
                return generateSupplierStatement(payeeId, null, payeeName, startDate, endDate, operatorId, operatorName);
            case EMPLOYEE:
                return generateManagementExpenseStatement(payeeId, payeeName, startDate, endDate, operatorId, operatorName);
            default:
                throw new ServiceException("不支持的收款方类型：" + payeeType);
        }
    }

    /**
     * 对账单确认
     *
     * @param statementId   对账单ID
     * @param confirmById   确认人ID
     * @param confirmByName 确认人姓名
     * @param confirmRemark 确认备注
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmStatement(Long statementId, Long confirmById, String confirmByName, String confirmRemark) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            if (!"PENDING_CONFIRMATION".equals(statement.getStatementStatus())) {
                throw new ServiceException("对账单状态不允许确认");
            }

            // 更新对账单状态
            statement.setStatementStatus(FinStatementStatus.CONFIRMED);
            statement.setRemark(confirmRemark);

            boolean result = baseMapper.updateById(statement) > 0;

            if (result) {
                log.info("对账单确认成功 - 对账单: {}, 确认人: {}", statement.getStatementCode(), confirmByName);
            }

            return result;
        } catch (Exception e) {
            log.error("对账单确认失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账单确认失败：" + e.getMessage());
        }
    }

    /**
     * 计算期初余额
     */
    private BigDecimal calculateOpeningBalance(Long companyId, LocalDate startDate, String statementType) {
        // 计算指定日期前的余额
        // AR类型：计算应收余额 = 应收总额 - 已收金额
        // AP类型：计算应付余额 = 应付总额 - 已付金额
        return BigDecimal.ZERO;
    }

    /**
     * 生成对账明细
     */
    private void generateStatementItems(Long statementId, Long companyId, LocalDate startDate, LocalDate endDate, String statementType) {
        // 根据对账类型生成明细
        if ("AR".equals(statementType)) {
            generateArStatementItems(statementId, companyId, startDate, endDate);
        } else if ("AP".equals(statementType)) {
            generateApStatementItems(statementId, companyId, startDate, endDate);
        }
    }

    /**
     * 生成应收对账明细 (客户对账)
     */
    private void generateArStatementItems(Long statementId, Long customerId, LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成客户对账明细 - 对账单ID: {}, 客户ID: {}, 期间: {} 至 {}",
                statementId, customerId, startDate, endDate);

            // 查询期间内的销售订单
            List<SaleOrderStatementItem> saleOrders = querySaleOrdersForStatement(
                customerId, startDate, endDate);

            // 查询期间内的出库单
            List<OutboundStatementItem> outbounds = queryOutboundsForStatement(
                customerId, startDate, endDate);

            // 查询期间内的应收账款
            List<ReceivableStatementItem> receivables = queryReceivablesForStatement(
                customerId, startDate, endDate);

            // 查询期间内的收款记录
            List<ReceiptStatementItem> receipts = queryReceiptsForStatement(
                customerId, startDate, endDate);

            // 生成销售订单对账明细
            generateSaleOrderStatementItems(statementId, saleOrders);

            // 生成出库单对账明细
            generateOutboundStatementItems(statementId, outbounds);

            // 生成应收账款对账明细
            generateReceivableStatementItems(statementId, receivables);

            // 生成收款对账明细
            generateReceiptStatementItems(statementId, receipts);

            // 计算客户对账汇总信息
            calculateCustomerStatementSummary(statementId);

            log.info("客户对账明细生成完成 - 对账单ID: {}, 销售订单: {}笔, 出库单: {}笔, 应收: {}笔, 收款: {}笔",
                statementId, saleOrders.size(), outbounds.size(), receivables.size(), receipts.size());

        } catch (Exception e) {
            log.error("生成客户对账明细失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("生成客户对账明细失败：" + e.getMessage());
        }
    }

    /**
     * 查询销售订单用于对账
     */
    private List<SaleOrderStatementItem> querySaleOrdersForStatement(Long customerId,
                                                                     LocalDate startDate, LocalDate endDate) {
        // TODO: 实现销售订单查询逻辑
        // 查询条件：
        // 客户ID匹配
        // 订单日期在期间内
        // 订单状态为已确认或已完成
        // 包含订单编号、订单日期、订单金额、订单状态等信息
        return new ArrayList<>();
    }

    /**
     * 查询出库单用于对账
     */
    private List<OutboundStatementItem> queryOutboundsForStatement(Long customerId,
                                                                   LocalDate startDate, LocalDate endDate) {
        // TODO: 实现出库单查询逻辑
        // 查询条件：
        // 客户ID匹配
        // 出库时间在期间内
        // 出库状态为已完成
        // 包含出库编号、出库时间、出库金额、关联销售订单等信息
        return new ArrayList<>();
    }

    /**
     * 查询应收账款用于对账
     */
    private List<ReceivableStatementItem> queryReceivablesForStatement(Long customerId,
                                                                       LocalDate startDate, LocalDate endDate) {
        // TODO: 实现应收账款查询逻辑
        // 查询条件：
        // 客户ID匹配
        // 应收日期在期间内
        // 应收状态为已审核
        // 包含应收编号、应收日期、应收金额、已收款金额等信息
        return new ArrayList<>();
    }

    /**
     * 查询收款记录用于对账
     */
    private List<ReceiptStatementItem> queryReceiptsForStatement(Long customerId,
                                                                 LocalDate startDate, LocalDate endDate) {
        // TODO: 实现收款记录查询逻辑
        // 查询条件：
        // 客户ID匹配
        // 收款日期在期间内
        // 收款状态为已确认
        // 包含收款编号、收款日期、收款金额、核销明细等信息
        return new ArrayList<>();
    }

    /**
     * 生成销售订单对账明细
     */
    private void generateSaleOrderStatementItems(Long statementId, List<SaleOrderStatementItem> saleOrders) {
        for (SaleOrderStatementItem order : saleOrders) {
            // TODO: 创建对账明细记录
            // 明细类型：SALE_ORDER
            // 包含：订单编号、订单日期、订单金额、订单状态等
        }
    }

    /**
     * 生成出库单对账明细
     */
    private void generateOutboundStatementItems(Long statementId, List<OutboundStatementItem> outbounds) {
        for (OutboundStatementItem outbound : outbounds) {
            // TODO: 创建对账明细记录
            // 明细类型：OUTBOUND
            // 包含：出库编号、出库时间、出库金额、关联订单等
        }
    }

    /**
     * 生成应收账款对账明细
     */
    private void generateReceivableStatementItems(Long statementId, List<ReceivableStatementItem> receivables) {
        for (ReceivableStatementItem receivable : receivables) {
            // TODO: 创建对账明细记录
            // 明细类型：RECEIVABLE
            // 包含：应收编号、应收日期、应收金额、已收款金额等
        }
    }

    /**
     * 生成收款对账明细
     */
    private void generateReceiptStatementItems(Long statementId, List<ReceiptStatementItem> receipts) {
        for (ReceiptStatementItem receipt : receipts) {
            // TODO: 创建对账明细记录
            // 明细类型：RECEIPT
            // 包含：收款编号、收款日期、收款金额、核销明细等
        }
    }

    /**
     * 计算客户对账汇总信息
     */
    private void calculateCustomerStatementSummary(Long statementId) {
        // TODO: 计算客户对账汇总信息
        // 汇总销售订单金额
        // 汇总出库金额
        // 汇总应收账款金额
        // 汇总收款金额
        // 计算差异金额
        // 更新对账单汇总信息
    }

    /**
     * 生成应付对账明细
     */
    private void generateApStatementItems(Long statementId, Long supplierId, LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成供应商对账明细 - 对账单ID: {}, 供应商ID: {}, 期间: {} 至 {}",
                statementId, supplierId, startDate, endDate);

            // 查询期间内的应付应付
            List<InvoiceStatementItem> invoices = queryInvoicesForStatement(supplierId, startDate, endDate);

            // 查询期间内的付款记录
            List<PaymentStatementItem> payments = queryPaymentsForStatement(supplierId, startDate, endDate);

            // 生成应付对账明细
            generateInvoiceStatementItems(statementId, invoices);

            // 生成付款对账明细
            generatePaymentStatementItems(statementId, payments);

            // 计算对账汇总信息
            calculateStatementSummary(statementId);

            log.info("供应商对账明细生成完成 - 对账单ID: {}, 应付: {}笔, 付款: {}笔",
                statementId, invoices.size(), payments.size());

        } catch (Exception e) {
            log.error("生成供应商对账明细失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("生成供应商对账明细失败：" + e.getMessage());
        }
    }

    /**
     * 查询采购订单用于对账
     */
    private List<PurchaseOrderStatementItem> queryPurchaseOrdersForStatement(Long supplierId,
                                                                             LocalDate startDate, LocalDate endDate) {
        // TODO: 实现采购订单查询逻辑
        // 查询条件：
        // 供应商ID匹配
        // 订单日期在期间内
        // 订单状态为已确认或已完成
        // 包含订单编号、订单日期、订单金额、订单状态等信息
        return new ArrayList<>();
    }

    /**
     * 查询入库单用于对账
     */
    private List<InboundStatementItem> queryInboundsForStatement(Long supplierId,
                                                                 LocalDate startDate, LocalDate endDate) {
        // TODO: 实现入库单查询逻辑
        // 查询条件：
        // 供应商ID匹配
        // 入库时间在期间内
        // 入库状态为已完成
        // 包含入库编号、入库时间、入库金额、关联采购订单等信息
        return new ArrayList<>();
    }

    /**
     * 查询应付用于对账
     */
    private List<InvoiceStatementItem> queryInvoicesForStatement(Long supplierId,
                                                                 LocalDate startDate, LocalDate endDate) {
        FinApInvoiceBo bo = new FinApInvoiceBo();
        bo.setPayeeId(supplierId);
        bo.setParams(new HashMap<>());
        bo.getParams().put("beginInvoiceDate", startDate);
        bo.getParams().put("endInvoiceDate", endDate);

        // 调用应付服务查询
        List<FinApInvoiceVo> invoices = finApInvoiceService.queryList(bo);

        // 转换为对账单明细项
        return invoices.stream().map(invoice -> {
            InvoiceStatementItem item = new InvoiceStatementItem();
            item.setInvoiceId(invoice.getInvoiceId());
            item.setInvoiceCode(invoice.getInvoiceCode());
            item.setInvoiceDate(invoice.getInvoiceDate());
            item.setAmount(invoice.getAmount());
            item.setStatus(invoice.getInvoiceStatus().getValue());
            // appliedAmount 需要在后续步骤中单独计算
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 查询付款记录用于对账
     */
    private List<PaymentStatementItem> queryPaymentsForStatement(Long supplierId,
                                                                 LocalDate startDate, LocalDate endDate) {
        FinApPaymentOrderBo bo = new FinApPaymentOrderBo();
        bo.setPayeeId(supplierId);
        bo.setParams(new HashMap<>());
        bo.getParams().put("beginPaymentDate", startDate);
        bo.getParams().put("endPaymentDate", endDate);

        // 调用付款单服务查询
        List<FinApPaymentOrderVo> payments = finApPaymentOrderService.queryList(bo);

        // 转换为对账单明细项
        return payments.stream().map(payment -> {
            PaymentStatementItem item = new PaymentStatementItem();
            item.setPaymentId(payment.getPaymentId());
            item.setPaymentCode(payment.getPaymentCode());
            item.setPaymentDate(payment.getPaymentDate());
            item.setAmount(payment.getPaymentAmount());
            item.setStatus(payment.getPaymentStatus().getValue());
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 生成采购订单对账明细
     */
    private void generatePurchaseOrderStatementItems(Long statementId, List<PurchaseOrderStatementItem> purchaseOrders) {
        for (PurchaseOrderStatementItem order : purchaseOrders) {
            // TODO: 创建对账明细记录
            // 明细类型：PURCHASE_ORDER
            // 包含：订单编号、订单日期、订单金额、订单状态等
        }
    }

    /**
     * 生成入库单对账明细
     */
    private void generateInboundStatementItems(Long statementId, List<InboundStatementItem> inbounds) {
        for (InboundStatementItem inbound : inbounds) {
            // TODO: 创建对账明细记录
            // 明细类型：INBOUND
            // 包含：入库编号、入库时间、入库金额、关联订单等
        }
    }

    /**
     * 生成应付对账明细
     */
    private void generateInvoiceStatementItems(Long statementId, List<InvoiceStatementItem> invoices) {
        for (InvoiceStatementItem invoice : invoices) {
            // TODO: 创建对账明细记录
            // 明细类型：INVOICE
            // 包含：应付编号、应付日期、应付金额、已核销金额等
        }
    }

    /**
     * 生成付款对账明细
     */
    private void generatePaymentStatementItems(Long statementId, List<PaymentStatementItem> payments) {
        for (PaymentStatementItem payment : payments) {
            // TODO: 创建对账明细记录
            // 明细类型：PAYMENT
            // 包含：付款编号、付款日期、付款金额、核销明细等
        }
    }

    /**
     * 计算对账汇总信息
     */
    private void calculateStatementSummary(Long statementId) {
        // TODO: 计算对账汇总信息
        // 汇总采购订单金额
        // 汇总入库金额
        // 汇总应付金额
        // 汇总付款金额
        // 计算差异金额
        // 更新对账单汇总信息
    }

    /**
     * 计算期末余额
     */
    private BigDecimal calculateClosingBalance(Long statementId, BigDecimal openingBalance) {
        // 查询本期所有对账明细
        List<FinStatementItemVo> items = finStatementItemService.queryListByStatementId(statementId);

        // 累加本期发生额（借方-贷方）
        BigDecimal currentPeriodAmount = items.stream()
            .map(item -> {
                BigDecimal debit = item.getAmountDebit() != null ? item.getAmountDebit() : BigDecimal.ZERO;
                BigDecimal credit = item.getAmountCredit() != null ? item.getAmountCredit() : BigDecimal.ZERO;
                return debit.subtract(credit);
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 期末余额 = 期初余额 + 本期发生额
        return openingBalance.add(currentPeriodAmount);
    }

    /**
     * 生成对账单编号
     */
    private String generateStatementCode(String type) {
        return type + "ST" + System.currentTimeMillis();
    }

    /**
     * 对账差异分析
     *
     * @param statementId 对账单ID
     * @return 差异分析结果
     */
    public List<StatementDifference> analyzeStatementDifferences(Long statementId) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            List<StatementDifference> differences = new ArrayList<>();

            // 金额差异分析
            differences.addAll(analyzeAmountDifferences(statementId));

            // 时间差异分析
            differences.addAll(analyzeTimeDifferences(statementId));

            // 单据匹配差异分析
            differences.addAll(analyzeDocumentMatchDifferences(statementId));

            // 核销差异分析
            differences.addAll(analyzeApplyDifferences(statementId));

            log.info("对账差异分析完成 - 对账单: {}, 差异项数: {}",
                statement.getStatementCode(), differences.size());

            return differences;
        } catch (Exception e) {
            log.error("对账差异分析失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账差异分析失败：" + e.getMessage());
        }
    }

    /**
     * 对账单确认
     *
     * @param statementId   对账单ID
     * @param confirmType   确认类型 (FULL/PARTIAL)
     * @param confirmRemark 确认备注
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmStatementWithWorkflow(Long statementId, String confirmType, String confirmRemark) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            if (!"GENERATED".equals(statement.getStatementStatus())) {
                throw new ServiceException("对账单状态不允许确认，当前状态：" + statement.getStatementStatus());
            }

            // 检查是否有未处理的差异
            List<StatementDifference> differences = analyzeStatementDifferences(statementId);
            if ("FULL".equals(confirmType) && !differences.isEmpty()) {
                throw new ServiceException("存在未处理的差异，无法完全确认");
            }

            // 更新对账单状态
            if ("FULL".equals(confirmType)) {
                statement.setStatementStatus(FinStatementStatus.CONFIRMED);
            } else if ("PARTIAL".equals(confirmType)) {
                statement.setStatementStatus(FinStatementStatus.PARTIALLY_CONFIRMED);
            } else {
                throw new ServiceException("无效的确认类型：" + confirmType);
            }

            // TODO: FinStatement实体中没有confirmTime、confirmById、confirmByName字段
            // 暂时在remark中记录确认信息，待实体完善后修正
            // statement.setConfirmTime(LocalDateTime.now());
            // statement.setConfirmById(LoginHelper.getUserId());
            // statement.setConfirmByName(LoginHelper.getLoginUser().getNickname());
            statement.setRemark(confirmRemark);

            boolean result = baseMapper.updateById(statement) > 0;

            if (result) {
                // 执行确认后的业务逻辑
                executeStatementConfirmedLogic(statement, confirmType);

                log.info("对账单确认成功 - 对账单: {}, 确认类型: {}",
                    statement.getStatementCode(), confirmType);
            }

            return result;
        } catch (Exception e) {
            log.error("对账单确认失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账单确认失败：" + e.getMessage());
        }
    }

    /**
     * 创建对账争议
     *
     * @param statementId   对账单ID
     * @param disputeType   争议类型
     * @param disputeAmount 争议金额
     * @param disputeReason 争议原因
     * @return 争议ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createStatementDispute(Long statementId, String disputeType,
                                       BigDecimal disputeAmount, String disputeReason) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            if (StringUtils.isBlank(disputeReason)) {
                throw new ServiceException("争议原因不能为空");
            }

            // TODO: 创建争议记录
            // FinStatementDispute dispute = new FinStatementDispute();
            // dispute.setStatementId(statementId);
            // dispute.setDisputeType(disputeType);
            // dispute.setDisputeAmount(disputeAmount);
            // dispute.setDisputeReason(disputeReason);
            // dispute.setDisputeStatus("OPEN");
            // dispute.setCreateById(LoginHelper.getUserId());
            // dispute.setCreateByName(LoginHelper.getLoginUser().getNickname());
            // dispute.setCreateTime(LocalDateTime.now());

            // Long disputeId = finStatementDisputeService.insertByBo(dispute);

            // 更新对账单状态为有争议
            statement.setStatementStatus(FinStatementStatus.DISPUTED);
            baseMapper.updateById(statement);

            Long disputeId = System.currentTimeMillis(); // 模拟争议ID

            log.info("对账争议创建成功 - 对账单: {}, 争议类型: {}, 争议金额: {}, 争议ID: {}",
                statement.getStatementCode(), disputeType, disputeAmount, disputeId);

            return disputeId;
        } catch (Exception e) {
            log.error("创建对账争议失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("创建对账争议失败：" + e.getMessage());
        }
    }

    /**
     * 分析金额差异
     */
    private List<StatementDifference> analyzeAmountDifferences(Long statementId) {
        // TODO: 实现金额差异分析
        // 比较采购订单金额与入库金额
        // 比较入库金额与应付金额
        // 比较应付金额与付款金额
        // 识别异常的金额差异
        return new ArrayList<>();
    }

    /**
     * 分析时间差异
     */
    private List<StatementDifference> analyzeTimeDifferences(Long statementId) {
        // TODO: 实现时间差异分析
        // 检查订单日期与入库时间的合理性
        // 检查入库时间与应付日期的合理性
        // 检查应付日期与付款日期的合理性
        // 识别异常的时间间隔
        return new ArrayList<>();
    }

    /**
     * 分析单据匹配差异
     */
    private List<StatementDifference> analyzeDocumentMatchDifferences(Long statementId) {
        // TODO: 实现单据匹配差异分析
        // 检查入库单是否有对应的采购订单
        // 检查应付是否有对应的入库单
        // 检查付款是否有对应的应付
        // 识别孤立的单据
        return new ArrayList<>();
    }

    /**
     * 分析核销差异
     */
    private List<StatementDifference> analyzeApplyDifferences(Long statementId) {
        // TODO: 实现核销差异分析
        // 检查应付的核销状态
        // 检查付款的核销状态
        // 识别核销金额异常
        // 识别重复核销
        return new ArrayList<>();
    }

    /**
     * 执行对账单确认后的业务逻辑
     */
    private void executeStatementConfirmedLogic(FinStatement statement, String confirmType) {
        try {
            // 发送确认通知
            sendStatementConfirmedNotification(statement, confirmType);

            // 更新相关业务状态
            updateRelatedBusinessStatus(statement, confirmType);

            // 触发后续业务流程
            triggerSubsequentProcess(statement, confirmType);

            log.info("对账单确认后业务逻辑执行完成 - 对账单: {}", statement.getStatementCode());
        } catch (Exception e) {
            log.warn("执行对账单确认后业务逻辑失败 - 对账单: {}, 错误: {}",
                statement.getStatementCode(), e.getMessage());
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 发送对账单确认通知
     */
    private void sendStatementConfirmedNotification(FinStatement statement, String confirmType) {
        // TODO: 实现对账单确认通知
        log.info("发送对账单确认通知 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 更新相关业务状态
     */
    private void updateRelatedBusinessStatus(FinStatement statement, String confirmType) {
        // TODO: 更新相关业务状态
        log.debug("更新相关业务状态 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 触发后续业务流程
     */
    private void triggerSubsequentProcess(FinStatement statement, String confirmType) {
        // TODO: 触发后续业务流程
        log.debug("触发后续业务流程 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 客户对账差异分析 (专门针对应收业务)
     *
     * @param statementId 对账单ID
     * @return 客户对账差异分析结果
     */
    public List<CustomerStatementDifference> analyzeCustomerStatementDifferences(Long statementId) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            List<CustomerStatementDifference> differences = new ArrayList<>();

            // 销售订单与出库单差异分析
            differences.addAll(analyzeSaleOrderOutboundDifferences(statementId));

            // 出库单与应收账款差异分析
            differences.addAll(analyzeOutboundReceivableDifferences(statementId));

            // 应收账款与收款差异分析
            differences.addAll(analyzeReceivableReceiptDifferences(statementId));

            // 客户信用状况分析
            differences.addAll(analyzeCustomerCreditDifferences(statementId));

            // 逾期应收分析
            differences.addAll(analyzeOverdueReceivableDifferences(statementId));

            log.info("客户对账差异分析完成 - 对账单: {}, 差异项数: {}",
                statement.getStatementCode(), differences.size());

            return differences;
        } catch (Exception e) {
            log.error("客户对账差异分析失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("客户对账差异分析失败：" + e.getMessage());
        }
    }

    /**
     * 客户对账单确认流程
     *
     * @param statementId   对账单ID
     * @param confirmType   确认类型 (CUSTOMER_CONFIRM/COMPANY_CONFIRM)
     * @param confirmRemark 确认备注
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmCustomerStatement(Long statementId, String confirmType, String confirmRemark) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            if (!"GENERATED".equals(statement.getStatementStatus()) && !"CUSTOMER_CONFIRMED".equals(statement.getStatementStatus())) {
                throw new ServiceException("对账单状态不允许确认，当前状态：" + statement.getStatementStatus());
            }

            // 检查是否有未处理的差异
            List<CustomerStatementDifference> differences = analyzeCustomerStatementDifferences(statementId);
            boolean hasHighSeverityDifferences = differences.stream()
                .anyMatch(diff -> "HIGH".equals(diff.getSeverity()));

            if (hasHighSeverityDifferences && "CUSTOMER_CONFIRM".equals(confirmType)) {
                throw new ServiceException("存在高严重级别的差异，客户无法确认");
            }

            // 更新对账单状态
            if ("CUSTOMER_CONFIRM".equals(confirmType)) {
                statement.setStatementStatus(FinStatementStatus.CUSTOMER_CONFIRMED);
                // TODO: FinStatement实体中没有customerConfirmTime字段，需要重新设计客户确认时间记录
                // 暂时在remark中记录客户确认时间，待实体完善后修正
                // statement.setCustomerConfirmTime(LocalDateTime.now());
                // statement.setCustomerConfirmById(LoginHelper.getUserId());
                // statement.setCustomerConfirmByName(LoginHelper.getLoginUser().getNickname());
            } else if ("COMPANY_CONFIRM".equals(confirmType)) {
                if (FinStatementStatus.CUSTOMER_CONFIRMED.equals(statement.getStatementStatus())) {
                    statement.setStatementStatus(FinStatementStatus.CONFIRMED);
                } else {
                    statement.setStatementStatus(FinStatementStatus.COMPANY_CONFIRMED);
                }
                // TODO: FinStatement实体中没有confirmTime、confirmById、confirmByName字段
                // 暂时在remark中记录确认信息，待实体完善后修正
                // statement.setConfirmTime(LocalDateTime.now());
                // statement.setConfirmById(LoginHelper.getUserId());
                // statement.setConfirmByName(LoginHelper.getLoginUser().getNickname());
            }

            statement.setRemark(confirmRemark);
            boolean result = baseMapper.updateById(statement) > 0;

            if (result) {
                // 执行确认后的业务逻辑
                executeCustomerStatementConfirmedLogic(statement, confirmType);

                log.info("客户对账单确认成功 - 对账单: {}, 确认类型: {}",
                    statement.getStatementCode(), confirmType);
            }

            return result;
        } catch (Exception e) {
            log.error("客户对账单确认失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("客户对账单确认失败：" + e.getMessage());
        }
    }

    /**
     * 分析销售订单与出库单差异
     */
    private List<CustomerStatementDifference> analyzeSaleOrderOutboundDifferences(Long statementId) {
        // TODO: 实现销售订单与出库单差异分析
        // 比较销售订单金额与出库金额
        // 检查订单是否完全出库
        // 识别超额出库或短缺出库
        return new ArrayList<>();
    }

    /**
     * 分析出库单与应收账款差异
     */
    private List<CustomerStatementDifference> analyzeOutboundReceivableDifferences(Long statementId) {
        // TODO: 实现出库单与应收账款差异分析
        // 比较出库金额与应收金额
        // 检查出库单是否生成应收账款
        // 识别应收账款生成异常
        return new ArrayList<>();
    }

    /**
     * 分析应收账款与收款差异
     */
    private List<CustomerStatementDifference> analyzeReceivableReceiptDifferences(Long statementId) {
        // TODO: 实现应收账款与收款差异分析
        // 比较应收金额与收款金额
        // 检查应收账款的收款状态
        // 识别收款异常和逾期情况
        return new ArrayList<>();
    }

    /**
     * 分析客户信用状况差异
     */
    private List<CustomerStatementDifference> analyzeCustomerCreditDifferences(Long statementId) {
        // TODO: 实现客户信用状况分析
        // 检查客户信用额度使用情况
        // 分析客户付款习惯
        // 识别信用风险
        return new ArrayList<>();
    }

    /**
     * 分析逾期应收差异
     */
    private List<CustomerStatementDifference> analyzeOverdueReceivableDifferences(Long statementId) {
        // TODO: 实现逾期应收分析
        // 识别逾期应收账款
        // 计算逾期天数和金额
        // 分析逾期趋势
        return new ArrayList<>();
    }

    /**
     * 执行客户对账单确认后的业务逻辑
     */
    private void executeCustomerStatementConfirmedLogic(FinStatement statement, String confirmType) {
        try {
            // 发送确认通知
            sendCustomerStatementConfirmedNotification(statement, confirmType);

            // 更新客户信用记录
            updateCustomerCreditRecord(statement, confirmType);

            // 触发后续业务流程
            triggerCustomerStatementSubsequentProcess(statement, confirmType);

            log.info("客户对账单确认后业务逻辑执行完成 - 对账单: {}", statement.getStatementCode());
        } catch (Exception e) {
            log.warn("执行客户对账单确认后业务逻辑失败 - 对账单: {}, 错误: {}",
                statement.getStatementCode(), e.getMessage());
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 发送客户对账单确认通知
     */
    private void sendCustomerStatementConfirmedNotification(FinStatement statement, String confirmType) {
        // TODO: 实现客户对账单确认通知
        log.info("发送客户对账单确认通知 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 更新客户信用记录
     */
    private void updateCustomerCreditRecord(FinStatement statement, String confirmType) {
        // TODO: 更新客户信用记录
        log.debug("更新客户信用记录 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 触发客户对账后续流程
     */
    private void triggerCustomerStatementSubsequentProcess(FinStatement statement, String confirmType) {
        // TODO: 触发客户对账后续流程
        log.debug("触发客户对账后续流程 - 对账单: {}, 确认类型: {}", statement.getStatementCode(), confirmType);
    }

    /**
     * 对账单模板生成
     *
     * @param statementId  对账单ID
     * @param templateType 模板类型 (STANDARD/DETAILED/SUMMARY)
     * @return 对账单模板数据
     */
    public Map<String, Object> generateStatementTemplate(Long statementId, String templateType) {
        try {
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            Map<String, Object> templateData = new HashMap<>();

            // 基础信息
            templateData.put("statementInfo", buildStatementBasicInfo(statement));

            // 根据模板类型生成不同内容
            switch (templateType) {
                case "STANDARD":
                    templateData.put("items", buildStandardStatementItems(statementId));
                    templateData.put("summary", buildStandardSummary(statementId));
                    break;
                case "DETAILED":
                    templateData.put("items", buildDetailedStatementItems(statementId));
                    templateData.put("summary", buildDetailedSummary(statementId));
                    templateData.put("differences", analyzeStatementDifferences(statementId));
                    break;
                case "SUMMARY":
                    templateData.put("summary", buildSummarySummary(statementId));
                    templateData.put("charts", buildSummaryCharts(statementId));
                    break;
                default:
                    throw new ServiceException("无效的模板类型：" + templateType);
            }

            // 格式化信息
            templateData.put("formatInfo", buildFormatInfo(templateType));
            templateData.put("generateTime", LocalDateTime.now());

            log.info("对账单模板生成成功 - 对账单: {}, 模板类型: {}",
                statement.getStatementCode(), templateType);

            return templateData;
        } catch (Exception e) {
            log.error("对账单模板生成失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账单模板生成失败：" + e.getMessage());
        }
    }

    /**
     * 对账单格式化输出
     *
     * @param statementId  对账单ID
     * @param outputFormat 输出格式 (PDF/EXCEL/HTML)
     * @param templateType 模板类型
     * @return 格式化输出结果
     */
    public Map<String, Object> formatStatementOutput(Long statementId, String outputFormat, String templateType) {
        try {
            // 生成模板数据
            Map<String, Object> templateData = generateStatementTemplate(statementId, templateType);

            // 根据输出格式进行格式化
            Map<String, Object> outputResult = new HashMap<>();

            switch (outputFormat) {
                case "PDF":
                    outputResult = formatToPdf(templateData);
                    break;
                case "EXCEL":
                    outputResult = formatToExcel(templateData);
                    break;
                case "HTML":
                    outputResult = formatToHtml(templateData);
                    break;
                default:
                    throw new ServiceException("无效的输出格式：" + outputFormat);
            }

            outputResult.put("statementId", statementId);
            outputResult.put("outputFormat", outputFormat);
            outputResult.put("templateType", templateType);
            outputResult.put("generateTime", LocalDateTime.now());

            log.info("对账单格式化输出成功 - 对账单ID: {}, 格式: {}", statementId, outputFormat);

            return outputResult;
        } catch (Exception e) {
            log.error("对账单格式化输出失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账单格式化输出失败：" + e.getMessage());
        }
    }

    /**
     * 批量对账单生成
     *
     * @param batchRequest 批量生成请求
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateStatements(BatchStatementRequest batchRequest) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (StatementGenerateRequest request : batchRequest.getRequests()) {
                try {
                    // 生成对账单
                    Long statementId = generateStatementByRequest(request);

                    successList.add(Map.of(
                        "partnerId", request.getPartnerId(),
                        "partnerName", request.getPartnerName(),
                        "statementId", statementId,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "partnerId", request.getPartnerId(),
                        "partnerName", request.getPartnerName(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", batchRequest.getRequests().size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", LoginHelper.getUserId());
            result.put("operatorName", LoginHelper.getLoginUser().getNickname());
            result.put("operationTime", LocalDateTime.now());

            log.info("批量对账单生成完成 - 总数: {}, 成功: {}, 失败: {}",
                batchRequest.getRequests().size(), successList.size(), failureList.size());

            return result;
        } catch (Exception e) {
            log.error("批量对账单生成失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量对账单生成失败：" + e.getMessage());
        }
    }

    /**
     * 构建对账单基础信息
     */
    private Map<String, Object> buildStatementBasicInfo(FinStatement statement) {
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("statementCode", statement.getStatementCode());
        basicInfo.put("statementName", statement.getStatementName());
        // TODO: FinStatement实体中没有statementType字段，需要重新设计对账类型记录
        // 暂时使用默认值，待实体完善后修正
        basicInfo.put("statementType", "UNKNOWN"); // 原: statement.getStatementType()
        basicInfo.put("statementStatus", statement.getStatementStatus());
        basicInfo.put("startDate", statement.getStartDate());
        basicInfo.put("endDate", statement.getEndDate());
        basicInfo.put("partnerId", statement.getPartnerId());
        basicInfo.put("partnerName", statement.getPartnerName());
        basicInfo.put("createTime", statement.getCreateTime());
        return basicInfo;
    }

    /**
     * 构建标准对账明细
     */
    private List<Map<String, Object>> buildStandardStatementItems(Long statementId) {
        // TODO: 构建标准对账明细
        // 包含：单据编号、单据日期、单据类型、金额、状态等基本信息
        return new ArrayList<>();
    }

    /**
     * 构建详细对账明细
     */
    private List<Map<String, Object>> buildDetailedStatementItems(Long statementId) {
        // TODO: 构建详细对账明细
        // 包含：完整的单据信息、明细信息、关联关系等
        return new ArrayList<>();
    }

    /**
     * 构建标准汇总信息
     */
    private Map<String, Object> buildStandardSummary(Long statementId) {
        Map<String, Object> summary = new HashMap<>();
        // TODO: 计算标准汇总信息
        summary.put("amount", BigDecimal.ZERO);
        summary.put("totalCount", 0);
        summary.put("paidAmount", BigDecimal.ZERO);
        summary.put("unpaidAmount", BigDecimal.ZERO);
        return summary;
    }

    /**
     * 构建详细汇总信息
     */
    private Map<String, Object> buildDetailedSummary(Long statementId) {
        Map<String, Object> summary = buildStandardSummary(statementId);
        // TODO: 添加详细汇总信息
        summary.put("averageAmount", BigDecimal.ZERO);
        summary.put("maxAmount", BigDecimal.ZERO);
        summary.put("minAmount", BigDecimal.ZERO);
        summary.put("overdueAmount", BigDecimal.ZERO);
        summary.put("overdueCount", 0);
        return summary;
    }

    /**
     * 构建摘要汇总信息
     */
    private Map<String, Object> buildSummarySummary(Long statementId) {
        Map<String, Object> summary = new HashMap<>();
        // TODO: 构建摘要汇总信息
        summary.put("amount", BigDecimal.ZERO);
        summary.put("paidAmount", BigDecimal.ZERO);
        summary.put("unpaidAmount", BigDecimal.ZERO);
        summary.put("overdueAmount", BigDecimal.ZERO);
        return summary;
    }

    /**
     * 构建摘要图表数据
     */
    private Map<String, Object> buildSummaryCharts(Long statementId) {
        Map<String, Object> charts = new HashMap<>();
        // TODO: 构建图表数据
        charts.put("amountChart", new ArrayList<>());
        charts.put("statusChart", new ArrayList<>());
        charts.put("trendChart", new ArrayList<>());
        return charts;
    }

    /**
     * 构建格式化信息
     */
    private Map<String, Object> buildFormatInfo(String templateType) {
        Map<String, Object> formatInfo = new HashMap<>();
        formatInfo.put("templateType", templateType);
        formatInfo.put("version", "1.0");
        formatInfo.put("locale", "zh_CN");
        formatInfo.put("currency", "CNY");
        return formatInfo;
    }

    /**
     * 格式化为PDF
     */
    private Map<String, Object> formatToPdf(Map<String, Object> templateData) {
        // TODO: 实现PDF格式化
        Map<String, Object> result = new HashMap<>();
        result.put("format", "PDF");
        result.put("fileName", "statement_" + System.currentTimeMillis() + ".pdf");
        result.put("fileSize", "1024KB");
        result.put("downloadUrl", "/download/pdf/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 格式化为Excel
     */
    private Map<String, Object> formatToExcel(Map<String, Object> templateData) {
        // TODO: 实现Excel格式化
        Map<String, Object> result = new HashMap<>();
        result.put("format", "EXCEL");
        result.put("fileName", "statement_" + System.currentTimeMillis() + ".xlsx");
        result.put("fileSize", "512KB");
        result.put("downloadUrl", "/download/excel/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 格式化为HTML
     */
    private Map<String, Object> formatToHtml(Map<String, Object> templateData) {
        // TODO: 实现HTML格式化
        Map<String, Object> result = new HashMap<>();
        result.put("format", "HTML");
        result.put("fileName", "statement_" + System.currentTimeMillis() + ".html");
        result.put("fileSize", "256KB");
        result.put("viewUrl", "/view/html/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 根据请求生成对账单
     */
    private Long generateStatementByRequest(StatementGenerateRequest request) {
        // TODO: 根据请求参数生成对账单
        // 创建对账单主记录
        // 生成对账明细
        // 计算汇总信息
        return System.currentTimeMillis(); // 模拟返回对账单ID
    }

    /**
     * 处理对账差异
     *
     * @param differenceId   差异ID
     * @param handlingMethod 处理方式
     * @param remark         处理备注
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleStatementDifference(Long differenceId, String handlingMethod, String remark) {
        try {
            // 校验参数
            if (differenceId == null) {
                throw new ServiceException("差异ID不能为空");
            }
            if (StringUtils.isBlank(handlingMethod)) {
                throw new ServiceException("处理方式不能为空");
            }

            // 获取差异信息
            // TODO: 需要实现获取差异记录的方法
            // StatementDifference difference = statementDifferenceService.queryById(differenceId);
            // if (difference == null) {
            //    throw new ServiceException("差异记录不存在");
            // }

            // 根据处理方式执行相应操作
            boolean result = false;
            switch (handlingMethod) {
                case "ACCEPT":
                    result = handleDifferenceAccept(differenceId, remark);
                    break;
                case "ADJUST":
                    result = handleDifferenceAdjust(differenceId, remark);
                    break;
                case "IGNORE":
                    result = handleDifferenceIgnore(differenceId, remark);
                    break;
                case "DISPUTE":
                    result = handleDifferenceDispute(differenceId, remark);
                    break;
                default:
                    throw new ServiceException("不支持的处理方式：" + handlingMethod);
            }

            // 更新差异状态
            if (result) {
                // TODO: 更新差异记录状态
                // statementDifferenceService.updateStatus(differenceId, "HANDLED",
                //    LoginHelper.getUserId(), LoginHelper.getLoginUser().getNickname());
            }

            log.info("对账差异处理成功 - 差异ID: {}, 处理方式: {}", differenceId, handlingMethod);
            return result;
        } catch (Exception e) {
            log.error("对账差异处理失败 - 差异ID: {}, 错误: {}", differenceId, e.getMessage(), e);
            throw new ServiceException("对账差异处理失败：" + e.getMessage());
        }
    }

    /**
     * 提起争议
     *
     * @param statementId   对账单ID
     * @param disputeReason 争议原因
     * @param evidence      争议证据
     * @return 争议ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long raiseDispute(Long statementId, String disputeReason, String evidence) {
        try {
            // 校验参数
            if (statementId == null) {
                throw new ServiceException("对账单ID不能为空");
            }
            if (StringUtils.isBlank(disputeReason)) {
                throw new ServiceException("争议原因不能为空");
            }

            // 获取对账单信息
            FinStatement statement = baseMapper.selectById(statementId);
            if (statement == null) {
                throw new ServiceException("对账单不存在");
            }

            // 检查对账单状态
            if (FinStatementStatus.CONFIRMED != statement.getStatementStatus() &&
                FinStatementStatus.PARTIALLY_CONFIRMED != statement.getStatementStatus()) {
                throw new ServiceException("只有已确认的对账单才能提起争议");
            }

            // 创建争议记录
            // TODO: 需要实现争议记录的创建
            // FinStatementDispute dispute = new FinStatementDispute();
            // dispute.setStatementId(statementId);
            // dispute.setDisputeCode(generateDisputeCode());
            // dispute.setDisputeReason(disputeReason);
            // dispute.setEvidence(evidence);
            // dispute.setDisputeStatus("OPEN");
            // dispute.setRaiseById(LoginHelper.getUserId());
            // dispute.setRaiseByName(LoginHelper.getLoginUser().getNickname());
            // dispute.setRaiseTime(LocalDateTime.now());
            //
            // finStatementDisputeService.insert(dispute);
            // Long disputeId = dispute.getDisputeId();

            // 更新对账单状态为争议中
            statement.setStatementStatus(FinStatementStatus.DISPUTED);
            baseMapper.updateById(statement);

            // 发送争议通知
            // sendDisputeNotification(statementId, disputeId, disputeReason);

            // 模拟返回争议ID
            Long disputeId = System.currentTimeMillis();

            log.info("争议提起成功 - 对账单: {}, 争议ID: {}, 争议原因: {}",
                statement.getStatementCode(), disputeId, disputeReason);

            return disputeId;
        } catch (Exception e) {
            log.error("提起争议失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("提起争议失败：" + e.getMessage());
        }
    }

    /**
     * 争议处理流程
     *
     * @param disputeId      争议ID
     * @param action         处理动作 (ACCEPT/REJECT/NEGOTIATE)
     * @param handleReason   处理原因
     * @param handleEvidence 处理证据
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleStatementDispute(Long disputeId, String action, String handleReason, String handleEvidence) {
        try {
            // TODO: 获取争议信息
            // FinStatementDispute dispute = finStatementDisputeService.queryById(disputeId);

            if (StringUtils.isBlank(handleReason)) {
                throw new ServiceException("处理原因不能为空");
            }

            // 根据处理动作执行不同逻辑
            switch (action) {
                case "ACCEPT":
                    handleDisputeAccept(disputeId, handleReason, handleEvidence);
                    break;
                case "REJECT":
                    handleDisputeReject(disputeId, handleReason, handleEvidence);
                    break;
                case "NEGOTIATE":
                    handleDisputeNegotiate(disputeId, handleReason, handleEvidence);
                    break;
                default:
                    throw new ServiceException("无效的处理动作：" + action);
            }

            // 记录处理历史
            recordDisputeHandleHistory(disputeId, action, handleReason, handleEvidence);

            // 发送处理通知
            sendDisputeHandleNotification(disputeId, action);

            log.info("争议处理成功 - 争议ID: {}, 处理动作: {}", disputeId, action);

            return true;
        } catch (Exception e) {
            log.error("争议处理失败 - 争议ID: {}, 错误: {}", disputeId, e.getMessage(), e);
            throw new ServiceException("争议处理失败：" + e.getMessage());
        }
    }

    /**
     * 争议跟踪查询
     *
     * @param disputeId 争议ID
     * @return 争议跟踪信息
     */
    public DisputeTrackingInfo getDisputeTracking(Long disputeId) {
        try {
            DisputeTrackingInfo trackingInfo = new DisputeTrackingInfo();
            trackingInfo.setDisputeId(disputeId);

            // 获取争议基本信息
            // TODO: 从数据库获取争议信息
            trackingInfo.setDisputeStatus("OPEN");
            trackingInfo.setDisputeType("AMOUNT_DIFFERENCE");
            trackingInfo.setDisputeAmount(new BigDecimal("1000.00"));
            trackingInfo.setCreateTime(LocalDateTime.now().minusDays(3));

            // 获取处理历史
            List<DisputeHandleRecord> handleHistory = getDisputeHandleHistory(disputeId);
            trackingInfo.setHandleHistory(handleHistory);

            // 计算处理进度
            int progress = calculateDisputeProgress(disputeId);
            trackingInfo.setProgress(progress);

            // 获取相关文档
            List<DisputeDocument> documents = getDisputeDocuments(disputeId);
            trackingInfo.setDocuments(documents);

            // 预估解决时间
            LocalDateTime estimatedResolveTime = estimateDisputeResolveTime(disputeId);
            trackingInfo.setEstimatedResolveTime(estimatedResolveTime);

            log.info("争议跟踪查询成功 - 争议ID: {}, 状态: {}", disputeId, trackingInfo.getDisputeStatus());

            return trackingInfo;
        } catch (Exception e) {
            log.error("争议跟踪查询失败 - 争议ID: {}, 错误: {}", disputeId, e.getMessage(), e);
            throw new ServiceException("争议跟踪查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量争议处理
     *
     * @param batchDisputeRequests 批量争议处理请求
     * @return 批量处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchHandleDisputes(List<DisputeHandleRequest> batchDisputeRequests) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (DisputeHandleRequest request : batchDisputeRequests) {
                try {
                    Boolean handleResult = handleStatementDispute(
                        request.getDisputeId(),
                        request.getAction(),
                        request.getHandleReason(),
                        request.getHandleEvidence()
                    );

                    if (handleResult) {
                        successList.add(Map.of(
                            "disputeId", request.getDisputeId(),
                            "action", request.getAction(),
                            "status", "SUCCESS"
                        ));
                    } else {
                        failureList.add(Map.of(
                            "disputeId", request.getDisputeId(),
                            "status", "FAILED",
                            "reason", "处理失败"
                        ));
                    }
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "disputeId", request.getDisputeId(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", batchDisputeRequests.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", LoginHelper.getUserId());
            result.put("operatorName", LoginHelper.getLoginUser().getNickname());
            result.put("operationTime", LocalDateTime.now());

            log.info("批量争议处理完成 - 总数: {}, 成功: {}, 失败: {}",
                batchDisputeRequests.size(), successList.size(), failureList.size());

            return result;
        } catch (Exception e) {
            log.error("批量争议处理失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量争议处理失败：" + e.getMessage());
        }
    }

    /**
     * 处理差异接受
     */
    private boolean handleDifferenceAccept(Long differenceId, String remark) {
        try {
            // 接受差异，不做调整
            // TODO: 更新差异记录状态为已接受
            // statementDifferenceService.updateStatus(differenceId, "ACCEPTED", remark);

            log.info("差异接受处理 - 差异ID: {}, 备注: {}", differenceId, remark);
            return true;
        } catch (Exception e) {
            log.error("差异接受处理失败 - 差异ID: {}, 错误: {}", differenceId, e.getMessage());
            return false;
        }
    }

    /**
     * 处理差异调整
     */
    private boolean handleDifferenceAdjust(Long differenceId, String remark) {
        try {
            // 执行差异调整
            // TODO: 根据差异类型执行相应的调整操作
            // - 金额差异：调整相关单据金额
            // - 数量差异：调整库存或订单数量
            // - 时间差异：更新相关日期

            log.info("差异调整处理 - 差异ID: {}, 备注: {}", differenceId, remark);
            return true;
        } catch (Exception e) {
            log.error("差异调整处理失败 - 差异ID: {}, 错误: {}", differenceId, e.getMessage());
            return false;
        }
    }

    /**
     * 处理差异忽略
     */
    private boolean handleDifferenceIgnore(Long differenceId, String remark) {
        try {
            // 忽略差异，标记为已处理
            // TODO: 更新差异记录状态为已忽略
            // statementDifferenceService.updateStatus(differenceId, "IGNORED", remark);

            log.info("差异忽略处理 - 差异ID: {}, 备注: {}", differenceId, remark);
            return true;
        } catch (Exception e) {
            log.error("差异忽略处理失败 - 差异ID: {}, 错误: {}", differenceId, e.getMessage());
            return false;
        }
    }

    /**
     * 处理差异争议
     */
    private boolean handleDifferenceDispute(Long differenceId, String remark) {
        try {
            // 将差异转为争议
            // TODO: 获取差异信息并创建争议记录
            // StatementDifference difference = statementDifferenceService.queryById(differenceId);
            // Long disputeId = raiseDispute(difference.getStatementId(), remark, "差异争议");

            log.info("差异争议处理 - 差异ID: {}, 备注: {}", differenceId, remark);
            return true;
        } catch (Exception e) {
            log.error("差异争议处理失败 - 差异ID: {}, 错误: {}", differenceId, e.getMessage());
            return false;
        }
    }

    /**
     * 生成争议编号
     */
    private String generateDisputeCode() {
        return "DISP" + System.currentTimeMillis();
    }

    /**
     * 发送争议通知
     */
    private void sendDisputeNotification(Long statementId, Long disputeId, String disputeReason) {
        // TODO: 发送争议通知给相关人员
        log.info("发送争议通知 - 对账单ID: {}, 争议ID: {}", statementId, disputeId);
    }

    /**
     * 处理争议接受
     */
    private void handleDisputeAccept(Long disputeId, String handleReason, String handleEvidence) {
        // TODO: 实现争议接受处理逻辑
        // 更新争议状态为已接受
        // 执行相应的业务调整
        // 更新相关对账单状态
        log.info("争议接受处理 - 争议ID: {}, 原因: {}", disputeId, handleReason);
    }

    /**
     * 处理争议拒绝
     */
    private void handleDisputeReject(Long disputeId, String handleReason, String handleEvidence) {
        // TODO: 实现争议拒绝处理逻辑
        // 更新争议状态为已拒绝
        // 记录拒绝原因和证据
        // 通知争议发起方
        log.info("争议拒绝处理 - 争议ID: {}, 原因: {}", disputeId, handleReason);
    }

    /**
     * 处理争议协商
     */
    private void handleDisputeNegotiate(Long disputeId, String handleReason, String handleEvidence) {
        // TODO: 实现争议协商处理逻辑
        // 更新争议状态为协商中
        // 创建协商记录
        // 安排协商会议或沟通
        log.info("争议协商处理 - 争议ID: {}, 原因: {}", disputeId, handleReason);
    }

    /**
     * 记录争议处理历史
     */
    private void recordDisputeHandleHistory(Long disputeId, String action, String handleReason, String handleEvidence) {
        // TODO: 记录争议处理历史到数据库
        log.debug("记录争议处理历史 - 争议ID: {}, 动作: {}", disputeId, action);
    }

    /**
     * 发送争议处理通知
     */
    private void sendDisputeHandleNotification(Long disputeId, String action) {
        // TODO: 发送争议处理通知
        log.info("发送争议处理通知 - 争议ID: {}, 动作: {}", disputeId, action);
    }

    /**
     * 获取争议处理历史
     */
    private List<DisputeHandleRecord> getDisputeHandleHistory(Long disputeId) {
        // TODO: 从数据库获取争议处理历史
        List<DisputeHandleRecord> history = new ArrayList<>();

        // 模拟数据
        DisputeHandleRecord record = new DisputeHandleRecord();
        record.setHandleTime(LocalDateTime.now().minusDays(1));
        record.setHandleAction("CREATE");
        record.setHandleReason("金额差异");
        record.setHandlerName("张三");
        history.add(record);

        return history;
    }

    /**
     * 计算争议处理进度
     */
    private int calculateDisputeProgress(Long disputeId) {
        // TODO: 根据争议状态和处理历史计算进度
        // 创建: 10%, 处理中: 50%, 协商中: 70%, 已解决: 100%
        return 50; // 模拟数据
    }

    /**
     * 获取争议相关文档
     */
    private List<DisputeDocument> getDisputeDocuments(Long disputeId) {
        // TODO: 获取争议相关的文档和证据
        List<DisputeDocument> documents = new ArrayList<>();

        // 模拟数据
        DisputeDocument doc = new DisputeDocument();
        doc.setDocumentName("对账差异说明.pdf");
        doc.setDocumentType("EVIDENCE");
        doc.setUploadTime(LocalDateTime.now().minusDays(2));
        documents.add(doc);

        return documents;
    }

    /**
     * 预估争议解决时间
     */
    private LocalDateTime estimateDisputeResolveTime(Long disputeId) {
        // TODO: 根据争议类型和复杂度预估解决时间
        // 简单争议: 3天, 复杂争议: 7天, 重大争议: 15天
        return LocalDateTime.now().plusDays(7); // 模拟数据
    }

    /**
     * 生成对账统计报表
     *
     * @param reportType  报表类型 (MONTHLY/QUARTERLY/YEARLY)
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param partnerType 合作伙伴类型 (SUPPLIER/CUSTOMER)
     * @return 对账统计报表
     */
    public Map<String, Object> generateStatementReport(String reportType, LocalDate startDate,
                                                       LocalDate endDate, String partnerType) {
        try {
            Map<String, Object> report = new HashMap<>();

            // 基础信息
            report.put("reportType", reportType);
            report.put("startDate", startDate);
            report.put("endDate", endDate);
            report.put("partnerType", partnerType);
            report.put("generateTime", LocalDateTime.now());

            // 对账单统计
            Map<String, Object> statementStats = generateStatementStatistics(startDate, endDate, partnerType);
            report.put("statementStats", statementStats);

            // 差异分析统计
            Map<String, Object> differenceStats = generateDifferenceStatistics(startDate, endDate, partnerType);
            report.put("differenceStats", differenceStats);

            // 争议处理统计
            Map<String, Object> disputeStats = generateDisputeStatistics(startDate, endDate, partnerType);
            report.put("disputeStats", disputeStats);

            // 趋势分析
            Map<String, Object> trendAnalysis = generateTrendAnalysis(startDate, endDate, partnerType);
            report.put("trendAnalysis", trendAnalysis);

            // 合作伙伴排名
            List<Map<String, Object>> partnerRanking = generatePartnerRanking(startDate, endDate, partnerType);
            report.put("partnerRanking", partnerRanking);

            log.info("对账统计报表生成成功 - 报表类型: {}, 期间: {} 至 {}", reportType, startDate, endDate);

            return report;
        } catch (Exception e) {
            log.error("对账统计报表生成失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("对账统计报表生成失败：" + e.getMessage());
        }
    }

    /**
     * 生成对账分析报表
     *
     * @param analysisType 分析类型 (EFFICIENCY/ACCURACY/RISK)
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 对账分析报表
     */
    public Map<String, Object> generateStatementAnalysisReport(String analysisType, LocalDate startDate, LocalDate endDate) {
        try {
            Map<String, Object> report = new HashMap<>();

            // 基础信息
            report.put("analysisType", analysisType);
            report.put("startDate", startDate);
            report.put("endDate", endDate);
            report.put("generateTime", LocalDateTime.now());

            // 根据分析类型生成不同内容
            switch (analysisType) {
                case "EFFICIENCY":
                    report.put("analysis", generateEfficiencyAnalysis(startDate, endDate));
                    break;
                case "ACCURACY":
                    report.put("analysis", generateAccuracyAnalysis(startDate, endDate));
                    break;
                case "RISK":
                    report.put("analysis", generateRiskAnalysis(startDate, endDate));
                    break;
                default:
                    throw new ServiceException("无效的分析类型：" + analysisType);
            }

            // 改进建议
            List<String> suggestions = generateImprovementSuggestions(analysisType, startDate, endDate);
            report.put("suggestions", suggestions);

            log.info("对账分析报表生成成功 - 分析类型: {}, 期间: {} 至 {}", analysisType, startDate, endDate);

            return report;
        } catch (Exception e) {
            log.error("对账分析报表生成失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("对账分析报表生成失败：" + e.getMessage());
        }
    }

    /**
     * 导出对账报表
     *
     * @param reportData   报表数据
     * @param exportFormat 导出格式 (PDF/EXCEL/CSV)
     * @return 导出结果
     */
    public Map<String, Object> exportStatementReport(Map<String, Object> reportData, String exportFormat) {
        try {
            Map<String, Object> exportResult = new HashMap<>();

            // 根据格式进行导出
            switch (exportFormat) {
                case "PDF":
                    exportResult = exportReportToPdf(reportData);
                    break;
                case "EXCEL":
                    exportResult = exportReportToExcel(reportData);
                    break;
                case "CSV":
                    exportResult = exportReportToCsv(reportData);
                    break;
                default:
                    throw new ServiceException("无效的导出格式：" + exportFormat);
            }

            exportResult.put("exportFormat", exportFormat);
            exportResult.put("exportTime", LocalDateTime.now());
            exportResult.put("operatorId", LoginHelper.getUserId());
            exportResult.put("operatorName", LoginHelper.getLoginUser().getNickname());

            log.info("对账报表导出成功 - 格式: {}", exportFormat);

            return exportResult;
        } catch (Exception e) {
            log.error("对账报表导出失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("对账报表导出失败：" + e.getMessage());
        }
    }

    /**
     * 生成对账单统计
     */
    private Map<String, Object> generateStatementStatistics(LocalDate startDate, LocalDate endDate, String partnerType) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现对账单统计逻辑
        stats.put("totalCount", 100);
        stats.put("confirmedCount", 85);
        stats.put("disputedCount", 10);
        stats.put("pendingCount", 5);
        stats.put("amount", new BigDecimal("1000000.00"));
        stats.put("confirmedAmount", new BigDecimal("850000.00"));
        stats.put("disputedAmount", new BigDecimal("100000.00"));
        stats.put("pendingAmount", new BigDecimal("50000.00"));
        return stats;
    }

    /**
     * 生成差异统计
     */
    private Map<String, Object> generateDifferenceStatistics(LocalDate startDate, LocalDate endDate, String partnerType) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现差异统计逻辑
        stats.put("totalDifferences", 25);
        stats.put("amountDifferences", 15);
        stats.put("timeDifferences", 8);
        stats.put("documentDifferences", 2);
        stats.put("totalDifferenceAmount", new BigDecimal("50000.00"));
        stats.put("averageDifferenceAmount", new BigDecimal("2000.00"));
        return stats;
    }

    /**
     * 生成争议统计
     */
    private Map<String, Object> generateDisputeStatistics(LocalDate startDate, LocalDate endDate, String partnerType) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现争议统计逻辑
        stats.put("totalDisputes", 10);
        stats.put("resolvedDisputes", 7);
        stats.put("pendingDisputes", 3);
        stats.put("averageResolveTime", 5.2); // 天
        stats.put("disputeResolutionRate", 0.7); // 70%
        return stats;
    }

    /**
     * 生成趋势分析
     */
    private Map<String, Object> generateTrendAnalysis(LocalDate startDate, LocalDate endDate, String partnerType) {
        Map<String, Object> analysis = new HashMap<>();
        // TODO: 实现趋势分析逻辑
        analysis.put("monthlyTrend", new ArrayList<>());
        analysis.put("accuracyTrend", new ArrayList<>());
        analysis.put("efficiencyTrend", new ArrayList<>());
        return analysis;
    }

    /**
     * 生成合作伙伴排名
     */
    private List<Map<String, Object>> generatePartnerRanking(LocalDate startDate, LocalDate endDate, String partnerType) {
        // TODO: 实现合作伙伴排名逻辑
        List<Map<String, Object>> ranking = new ArrayList<>();

        // 模拟数据
        Map<String, Object> partner1 = new HashMap<>();
        partner1.put("partnerId", 1L);
        partner1.put("partnerName", "供应商A");
        partner1.put("statementCount", 20);
        partner1.put("amount", new BigDecimal("200000.00"));
        partner1.put("accuracyRate", 0.95);
        ranking.add(partner1);

        return ranking;
    }

    /**
     * 生成效率分析
     */
    private Map<String, Object> generateEfficiencyAnalysis(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> analysis = new HashMap<>();
        // TODO: 实现效率分析逻辑
        analysis.put("averageProcessTime", 2.5); // 天
        analysis.put("automationRate", 0.8); // 80%
        analysis.put("manualInterventionRate", 0.2); // 20%
        analysis.put("throughput", 50); // 每天处理数量
        return analysis;
    }

    /**
     * 生成准确性分析
     */
    private Map<String, Object> generateAccuracyAnalysis(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> analysis = new HashMap<>();
        // TODO: 实现准确性分析逻辑
        analysis.put("overallAccuracy", 0.95); // 95%
        analysis.put("amountAccuracy", 0.98); // 98%
        analysis.put("timeAccuracy", 0.92); // 92%
        analysis.put("documentAccuracy", 0.96); // 96%
        return analysis;
    }

    /**
     * 生成风险分析
     */
    private Map<String, Object> generateRiskAnalysis(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> analysis = new HashMap<>();
        // TODO: 实现风险分析逻辑
        analysis.put("highRiskPartners", 3);
        analysis.put("mediumRiskPartners", 8);
        analysis.put("lowRiskPartners", 25);
        analysis.put("totalRiskAmount", new BigDecimal("150000.00"));
        analysis.put("riskMitigationRate", 0.85); // 85%
        return analysis;
    }

    /**
     * 生成改进建议
     */
    private List<String> generateImprovementSuggestions(String analysisType, LocalDate startDate, LocalDate endDate) {
        List<String> suggestions = new ArrayList<>();
        // TODO: 根据分析结果生成改进建议

        switch (analysisType) {
            case "EFFICIENCY":
                suggestions.add("增加自动化对账规则，减少人工干预");
                suggestions.add("优化对账流程，缩短处理时间");
                suggestions.add("建立对账模板，提高标准化程度");
                break;
            case "ACCURACY":
                suggestions.add("加强数据校验规则，提高数据准确性");
                suggestions.add("完善异常处理机制，及时发现问题");
                suggestions.add("定期培训操作人员，减少人为错误");
                break;
            case "RISK":
                suggestions.add("建立风险预警机制，及时识别高风险合作伙伴");
                suggestions.add("制定风险应对策略，降低业务风险");
                suggestions.add("加强合作伙伴信用管理，优化合作关系");
                break;
        }

        return suggestions;
    }

    /**
     * 导出报表为PDF
     */
    private Map<String, Object> exportReportToPdf(Map<String, Object> reportData) {
        // TODO: 实现PDF导出
        Map<String, Object> result = new HashMap<>();
        result.put("fileName", "statement_report_" + System.currentTimeMillis() + ".pdf");
        result.put("fileSize", "2048KB");
        result.put("downloadUrl", "/download/report/pdf/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 导出报表为Excel
     */
    private Map<String, Object> exportReportToExcel(Map<String, Object> reportData) {
        // TODO: 实现Excel导出
        Map<String, Object> result = new HashMap<>();
        result.put("fileName", "statement_report_" + System.currentTimeMillis() + ".xlsx");
        result.put("fileSize", "1024KB");
        result.put("downloadUrl", "/download/report/excel/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 导出报表为CSV
     */
    private Map<String, Object> exportReportToCsv(Map<String, Object> reportData) {
        // TODO: 实现CSV导出
        Map<String, Object> result = new HashMap<>();
        result.put("fileName", "statement_report_" + System.currentTimeMillis() + ".csv");
        result.put("fileSize", "512KB");
        result.put("downloadUrl", "/download/report/csv/" + System.currentTimeMillis());
        return result;
    }

    /**
     * 生成管理费用对账明细
     */
    private void generateManagementExpenseStatementItems(Long statementId, Long departmentId,
                                                         LocalDate startDate, LocalDate endDate) {
        try {
            log.info("开始生成管理费用对账明细 - 对账单ID: {}, 部门ID: {}, 期间: {} 至 {}",
                statementId, departmentId, startDate, endDate);

            // 查询期间内的管理费用应付
            List<InvoiceStatementItem> expenseInvoices = queryManagementExpenseInvoicesForStatement(
                departmentId, startDate, endDate);

            // 查询期间内的管理费用付款记录
            List<PaymentStatementItem> expensePayments = queryManagementExpensePaymentsForStatement(
                departmentId, startDate, endDate);

            // 生成应付对账明细
            generateInvoiceStatementItems(statementId, expenseInvoices);

            // 生成付款对账明细
            generatePaymentStatementItems(statementId, expensePayments);

            log.info("管理费用对账明细生成完成 - 对账单ID: {}, 应付数量: {}, 付款数量: {}",
                statementId, expenseInvoices.size(), expensePayments.size());

        } catch (Exception e) {
            log.error("生成管理费用对账明细失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("生成管理费用对账明细失败：" + e.getMessage());
        }
    }

    /**
     * 查询管理费用应付用于对账
     */
    private List<InvoiceStatementItem> queryManagementExpenseInvoicesForStatement(Long departmentId,
                                                                                  LocalDate startDate, LocalDate endDate) {
        FinApInvoiceBo bo = new FinApInvoiceBo();
        bo.setPayeeType(FinPayeeType.EMPLOYEE); // 管理费用类型
        // 注意：这里使用payeeId来存储部门ID，实际业务中可能需要调整
        bo.setPayeeId(departmentId);
        bo.setParams(new HashMap<>());
        bo.getParams().put("beginInvoiceDate", startDate);
        bo.getParams().put("endInvoiceDate", endDate);

        List<FinApInvoiceVo> invoices = finApInvoiceService.queryList(bo);

        return invoices.stream().map(invoice -> {
            InvoiceStatementItem item = new InvoiceStatementItem();
            item.setInvoiceId(invoice.getInvoiceId());
            item.setInvoiceCode(invoice.getInvoiceCode());
            item.setInvoiceDate(invoice.getInvoiceDate());
            item.setAmount(invoice.getAmount());
            item.setStatus(invoice.getInvoiceStatus().getValue());
            // 计算已核销金额
            item.setAppliedAmount(calculateInvoiceAppliedAmount(invoice.getInvoiceId()));
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 查询管理费用付款记录用于对账
     */
    private List<PaymentStatementItem> queryManagementExpensePaymentsForStatement(Long departmentId,
                                                                                  LocalDate startDate, LocalDate endDate) {
        FinApPaymentOrderBo bo = new FinApPaymentOrderBo();
        bo.setPayeeType(FinPayeeType.EMPLOYEE); // 管理费用类型
        bo.setPayeeId(departmentId);
        bo.setParams(new HashMap<>());
        bo.getParams().put("beginPaymentDate", startDate);
        bo.getParams().put("endPaymentDate", endDate);

        List<FinApPaymentOrderVo> payments = finApPaymentOrderService.queryList(bo);

        return payments.stream().map(payment -> {
            PaymentStatementItem item = new PaymentStatementItem();
            item.setPaymentId(payment.getPaymentId());
            item.setPaymentCode(payment.getPaymentCode());
            item.setPaymentDate(payment.getPaymentDate());
            item.setAmount(payment.getPaymentAmount());
            item.setStatus(payment.getPaymentStatus().getValue());
            item.setAppliedAmount(payment.getAppliedAmount());
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 计算应付已核销金额
     */
    private BigDecimal calculateInvoiceAppliedAmount(Long invoiceId) {
        // TODO: 实现应付已核销金额计算逻辑
        // 这里应该查询FinApPaymentInvoiceLink表来计算已核销金额
        return BigDecimal.ZERO;
    }

    /**
     * 获取对账统计报表（按收款方类型分类）
     *
     * @param payeeType  收款方类型
     * @param reportType 报表类型 (MONTHLY/QUARTERLY/YEARLY)
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 分类对账统计报表
     */
    public Map<String, Object> generateStatementReportByPayeeType(FinPayeeType payeeType, String reportType,
                                                                  LocalDate startDate, LocalDate endDate) {
        Map<String, Object> report = new HashMap<>();

        try {
            // 基础信息
            report.put("payeeType", payeeType);
            report.put("payeeTypeName", payeeType.getName());
            report.put("reportType", reportType);
            report.put("startDate", startDate);
            report.put("endDate", endDate);
            report.put("generateTime", LocalDateTime.now());

            // 根据收款方类型查询不同的统计数据
            switch (payeeType) {
                case SUPPLIER:
                    report.putAll(generateSupplierStatementReport(startDate, endDate));
                    break;
                case EMPLOYEE:
                    report.putAll(generateManagementExpenseStatementReport(startDate, endDate));
                    break;
                default:
                    throw new ServiceException("不支持的收款方类型：" + payeeType);
            }

            log.info("生成分类对账统计报表成功 - 类型: {}, 期间: {} 至 {}",
                payeeType.getName(), startDate, endDate);

        } catch (Exception e) {
            log.error("生成分类对账统计报表失败 - 类型: {}, 错误: {}", payeeType, e.getMessage(), e);
            throw new ServiceException("生成分类对账统计报表失败：" + e.getMessage());
        }

        return report;
    }

    /**
     * 生成供应商对账统计报表
     */
    private Map<String, Object> generateSupplierStatementReport(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> report = new HashMap<>();

        // TODO: 实现供应商对账统计逻辑
        report.put("totalSuppliers", 0);
        report.put("totalInvoiceAmount", BigDecimal.ZERO);
        report.put("totalPaymentAmount", BigDecimal.ZERO);
        report.put("totalOutstandingAmount", BigDecimal.ZERO);

        return report;
    }

    /**
     * 生成管理费用对账统计报表
     */
    private Map<String, Object> generateManagementExpenseStatementReport(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> report = new HashMap<>();

        // TODO: 实现管理费用对账统计逻辑
        report.put("totalDepartments", 0);
        report.put("totalExpenseAmount", BigDecimal.ZERO);
        report.put("totalPaymentAmount", BigDecimal.ZERO);
        report.put("totalPendingAmount", BigDecimal.ZERO);

        return report;
    }

    // 内部类定义
    public static class StatementDifference {
        private String differenceType;
        private String description;
        private BigDecimal expectedValue;
        private BigDecimal actualValue;
        private BigDecimal differenceAmount;
        private String severity; // HIGH/MEDIUM/LOW

        // getters and setters
        public String getDifferenceType() {
            return differenceType;
        }

        public void setDifferenceType(String differenceType) {
            this.differenceType = differenceType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public BigDecimal getExpectedValue() {
            return expectedValue;
        }

        public void setExpectedValue(BigDecimal expectedValue) {
            this.expectedValue = expectedValue;
        }

        public BigDecimal getActualValue() {
            return actualValue;
        }

        public void setActualValue(BigDecimal actualValue) {
            this.actualValue = actualValue;
        }

        public BigDecimal getDifferenceAmount() {
            return differenceAmount;
        }

        public void setDifferenceAmount(BigDecimal differenceAmount) {
            this.differenceAmount = differenceAmount;
        }

        public String getSeverity() {
            return severity;
        }

        public void setSeverity(String severity) {
            this.severity = severity;
        }
    }

    // 内部类定义 - 客户对账差异
    public static class CustomerStatementDifference {
        private String differenceType;
        private String description;
        private BigDecimal expectedValue;
        private BigDecimal actualValue;
        private BigDecimal differenceAmount;
        private String severity; // HIGH/MEDIUM/LOW
        private String businessImpact; // 业务影响
        private String suggestedAction; // 建议处理方式

        // getters and setters
        public String getDifferenceType() {
            return differenceType;
        }

        public void setDifferenceType(String differenceType) {
            this.differenceType = differenceType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public BigDecimal getExpectedValue() {
            return expectedValue;
        }

        public void setExpectedValue(BigDecimal expectedValue) {
            this.expectedValue = expectedValue;
        }

        public BigDecimal getActualValue() {
            return actualValue;
        }

        public void setActualValue(BigDecimal actualValue) {
            this.actualValue = actualValue;
        }

        public BigDecimal getDifferenceAmount() {
            return differenceAmount;
        }

        public void setDifferenceAmount(BigDecimal differenceAmount) {
            this.differenceAmount = differenceAmount;
        }

        public String getSeverity() {
            return severity;
        }

        public void setSeverity(String severity) {
            this.severity = severity;
        }

        public String getBusinessImpact() {
            return businessImpact;
        }

        public void setBusinessImpact(String businessImpact) {
            this.businessImpact = businessImpact;
        }

        public String getSuggestedAction() {
            return suggestedAction;
        }

        public void setSuggestedAction(String suggestedAction) {
            this.suggestedAction = suggestedAction;
        }
    }

    // 其他内部类定义
    private static class PurchaseOrderStatementItem {
    }

    private static class InboundStatementItem {
    }

    private static class InvoiceStatementItem {
        private Long invoiceId;
        private String invoiceCode;
        private LocalDate invoiceDate;
        private BigDecimal amount;
        private String status;
        private BigDecimal appliedAmount;

        // Getters and Setters
        public Long getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(Long invoiceId) {
            this.invoiceId = invoiceId;
        }

        public String getInvoiceCode() {
            return invoiceCode;
        }

        public void setInvoiceCode(String invoiceCode) {
            this.invoiceCode = invoiceCode;
        }

        public LocalDate getInvoiceDate() {
            return invoiceDate;
        }

        public void setInvoiceDate(LocalDate invoiceDate) {
            this.invoiceDate = invoiceDate;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public BigDecimal getAppliedAmount() {
            return appliedAmount;
        }

        public void setAppliedAmount(BigDecimal appliedAmount) {
            this.appliedAmount = appliedAmount;
        }
    }

    private static class PaymentStatementItem {
        private Long paymentId;
        private String paymentCode;
        private LocalDate paymentDate;
        private BigDecimal amount;
        private String status;
        private BigDecimal appliedAmount;

        // Getters and Setters
        public Long getPaymentId() {
            return paymentId;
        }

        public void setPaymentId(Long paymentId) {
            this.paymentId = paymentId;
        }

        public String getPaymentCode() {
            return paymentCode;
        }

        public void setPaymentCode(String paymentCode) {
            this.paymentCode = paymentCode;
        }

        public LocalDate getPaymentDate() {
            return paymentDate;
        }

        public void setPaymentDate(LocalDate paymentDate) {
            this.paymentDate = paymentDate;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public BigDecimal getAppliedAmount() {
            return appliedAmount;
        }

        public void setAppliedAmount(BigDecimal appliedAmount) {
            this.appliedAmount = appliedAmount;
        }
    }

    // 客户对账相关内部类
    private static class SaleOrderStatementItem {
    }

    private static class OutboundStatementItem {
    }

    private static class ReceivableStatementItem {
    }

    private static class ReceiptStatementItem {
    }

    // 对账单生成相关内部类
    public static class BatchStatementRequest {
        private List<StatementGenerateRequest> requests;

        public List<StatementGenerateRequest> getRequests() {
            return requests;
        }

        public void setRequests(List<StatementGenerateRequest> requests) {
            this.requests = requests;
        }
    }

    public static class StatementGenerateRequest {
        private Long partnerId;
        private String partnerName;
        private String statementType;
        private LocalDate startDate;
        private LocalDate endDate;

        public Long getPartnerId() {
            return partnerId;
        }

        public void setPartnerId(Long partnerId) {
            this.partnerId = partnerId;
        }

        public String getPartnerName() {
            return partnerName;
        }

        public void setPartnerName(String partnerName) {
            this.partnerName = partnerName;
        }

        public String getStatementType() {
            return statementType;
        }

        public void setStatementType(String statementType) {
            this.statementType = statementType;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public void setStartDate(LocalDate startDate) {
            this.startDate = startDate;
        }

        public LocalDate getEndDate() {
            return endDate;
        }

        public void setEndDate(LocalDate endDate) {
            this.endDate = endDate;
        }
    }

    // 争议处理相关内部类
    public static class DisputeTrackingInfo {
        private Long disputeId;
        private String disputeStatus;
        private String disputeType;
        private BigDecimal disputeAmount;
        private LocalDateTime createTime;
        private List<DisputeHandleRecord> handleHistory;
        private int progress;
        private List<DisputeDocument> documents;
        private LocalDateTime estimatedResolveTime;

        // getters and setters
        public Long getDisputeId() {
            return disputeId;
        }

        public void setDisputeId(Long disputeId) {
            this.disputeId = disputeId;
        }

        public String getDisputeStatus() {
            return disputeStatus;
        }

        public void setDisputeStatus(String disputeStatus) {
            this.disputeStatus = disputeStatus;
        }

        public String getDisputeType() {
            return disputeType;
        }

        public void setDisputeType(String disputeType) {
            this.disputeType = disputeType;
        }

        public BigDecimal getDisputeAmount() {
            return disputeAmount;
        }

        public void setDisputeAmount(BigDecimal disputeAmount) {
            this.disputeAmount = disputeAmount;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }

        public List<DisputeHandleRecord> getHandleHistory() {
            return handleHistory;
        }

        public void setHandleHistory(List<DisputeHandleRecord> handleHistory) {
            this.handleHistory = handleHistory;
        }

        public int getProgress() {
            return progress;
        }

        public void setProgress(int progress) {
            this.progress = progress;
        }

        public List<DisputeDocument> getDocuments() {
            return documents;
        }

        public void setDocuments(List<DisputeDocument> documents) {
            this.documents = documents;
        }

        public LocalDateTime getEstimatedResolveTime() {
            return estimatedResolveTime;
        }

        public void setEstimatedResolveTime(LocalDateTime estimatedResolveTime) {
            this.estimatedResolveTime = estimatedResolveTime;
        }
    }

    public static class DisputeHandleRequest {
        private Long disputeId;
        private String action;
        private String handleReason;
        private String handleEvidence;

        public Long getDisputeId() {
            return disputeId;
        }

        public void setDisputeId(Long disputeId) {
            this.disputeId = disputeId;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getHandleReason() {
            return handleReason;
        }

        public void setHandleReason(String handleReason) {
            this.handleReason = handleReason;
        }

        public String getHandleEvidence() {
            return handleEvidence;
        }

        public void setHandleEvidence(String handleEvidence) {
            this.handleEvidence = handleEvidence;
        }
    }

    private static class DisputeHandleRecord {
        private LocalDateTime handleTime;
        private String handleAction;
        private String handleReason;
        private String handlerName;

        public LocalDateTime getHandleTime() {
            return handleTime;
        }

        public void setHandleTime(LocalDateTime handleTime) {
            this.handleTime = handleTime;
        }

        public String getHandleAction() {
            return handleAction;
        }

        public void setHandleAction(String handleAction) {
            this.handleAction = handleAction;
        }

        public String getHandleReason() {
            return handleReason;
        }

        public void setHandleReason(String handleReason) {
            this.handleReason = handleReason;
        }

        public String getHandlerName() {
            return handlerName;
        }

        public void setHandlerName(String handlerName) {
            this.handlerName = handlerName;
        }
    }

    private static class DisputeDocument {
        private String documentName;
        private String documentType;
        private LocalDateTime uploadTime;

        public String getDocumentName() {
            return documentName;
        }

        public void setDocumentName(String documentName) {
            this.documentName = documentName;
        }

        public String getDocumentType() {
            return documentType;
        }

        public void setDocumentType(String documentType) {
            this.documentType = documentType;
        }

        public LocalDateTime getUploadTime() {
            return uploadTime;
        }

        public void setUploadTime(LocalDateTime uploadTime) {
            this.uploadTime = uploadTime;
        }
    }
}
