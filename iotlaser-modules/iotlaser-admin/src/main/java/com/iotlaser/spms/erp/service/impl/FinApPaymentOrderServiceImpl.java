package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import com.iotlaser.spms.erp.domain.FinApPaymentOrder;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinApPaymentOrderStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import com.iotlaser.spms.erp.mapper.FinApPaymentInvoiceLinkMapper;
import com.iotlaser.spms.erp.mapper.FinApPaymentOrderMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_FIN_AP_PAYMENT_ORDER_CODE;

/**
 * 付款单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApPaymentOrderServiceImpl implements IFinApPaymentOrderService {
    // =================================================================================================================
    // [DDD-TODO] 跨聚合调用优化 - 目标: 实现最终一致性，降低服务间耦合
    // 当前为保持业务流程的同步与完整性，暂时直接依赖其他聚合根的Service。
    // 理想架构应通过领域事件(Domain Event)或应用服务层(Application Service)进行解耦。
    // 例如：核销完成后，发布`PaymentAppliedEvent`，由总账上下文的监听器异步订阅并创建流水。
    // -----------------------------------------------------------------------------------------------------------------
    // 参考文档: docs/design/README_OVERVIEW.md
    // TODO: [REFACTOR] - [HIGH] - 将此处的服务直接依赖重构为领域事件模式。
    // =================================================================================================================
    private final FinApPaymentOrderMapper baseMapper;
    private final IFinApInvoiceService finApInvoiceService;
    private final FinApPaymentInvoiceLinkMapper linkMapper;
    private final ICompanyService companyService;
    private final Gen gen;

    /**
     * {@inheritDoc}
     */
    @Override
    public FinApPaymentOrderVo queryById(Long paymentId) {
        return baseMapper.selectVoById(paymentId);
    }

    /**
     * 分页查询付款单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单分页列表
     */
    @Override
    public TableDataInfo<FinApPaymentOrderVo> queryPageList(FinApPaymentOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApPaymentOrder> lqw = buildQueryWrapper(bo);
        Page<FinApPaymentOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的付款单列表
     *
     * @param bo 查询条件
     * @return 付款单列表
     */
    @Override
    public List<FinApPaymentOrderVo> queryList(FinApPaymentOrderBo bo) {
        LambdaQueryWrapper<FinApPaymentOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApPaymentOrder> buildQueryWrapper(FinApPaymentOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApPaymentOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApPaymentOrder::getPaymentId);
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentCode()), FinApPaymentOrder::getPaymentCode, bo.getPaymentCode());
        if (bo.getPayeeType() != null) {
            lqw.eq(FinApPaymentOrder::getPayeeType, bo.getPayeeType());
        }
        lqw.eq(bo.getPayeeId() != null, FinApPaymentOrder::getPayeeId, bo.getPayeeId());
        lqw.like(StringUtils.isNotBlank(bo.getPayeeName()), FinApPaymentOrder::getPayeeName, bo.getPayeeName());
        lqw.eq(bo.getPaymentAmount() != null, FinApPaymentOrder::getPaymentAmount, bo.getPaymentAmount());
        if (bo.getPaymentMethod() != null) {
            lqw.eq(FinApPaymentOrder::getPaymentMethod, bo.getPaymentMethod());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getBankSerialNumber()), FinApPaymentOrder::getBankSerialNumber, bo.getBankSerialNumber());
        lqw.eq(bo.getAppliedAmount() != null, FinApPaymentOrder::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getUnappliedAmount() != null, FinApPaymentOrder::getUnappliedAmount, bo.getUnappliedAmount());
        lqw.eq(bo.getPaymentDate() != null, FinApPaymentOrder::getPaymentDate, bo.getPaymentDate());
        if (bo.getPaymentStatus() != null) {
            lqw.eq(FinApPaymentOrder::getPaymentStatus, bo.getPaymentStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApPaymentOrder::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinApPaymentOrderVo insertByBo(FinApPaymentOrderBo bo) {
        final String currentUser = LoginHelper.getUsername();
        try {
            // 1. 初始化编码和日期
            if (StringUtils.isEmpty(bo.getPaymentCode())) {
                bo.setPaymentCode(gen.code(ERP_FIN_AP_PAYMENT_ORDER_CODE));
            }
            if (bo.getPaymentDate() == null) {
                bo.setPaymentDate(LocalDate.now());
            }
            // 2. 填充冗余字段
            fillRedundantFields(bo);

            // 3. 转换并校验
            FinApPaymentOrder add = MapstructUtils.convert(bo, FinApPaymentOrder.class);
            validEntityBeforeSave(add);

            // 4. 初始化金额和状态
            add.setPaymentStatus(FinApPaymentOrderStatus.DRAFT);
            add.setAppliedAmount(BigDecimal.ZERO);
            add.setUnappliedAmount(add.getPaymentAmount());

            // 5. 插入数据库
            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                log.error("【付款单】[新增] - 失败. 数据库插入返回false. 操作人: {}, 数据: {}", currentUser, add);
                throw new ServiceException("创建付款单失败，数据库操作异常");
            }

            log.info("【付款单】[新增] - 成功. 操作人: {}, 单号: {}, ID: {}", currentUser, add.getPaymentCode(), add.getPaymentId());
            return MapstructUtils.convert(add, FinApPaymentOrderVo.class);
        } catch (ServiceException se) {
            log.warn("【付款单】[新增] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[新增] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException(String.format("新增付款单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinApPaymentOrderVo updateByBo(FinApPaymentOrderBo bo) {
        final String currentUser = LoginHelper.getUsername();
        try {
            // 1. 填充冗余字段
            fillRedundantFields(bo);

            // 2. 转换并校验
            FinApPaymentOrder update = MapstructUtils.convert(bo, FinApPaymentOrder.class);
            validEntityBeforeSave(update);

            // 3. 如果付款金额有变，重新计算未核销金额
            FinApPaymentOrder oldEntity = baseMapper.selectById(bo.getPaymentId());
            if (update.getPaymentAmount() != null && update.getPaymentAmount().compareTo(oldEntity.getPaymentAmount()) != 0) {
                BigDecimal newUnappliedAmount = update.getPaymentAmount().subtract(oldEntity.getAppliedAmount());
                if (newUnappliedAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ServiceException("修改失败：付款金额不能小于已核销金额 [" + oldEntity.getAppliedAmount() + "]");
                }
                update.setUnappliedAmount(newUnappliedAmount);
            }

            // 4. 更新数据库
            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                log.warn("【付款单】[修改] - 警告. 数据库更新未生效，记录可能已被删除。操作人: {}, ID: {}", currentUser, update.getPaymentId());
                throw new ServiceException("修改失败：更新付款单失败，请刷新后重试");
            }

            log.info("【付款单】[修改] - 成功. 操作人: {}, ID: {}", currentUser, update.getPaymentId());
            return queryById(update.getPaymentId());
        } catch (ServiceException se) {
            log.warn("【付款单】[修改] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getPaymentId(), se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[修改] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getPaymentId(), e.getMessage(), e);
            throw new ServiceException(String.format("修改付款单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApPaymentOrder entity) {
        // 业务规则1: 校验付款单编号唯一性
        if (StringUtils.isNotBlank(entity.getPaymentCode())) {
            LambdaQueryWrapper<FinApPaymentOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinApPaymentOrder::getPaymentCode, entity.getPaymentCode());
            if (entity.getPaymentId() != null) {
                wrapper.ne(FinApPaymentOrder::getPaymentId, entity.getPaymentId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("操作失败：付款单号 [" + entity.getPaymentCode() + "] 已被使用，请修改");
            }
        }

        // 业务规则2: 校验状态流转
        if (entity.getPaymentId() != null && entity.getPaymentStatus() != null) {
            FinApPaymentOrder existing = baseMapper.selectById(entity.getPaymentId());
            if (existing != null && !isValidStatusTransition(existing.getPaymentStatus(), entity.getPaymentStatus())) {
                throw new ServiceException("操作失败：付款单状态从【" + existing.getPaymentStatus().getDesc() + "】到【" + entity.getPaymentStatus().getDesc() + "】的流转不被允许");
            }
            // 只有草稿状态才能修改
            if (existing != null && existing.getPaymentStatus() != FinApPaymentOrderStatus.DRAFT) {
                throw new ServiceException("操作失败：只有草稿状态的付款单才能修改");
            }
        }

        // 业务规则3: 校验收款方
        if (entity.getPayeeType() == null) {
            throw new ServiceException("操作失败：收款方类型不能为空");
        }
        if (entity.getPayeeType() == FinPayeeType.SUPPLIER) {
            if (entity.getPayeeId() == null) {
                throw new ServiceException("操作失败：收款方为供应商时，必须指定供应商ID");
            }
            CompanyVo supplier = companyService.queryById(entity.getPayeeId());
            if (supplier == null) {
                throw new ServiceException("操作失败：ID为 [" + entity.getPayeeId() + "] 的供应商不存在");
            }
        }

        // 业务规则4: 校验付款金额
        if (entity.getPaymentAmount() == null || entity.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("操作失败：付款金额必须大于0");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        final String currentUser = LoginHelper.getUsername();
        try {
            if (isValid) {
                List<FinApPaymentOrder> payments = baseMapper.selectByIds(ids);
                for (FinApPaymentOrder payment : payments) {
                    if (payment.getPaymentStatus() != FinApPaymentOrderStatus.DRAFT) {
                        throw new ServiceException("删除失败：付款单 [" + payment.getPaymentCode() + "] 状态为“" + payment.getPaymentStatus().getDesc() + "”，仅草稿状态可删除");
                    }
                    if (payment.getAppliedAmount() != null && payment.getAppliedAmount().compareTo(BigDecimal.ZERO) > 0) {
                        throw new ServiceException("删除失败：付款单 [" + payment.getPaymentCode() + "] 已存在核销记录，不允许删除");
                    }
                }
            } else {
                log.warn("【付款单】[删除] - 警告. 跳过业务校验直接删除数据。操作人: {}", currentUser);
            }

            // 删除主表
            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("【付款单】[批量删除] - 成功. 操作人: {}, 删除ID列表: {}", currentUser, ids);
            } else {
                log.warn("【付款单】[批量删除] - 警告. 数据库操作未影响任何行。操作人: {}, 尝试删除的ID: {}", currentUser, ids);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【付款单】[删除] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[删除] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException(String.format("删除付款单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * 创建付款申请
     *
     * @param supplierId    供应商ID
     * @param supplierCode  供应商编码
     * @param supplierName  供应商名称
     * @param paymentAmount 付款金额
     * @param paymentMethod 付款方式
     * @param applicantId   申请人ID
     * @param applicantName 申请人姓名
     * @param remark        备注
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createPaymentApplication(Long supplierId, String supplierCode, String supplierName,
                                            BigDecimal paymentAmount, FinPaymentMethod paymentMethod,
                                            Long applicantId, String applicantName, String remark) {
        try {
            FinApPaymentOrder paymentOrder = new FinApPaymentOrder();

            // 生成付款单编号
            paymentOrder.setPaymentCode(gen.code(ERP_FIN_AP_PAYMENT_ORDER_CODE));
            paymentOrder.setSummary("付款申请-" + supplierName);

            // 供应商信息
            paymentOrder.setPayeeType(FinPayeeType.SUPPLIER);
            paymentOrder.setPayeeId(supplierId);
            paymentOrder.setPayeeName(supplierName);

            // 付款信息
            paymentOrder.setPaymentAmount(paymentAmount);
            paymentOrder.setPaymentMethod(paymentMethod);
            paymentOrder.setAppliedAmount(BigDecimal.ZERO);
            paymentOrder.setUnappliedAmount(paymentAmount);

            // 状态信息
            paymentOrder.setPaymentStatus(FinApPaymentOrderStatus.DRAFT);
            paymentOrder.setRemark(remark);

            boolean result = baseMapper.insert(paymentOrder) > 0;

            if (result) {
                log.info("付款申请创建成功 - 供应商: {}, 金额: {}, 申请人: {}",
                    supplierName, paymentAmount, applicantName);
            }

            return result;
        } catch (Exception e) {
            log.error("付款申请创建失败 - 供应商: {}, 错误: {}", supplierName, e.getMessage(), e);
            throw new ServiceException("付款申请创建失败：" + e.getMessage());
        }
    }


    /**
     * 付款单核销到发票
     *
     * @param paymentId     付款单ID
     * @param invoiceId     发票ID
     * @param appliedAmount 核销金额
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 是否核销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyToInvoice(Long paymentId, Long invoiceId, BigDecimal appliedAmount,
                                  Long operatorId, String operatorName) {
        try {
            // 1. 校验核销金额
            if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销失败：核销金额必须大于0");
            }

            // 2. 锁定并校验付款单
            FinApPaymentOrder paymentOrder = baseMapper.selectById(paymentId);
            if (paymentOrder == null) {
                throw new ServiceException("核销失败：ID为 [" + paymentId + "] 的付款单不存在");
            }
            if (paymentOrder.getUnappliedAmount().compareTo(appliedAmount) < 0) {
                throw new ServiceException("核销失败：核销金额 [" + appliedAmount + "] 不能大于付款单未核销金额 [" + paymentOrder.getUnappliedAmount() + "]");
            }
            if (paymentOrder.getPaymentStatus() != FinApPaymentOrderStatus.UNAPPLIED && paymentOrder.getPaymentStatus() != FinApPaymentOrderStatus.PARTIALLY_APPLIED) {
                throw new ServiceException("核销失败：付款单 [" + paymentOrder.getPaymentCode() + "] 当前状态 “" + paymentOrder.getPaymentStatus().getDesc() + "” 不允许核销");
            }

            // 3. 锁定并校验应付单
            FinApInvoiceVo invoice = finApInvoiceService.queryById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("核销失败：ID为 [" + invoiceId + "] 的应付单不存在");
            }
            BigDecimal invoiceUnpaidAmount = invoice.getAmount().subtract(linkMapper.getAppliedAmountByInvoiceId(invoiceId));
            if (appliedAmount.compareTo(invoiceUnpaidAmount) > 0) {
                throw new ServiceException("核销失败：核销金额 [" + appliedAmount + "] 不能大于应付单未付金额 [" + invoiceUnpaidAmount + "]");
            }
            if (invoice.getInvoiceStatus() != FinApInvoiceStatus.UNPAID && invoice.getInvoiceStatus() != FinApInvoiceStatus.PARTIALLY_PAID) {
                throw new ServiceException("核销失败：应付单 [" + invoice.getInvoiceCode() + "] 当前状态 “" + invoice.getInvoiceStatus().getDesc() + "” 不允许核销");
            }

            // 4. 创建核销关联记录
            createPaymentInvoiceLink(paymentOrder, invoice, appliedAmount, operatorId, operatorName);
            log.info("【付款单】[核销] - 创建核销关联记录成功. 付款单ID: {}, 应付单ID: {}", paymentId, invoiceId);

            // 5. 更新付款单金额与状态
            updatePaymentOrderStatusByAmount(paymentId);

            // 6. 更新应付单金额与状态
            finApInvoiceService.updateInvoicePaymentStatus(invoiceId);

            return true;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("核销操作时发生未知系统错误，请联系管理员");
        }
    }

    /**
     * 创建付款发票核销记录
     */
    private void createPaymentInvoiceLink(FinApPaymentOrder payment, FinApInvoiceVo invoice,
                                          BigDecimal appliedAmount, Long appliedById, String appliedByName) {
        FinApPaymentInvoiceLink link = new FinApPaymentInvoiceLink();
        link.setPaymentId(payment.getPaymentId());
        link.setInvoiceId(invoice.getInvoiceId());
        link.setAppliedAmount(appliedAmount);
        link.setCancellationDate(LocalDate.now());
        if (linkMapper.insert(link) <= 0) {
            throw new ServiceException("创建付款-应付核销关联记录失败");
        }
    }

    /**
     * 取消付款单
     *
     * @param paymentId    付款单ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelPayment(Long paymentId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            if (FinApPaymentOrderStatus.FULLY_APPLIED == payment.getPaymentStatus() || FinApPaymentOrderStatus.CANCELLED == payment.getPaymentStatus()) {
                throw new ServiceException("付款单状态不允许取消");
            }

            payment.setPaymentStatus(FinApPaymentOrderStatus.CANCELLED);
            payment.setRemark(cancelReason);
            boolean result = baseMapper.updateById(payment) > 0;

            if (result) {
                log.info("付款单取消成功 - 付款单: {}, 取消人: {}, 原因: {}",
                    payment.getPaymentCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("付款单取消失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("付款单取消失败：" + e.getMessage());
        }
    }

    /**
     * 撤销付款核销
     *
     * @param linkId       核销记录ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelWriteoff(Long linkId, Long operatorId, String operatorName) {
        final String currentUser = "SYSTEM"; // TODO: Replace with actual user
        log.info("【付款单】[反核销] - 开始. 操作人: {}, 核销记录ID: {}", currentUser, linkId);
        try {
            FinApPaymentInvoiceLink link = linkMapper.selectById(linkId);
            if (link == null) {
                throw new ServiceException("反核销失败：ID为 [" + linkId + "] 的核销记录不存在");
            }

            boolean deleted = linkMapper.deleteByIds(List.of(linkId)) > 0;
            if (!deleted) {
                throw new ServiceException("反核销失败：删除核销记录时发生错误");
            }
            log.info("【付款单】[反核销] - 删除核销记录成功. ID: {}", linkId);

            // 重新计算并更新付款单的金额与状态
            updatePaymentOrderStatusByAmount(link.getPaymentId());

            // 重新计算并更新应付单的金额与状态
            finApInvoiceService.updateInvoicePaymentStatus(link.getInvoiceId());

            log.info("【付款单】[反核销] - 成功. 操作人: {}, 原付款单ID: {}, 原应付单ID: {}", currentUser, link.getPaymentId(), link.getInvoiceId());
            return true;
        } catch (ServiceException se) {
            log.warn("【付款单】[反核销] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[反核销] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException("反核销操作时发生未知系统错误，请联系管理员");
        }
    }

    // ==================== 付款核销核心逻辑 ====================

    /**
     * TODO: [付款核销逻辑] - 参考文档 docs/design/README_FINANCE.md
     * 将一笔付款单核销到一张或多张应付发票。
     *
     * @param paymentId 要进行核销的付款单ID
     * @param links     核销明细，包含要核销的发票ID和本次核销的金额
     * @return 是否核销成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean applyPaymentToInvoices(Long paymentId, List<FinApPaymentInvoiceLink> links) {
        // 查询付款单信息，并校验其状态是否为“已审核”或“部分核销”。
        FinApPaymentOrder paymentOrder = baseMapper.selectById(paymentId);
        if (paymentOrder == null) {
            throw new ServiceException("付款单状态不存在，无法核销");
        }
        if (!(FinApPaymentOrderStatus.UNAPPLIED == paymentOrder.getPaymentStatus() || FinApPaymentOrderStatus.PARTIALLY_APPLIED == paymentOrder.getPaymentStatus())) {
            throw new ServiceException("付款单状态不正确，无法核销");
        }

        // 校验本次要核销的总金额是否超过付款单的未核销金额。
        BigDecimal totalWriteOffAmount = links.stream().map(FinApPaymentInvoiceLink::getAppliedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalWriteOffAmount.compareTo(paymentOrder.getUnappliedAmount()) > 0) {
            throw new ServiceException("核销总金额超过付款单未核销金额");
        }

        List<FinApPaymentInvoiceLink> updates = new ArrayList<>();
        //遍历核销明细列表
        for (FinApPaymentInvoiceLink item : links) {
            // 查询应付发票信息，并校验其状态是否允许被核销。
            FinApInvoiceVo invoice = finApInvoiceService.queryById(item.getInvoiceId());
            // TODO...

            // 创建核销关联记录 (FinApPaymentInvoiceLink)。
            FinApPaymentInvoiceLink link = new FinApPaymentInvoiceLink();
            link.setPaymentId(paymentId);
            link.setInvoiceId(item.getInvoiceId());
            link.setAppliedAmount(item.getAppliedAmount());
            link.setCancellationDate(LocalDate.now());
            updates.addAll(links);
            // 更新发票的已付金额和状态 (PARTIALLY_PAID, FULLY_PAID)。
            finApInvoiceService.updateInvoicePaymentStatus(item.getInvoiceId(), item.getAppliedAmount());
        }

        //更新付款单的已核销金额和状态 (PARTIALLY_APPLIED, FULLY_APPLIED)。
        updatePaymentOrderStatusByAmount(paymentId, totalWriteOffAmount);

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createAccountLedger(Long orderId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【付款单】[创建总账流水] - 开始. 操作人: {}, 付款单ID: {}", currentUser, orderId);

        try {
            FinApPaymentOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("操作失败：付款单不存在");
            }
            // 只有完全核销的单据才能生成总账流水
            if (order.getPaymentStatus() != FinApPaymentOrderStatus.FULLY_APPLIED) {
                throw new ServiceException("操作失败：只有完全核销的付款单才能生成总账流水");
            }

            // TODO: 调用 IFinAccountLedgerService 服务创建流水
            // finAccountLedgerService.createFromReceiptOrder(order);
//            if (!result) {
//                throw new ServiceException("创建失败");
//            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Boolean updatePaymentOrderStatusByAmount(Long paymentId) {
        BigDecimal appliedAmount = linkMapper.getAppliedAmountByPaymentIdId(paymentId);
        return updatePaymentOrderStatusByAmount(paymentId, appliedAmount);
    }

    /**
     * 更新付款单的核销状态
     * <p>
     * 此方法在付款核销或反核销后被调用，用于重新计算并更新付款单的已核销/未核销金额和业务状态。
     *
     * @param paymentId     付款单ID
     * @param appliedAmount 付款单已核销金额
     * @return 是否更新成功
     */
    public Boolean updatePaymentOrderStatusByAmount(Long paymentId, BigDecimal appliedAmount) {
        FinApPaymentOrder order = baseMapper.selectById(paymentId);
        if (order == null) {
            log.error("【付款单】[状态更新] - 失败. 未找到ID为 {} 的付款单", paymentId);
            throw new ServiceException("更新付款单状态失败：单据不存在");
        }

        BigDecimal totalAmount = order.getPaymentAmount();
        FinApPaymentOrderStatus newStatus;

        // 使用 compareTo 进行精确比较
        if (appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            newStatus = FinApPaymentOrderStatus.UNAPPLIED;
        } else if (appliedAmount.compareTo(totalAmount) >= 0) {
            newStatus = FinApPaymentOrderStatus.FULLY_APPLIED;
        } else {
            newStatus = FinApPaymentOrderStatus.PARTIALLY_APPLIED;
        }

        // 仅当状态或金额发生变化时才更新
        if (order.getPaymentStatus() != newStatus || order.getAppliedAmount().compareTo(appliedAmount) != 0) {
            FinApPaymentOrder update = new FinApPaymentOrder();
            update.setPaymentId(paymentId);
            update.setAppliedAmount(appliedAmount);
            update.setUnappliedAmount(totalAmount.subtract(appliedAmount));
            update.setPaymentStatus(newStatus);
            if (baseMapper.updateById(update) > 0) {
                log.info("【付款单】[状态更新] - 成功. ID: {}, 状态由 [{}] 更新为 [{}], 已核销金额: {}",
                    paymentId, order.getPaymentStatus().getDesc(), newStatus.getDesc(), appliedAmount);
                // 如果变为完全核销，并且之前不是完全核销状态，则触发创建总账流水
                if (newStatus == FinApPaymentOrderStatus.FULLY_APPLIED && order.getPaymentStatus() != FinApPaymentOrderStatus.FULLY_APPLIED) {
                    log.info("【付款单】[状态更新] - 单据已完全核销，准备触发总账流水生成。ID: {}", paymentId);
                    // [DDD-TODO] 此处应替换为发布领域事件
                    createAccountLedger(paymentId);
                }
            } else {
                log.error("【付款单】[状态更新] - 失败. 数据库更新未生效. ID: {}", paymentId);
                throw new ServiceException("更新付款单状态时数据库操作失败");
            }
        }
        return true;
    }

    /**
     * 确认付款单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmPayment(Long paymentId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【付款单】[确认] - 开始. 操作人: {}, ID: {}", currentUser, paymentId);
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("确认失败：ID为 [" + paymentId + "] 的付款单不存在");
            }
            if (payment.getPaymentStatus() != FinApPaymentOrderStatus.DRAFT) {
                throw new ServiceException("确认失败：付款单 [" + payment.getPaymentCode() + "] 状态为“" + payment.getPaymentStatus().getDesc() + "”，仅草稿状态可确认");
            }

            FinApPaymentOrder update = new FinApPaymentOrder();
            update.setPaymentId(paymentId);
            update.setPaymentStatus(FinApPaymentOrderStatus.UNAPPLIED); // 状态变为待核销
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【付款单】[确认] - 成功. 操作人: {}, 单号: {}", currentUser, payment.getPaymentCode());
            } else {
                log.warn("【付款单】[确认] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, paymentId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【付款单】[确认] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, paymentId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[确认] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, paymentId, e.getMessage(), e);
            throw new ServiceException(String.format("确认付款单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * 取消付款单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelPayment(Long paymentId, String reason) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【付款单】[取消] - 开始. 操作人: {}, ID: {}, 原因: {}", currentUser, paymentId, reason);
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("取消失败：ID为 [" + paymentId + "] 的付款单不存在");
            }

            // 只有草稿和待核销状态可以取消
            if (payment.getPaymentStatus() != FinApPaymentOrderStatus.DRAFT && payment.getPaymentStatus() != FinApPaymentOrderStatus.UNAPPLIED) {
                throw new ServiceException("取消失败：付款单 [" + payment.getPaymentCode() + "] 状态为“" + payment.getPaymentStatus().getDesc() + "”，无法取消");
            }

            // 如果已存在核销记录，则不能取消
            if (payment.getAppliedAmount() != null && payment.getAppliedAmount().compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("取消失败：付款单 [" + payment.getPaymentCode() + "] 已存在核销记录，请先反核销");
            }

            FinApPaymentOrder update = new FinApPaymentOrder();
            update.setPaymentId(paymentId);
            update.setPaymentStatus(FinApPaymentOrderStatus.CANCELLED);
            if (StringUtils.isNotBlank(reason)) {
                update.setRemark(StringUtils.isNotBlank(payment.getRemark()) ? payment.getRemark() + " | 取消原因: " + reason : "取消原因: " + reason);
            }
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【付款单】[取消] - 成功. 操作人: {}, 单号: {}", currentUser, payment.getPaymentCode());
            } else {
                log.warn("【付款单】[取消] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, paymentId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【付款单】[取消] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, paymentId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【付款单】[取消] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, paymentId, e.getMessage(), e);
            throw new ServiceException(String.format("取消付款单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    private void fillRedundantFields(FinApPaymentOrderBo bo) {
        // 填充供应商名称
        if (bo.getPayeeType() == FinPayeeType.SUPPLIER && bo.getPayeeId() != null && StringUtils.isEmpty(bo.getPayeeName())) {
            CompanyVo supplier = companyService.queryById(bo.getPayeeId());
            if (supplier != null) {
                bo.setPayeeName(supplier.getCompanyName());
            }
        }

    }

    private boolean isValidStatusTransition(FinApPaymentOrderStatus fromStatus, FinApPaymentOrderStatus toStatus) {
        if (fromStatus == null || toStatus == null || fromStatus == toStatus) {
            return true;
        }
        return switch (fromStatus) {
            case DRAFT -> toStatus == FinApPaymentOrderStatus.UNAPPLIED || toStatus == FinApPaymentOrderStatus.CANCELLED;
            case UNAPPLIED -> toStatus == FinApPaymentOrderStatus.PARTIALLY_APPLIED || toStatus == FinApPaymentOrderStatus.FULLY_APPLIED || toStatus == FinApPaymentOrderStatus.CANCELLED;
            case PARTIALLY_APPLIED -> toStatus == FinApPaymentOrderStatus.FULLY_APPLIED || toStatus == FinApPaymentOrderStatus.CANCELLED;
            case FULLY_APPLIED, CANCELLED -> false;
            default -> false;
        };
    }
}
