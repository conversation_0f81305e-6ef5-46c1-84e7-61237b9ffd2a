package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinApPaymentOrder;
import com.iotlaser.spms.erp.enums.FinAccountType;
import com.iotlaser.spms.erp.enums.FinApPaymentOrderStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 付款单业务对象 erp_fin_ap_payment_order
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApPaymentOrder.class, reverseConvertGenerate = false)
public class FinApPaymentOrderBo extends BaseEntity {

    /**
     * 付款ID
     */
    private Long paymentId;

    /**
     * 付款编码
     */
    private String paymentCode;

    /**
     * 收款方类型
     */
    private FinPayeeType payeeType;

    /**
     * 收款方ID
     */
    private Long payeeId;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private FinAccountType accountType;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 付款方式
     */
    private FinPaymentMethod paymentMethod;

    /**
     * 付款时间
     */
    private LocalDate paymentDate;

    /**
     * 银行交易流水号
     */
    @NotBlank(message = "银行交易流水号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 付款状态
     */
    @NotBlank(message = "付款状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private FinApPaymentOrderStatus paymentStatus;

    /**
     * 摘要
     */
    @NotBlank(message = "摘要不能为空", groups = {AddGroup.class, EditGroup.class})
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
