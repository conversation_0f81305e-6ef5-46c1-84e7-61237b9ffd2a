package com.iotlaser.spms.common.domain.bo;

import com.iotlaser.spms.common.domain.SourceItemBatch;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SourceItemBatch.class, reverseConvertGenerate = false)
public class SourceItemBatchBo extends BaseEntity {

    /**
     * 批次ID
     */
    @NotNull(message = "批次ID不能为空", groups = {EditGroup.class})
    private Long batchId;

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long itemId;

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long mainId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
