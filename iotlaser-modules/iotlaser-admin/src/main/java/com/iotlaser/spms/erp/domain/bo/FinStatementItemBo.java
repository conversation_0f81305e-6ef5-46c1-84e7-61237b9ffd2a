package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinStatementItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 对账单明细业务对象 erp_fin_statement_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinStatementItem.class, reverseConvertGenerate = false)
public class FinStatementItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 对账ID
     */
    @NotNull(message = "对账ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long statementId;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotBlank(message = "源头类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotBlank(message = "上游类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @NotNull(message = "上游明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceItemId;

    /**
     * 组号
     */
    private String groupId;

    /**
     * 组内余额
     */
    private BigDecimal groupBalance;

    /**
     * 借方金额 (应收增加)
     */
    private BigDecimal amountDebit;

    /**
     * 贷方金额 (应收减少)
     */
    private BigDecimal amountCredit;

    /**
     * 对账标记
     */
    private String markFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
