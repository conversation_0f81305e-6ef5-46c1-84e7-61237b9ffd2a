package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinApInvoiceItem;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceItemVo;
import com.iotlaser.spms.erp.mapper.FinApInvoiceItemMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 应付单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApInvoiceItemServiceImpl implements IFinApInvoiceItemService {

    private final FinApInvoiceItemMapper baseMapper;

    /**
     * 查询应付明细
     *
     * @param itemId 主键
     * @return 应付明细
     */
    @Override
    public FinApInvoiceItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询应付明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应付明细分页列表
     */
    @Override
    public TableDataInfo<FinApInvoiceItemVo> queryPageList(FinApInvoiceItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApInvoiceItem> lqw = buildQueryWrapper(bo);
        Page<FinApInvoiceItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应付明细列表
     *
     * @param bo 查询条件
     * @return 应付明细列表
     */
    @Override
    public List<FinApInvoiceItemVo> queryList(FinApInvoiceItemBo bo) {
        LambdaQueryWrapper<FinApInvoiceItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApInvoiceItem> buildQueryWrapper(FinApInvoiceItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApInvoiceItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApInvoiceItem::getItemId);
        lqw.eq(bo.getInvoiceId() != null, FinApInvoiceItem::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getSourceId() != null, FinApInvoiceItem::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), FinApInvoiceItem::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(FinApInvoiceItem::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, FinApInvoiceItem::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), FinApInvoiceItem::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(FinApInvoiceItem::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getDirectSourceItemId() != null, FinApInvoiceItem::getDirectSourceItemId, bo.getDirectSourceItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), FinApInvoiceItem::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(bo.getProductId() != null, FinApInvoiceItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), FinApInvoiceItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), FinApInvoiceItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, FinApInvoiceItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), FinApInvoiceItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), FinApInvoiceItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getQuantity() != null, FinApInvoiceItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPriceExclusiveTax() != null, FinApInvoiceItem::getPriceExclusiveTax, bo.getPriceExclusiveTax());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinApInvoiceItem::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxRate() != null, FinApInvoiceItem::getTaxRate, bo.getTaxRate());
        lqw.eq(bo.getTaxAmount() != null, FinApInvoiceItem::getTaxAmount, bo.getTaxAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApInvoiceItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增应付明细
     *
     * @param bo 应付明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinApInvoiceItemBo bo) {
        FinApInvoiceItem add = MapstructUtils.convert(bo, FinApInvoiceItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改应付明细
     *
     * @param bo 应付明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinApInvoiceItemBo bo) {
        FinApInvoiceItem update = MapstructUtils.convert(bo, FinApInvoiceItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApInvoiceItem entity) {
        // 数据校验：检查唯一约束
        validateUniqueConstraint(entity);

        // 数据校验：检查必填字段
        validateRequiredFields(entity);
    }

    /**
     * 校验并批量删除应付明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 业务校验：检查是否可以删除
            validateBeforeDelete(ids);
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除应付明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除应付明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除应付明细失败：" + e.getMessage());
        }
    }

    /**
     * 校验唯一约束
     *
     * @param entity 实体对象
     */
    private void validateUniqueConstraint(FinApInvoiceItem entity) {
        // 检查同一发票下的产品是否重复
        LambdaQueryWrapper<FinApInvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApInvoiceItem::getInvoiceId, entity.getInvoiceId());
        wrapper.eq(FinApInvoiceItem::getProductId, entity.getProductId());

        // 如果是更新操作，排除当前记录
        if (entity.getItemId() != null) {
            wrapper.ne(FinApInvoiceItem::getItemId, entity.getItemId());
        }

        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("该发票中已存在相同产品的明细记录");
        }
    }

    /**
     * 校验必填字段
     *
     * @param entity 实体对象
     */
    private void validateRequiredFields(FinApInvoiceItem entity) {
        if (entity.getInvoiceId() == null) {
            throw new ServiceException("发票ID不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品ID不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0");
        }
        if (entity.getPrice() == null || entity.getPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("单价不能为负数");
        }
    }

    /**
     * 删除前校验
     *
     * @param ids 待删除的ID集合
     */
    private void validateBeforeDelete(Collection<Long> ids) {
        // 检查发票明细是否可以删除
        List<FinApInvoiceItem> items = baseMapper.selectByIds(ids);
        for (FinApInvoiceItem item : items) {
            // 检查主表状态，只有草稿状态的发票明细才能删除
            // TODO: 添加finApInvoiceService依赖注入
            // FinApInvoice invoice = finApInvoiceService.queryById(item.getInvoiceId());
            // if (invoice != null && !"DRAFT".equals(invoice.getInvoiceStatus()) && !"PENDING".equals(invoice.getInvoiceStatus())) {
            //    throw new ServiceException("发票明细所属发票【" + invoice.getInvoiceName() +
            //        "】状态为【" + invoice.getInvoiceStatus() + "】，不允许删除明细");
            // }

            log.info("删除应付明细校验通过：产品【{}】", item.getProductName());
        }
    }

    /**
     * 根据发票ID获取明细列表
     *
     * @param invoiceId 发票ID
     * @return 明细列表
     */
    @Override
    public List<FinApInvoiceItem> getItemsByInvoiceId(Long invoiceId) {
        LambdaQueryWrapper<FinApInvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApInvoiceItem::getInvoiceId, invoiceId);
        wrapper.orderByAsc(FinApInvoiceItem::getItemId);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 检查是否存在发票明细
     *
     * @param invoiceId 发票ID
     * @return 是否存在明细
     */
    @Override
    public Boolean existsByInvoiceId(Long invoiceId) {
        LambdaQueryWrapper<FinApInvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApInvoiceItem::getInvoiceId, invoiceId);
        return baseMapper.exists(wrapper);
    }

    /**
     * 根据发票ID获取明细ID列表
     *
     * @param invoiceId 发票ID
     * @return 明细ID列表
     */
    @Override
    public List<Long> getItemIdsByInvoiceId(Long invoiceId) {
        LambdaQueryWrapper<FinApInvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApInvoiceItem::getInvoiceId, invoiceId);
        wrapper.select(FinApInvoiceItem::getItemId);
        return baseMapper.selectObjs(wrapper);
    }
}
