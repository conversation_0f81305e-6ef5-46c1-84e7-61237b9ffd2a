package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购入库明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface IPurchaseInboundItemService {

    /**
     * 查询采购入库明细
     *
     * @param itemId 主键
     * @return 采购入库明细
     */
    PurchaseInboundItemVo queryById(Long itemId);

    /**
     * 分页查询采购入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库明细分页列表
     */
    TableDataInfo<PurchaseInboundItemVo> queryPageList(PurchaseInboundItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购入库明细列表
     *
     * @param bo 查询条件
     * @return 采购入库明细列表
     */
    List<PurchaseInboundItemVo> queryList(PurchaseInboundItemBo bo);

    /**
     * 新增采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PurchaseInboundItemBo bo);

    /**
     * 修改采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PurchaseInboundItemBo bo);

    /**
     * 校验并批量删除采购入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入采购入库明细表
     * ✅ 修正：使用BO而非直接暴露Entity
     *
     * @param items 明细BO列表
     * @return 是否插入成功
     */
    Boolean insertOrUpdateBatch(List<PurchaseInboundItemBo> items);

    /**
     * 根据采购入库单id查询采购入库明细表id集合
     *
     * @param inboundId 采购入库单id
     * @return 采购入库明细表id集合
     */
    List<Long> selectItemIdsByInboundId(Long inboundId);

}
