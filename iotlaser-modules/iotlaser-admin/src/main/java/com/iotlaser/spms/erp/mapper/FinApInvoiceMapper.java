package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.FinApInvoice;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 应付Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface FinApInvoiceMapper extends BaseMapperPlus<FinApInvoice, FinApInvoiceVo> {

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<FinApInvoice>().eq(FinApInvoice::getDirectSourceId, directSourceId));
    }

    default List<FinApInvoice> selectListByDirectSourceId(Long directSourceId) {
        return selectList(new LambdaQueryWrapper<FinApInvoice>().eq(FinApInvoice::getDirectSourceId, directSourceId));
    }

    default List<FinApInvoice> queryByPayeeId(Long payeeId) {
        return selectList(new LambdaQueryWrapper<FinApInvoice>().orderByAsc(FinApInvoice::getInvoiceDate).eq(FinApInvoice::getPayeeId, payeeId).in(FinApInvoice::getInvoiceStatus, FinApInvoiceStatus.UNPAID.getValue(), FinApInvoiceStatus.PARTIALLY_PAID.getValue()));
    }
}
