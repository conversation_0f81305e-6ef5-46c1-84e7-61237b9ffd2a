package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleReturnItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 销售退货明细业务对象 erp_sale_return_item
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleReturnItem.class, reverseConvertGenerate = false)
public class SaleReturnItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 退货单ID
     */
    @NotNull(message = "退货单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long returnId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 应退数量
     */
    @NotNull(message = "应退数量不能为空", groups = {EditGroup.class})
    private BigDecimal quantity;

    /**
     * 实收退货数量
     */
    private BigDecimal finishQuantity;

    /**
     * 单价(含税)
     */
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
