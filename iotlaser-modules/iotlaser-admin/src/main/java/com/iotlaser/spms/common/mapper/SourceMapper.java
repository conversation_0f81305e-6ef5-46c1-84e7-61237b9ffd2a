package com.iotlaser.spms.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.Source;
import com.iotlaser.spms.common.domain.SourceInfo;
import com.iotlaser.spms.common.domain.vo.SourceVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-04-23
 */
public interface SourceMapper extends BaseMapperPlus<Source, SourceVo> {

    /**
     * 查询采购入库明细表及其关联信息
     */
    Source queryByIdWith(@Param("sourceInfo") SourceInfo sourceInfo, @Param("id") Long id);

    /**
     * 分页查询采购入库明细表及其关联信息
     */
    List<Source> queryPageListWith(@Param("sourceInfo") SourceInfo sourceInfo, @Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<Source> wrapper);

}
