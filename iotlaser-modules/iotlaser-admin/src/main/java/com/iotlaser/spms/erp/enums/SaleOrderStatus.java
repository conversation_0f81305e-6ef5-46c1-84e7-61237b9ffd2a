package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SaleOrderStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "订单创建，待确认"),
    CONFIRMED("confirmed", "已确认", "订单已审核，可用于指导生产或发货"),
    ON_HOLD("on_hold", "挂起", "因信用、库存等问题暂时挂起，不能发货"),
    PENDING_PRODUCTION("pending_production", "待生产", "订单已确认，关联的生产任务未开始"),
    IN_PRODUCTION("in_production", "生产中", "关联的生产任务已开始"),
    READY_TO_SHIP("ready_to_ship", "待发货", "货物已备齐（或生产完），等待发货"),
    PARTIALLY_SHIPPED("partially_shipped", "部分发货", "已发出部分货物"),
    FULLY_SHIPPED("fully_shipped", "全部发货", "订单所有货物已发出"),
    COMPLETED("completed", "已完成", "订单已完成"),
    CLOSED("closed", "已关闭", "发货且回款等流程结束，订单归档"),
    CANCELLED("cancelled", "已取消", "在发货前，订单被作废");

    public final static String DICT_CODE = "erp_sale_order_status";
    public final static String DICT_NAME = "销售订单状态";
    public final static String DICT_DESC = "管理销售订单从创建到完成的全生命周期状态，包括草稿、确认、发货、关闭等业务流程状态";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 销售订单状态枚举
     */
    public static SaleOrderStatus getByValue(String value) {
        for (SaleOrderStatus orderStatus : values()) {
            if (orderStatus.getValue().equals(value)) {
                return orderStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == FULLY_SHIPPED || this == CLOSED || this == CANCELLED;
    }

    /**
     * 判断是否可以发货
     *
     * @return 是否可以发货
     */
    public boolean canShip() {
        return this == READY_TO_SHIP || this == PARTIALLY_SHIPPED;
    }

    /**
     * 判断是否为挂起状态
     *
     * @return 是否为挂起状态
     */
    public boolean isOnHold() {
        return this == ON_HOLD;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public SaleOrderStatus[] getNextPossibleStates() {
        switch (this) {
            case DRAFT:
                return new SaleOrderStatus[]{CONFIRMED, CANCELLED};
            case CONFIRMED:
                return new SaleOrderStatus[]{PENDING_PRODUCTION, READY_TO_SHIP, ON_HOLD, CANCELLED};
            case ON_HOLD:
                return new SaleOrderStatus[]{CONFIRMED, CANCELLED};
            case PENDING_PRODUCTION:
                return new SaleOrderStatus[]{IN_PRODUCTION, CANCELLED};
            case IN_PRODUCTION:
                return new SaleOrderStatus[]{READY_TO_SHIP, CANCELLED};
            case READY_TO_SHIP:
                return new SaleOrderStatus[]{PARTIALLY_SHIPPED, FULLY_SHIPPED, CANCELLED};
            case PARTIALLY_SHIPPED:
                return new SaleOrderStatus[]{FULLY_SHIPPED, CANCELLED};
            case FULLY_SHIPPED:
                return new SaleOrderStatus[]{CLOSED};
            case CLOSED:
            case CANCELLED:
            default:
                return new SaleOrderStatus[]{};
        }
    }
}
