package com.iotlaser.spms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.Company;
import com.iotlaser.spms.base.domain.bo.CompanyBo;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.mapper.CompanyMapper;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.BASE_COMPANY_CODE;

/**
 * 公司信息Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CompanyServiceImpl implements ICompanyService {

    private final CompanyMapper baseMapper;
    private final Gen gen;

    /**
     * 查询公司信息
     *
     * @param companyId 主键
     * @return 公司信息
     */
    @Override
    public CompanyVo queryById(Long companyId) {
        return baseMapper.selectVoById(companyId);
    }

    /**
     * 分页查询公司信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 公司信息分页列表
     */
    @Override
    public TableDataInfo<CompanyVo> queryPageList(CompanyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Company> lqw = buildQueryWrapper(bo);
        Page<CompanyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的公司信息列表
     *
     * @param bo 查询条件
     * @return 公司信息列表
     */
    @Override
    public List<CompanyVo> queryList(CompanyBo bo) {
        LambdaQueryWrapper<Company> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Company> buildQueryWrapper(CompanyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Company> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Company::getCompanyId);
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyType()), Company::getCompanyType, bo.getCompanyType());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyCode()), Company::getCompanyCode, bo.getCompanyCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), Company::getCompanyName, bo.getCompanyName());
        lqw.like(StringUtils.isNotBlank(bo.getShortName()), Company::getShortName, bo.getShortName());
        lqw.like(StringUtils.isNotBlank(bo.getEnglishName()), Company::getEnglishName, bo.getEnglishName());
        lqw.eq(StringUtils.isNotBlank(bo.getIntro()), Company::getIntro, bo.getIntro());
        lqw.eq(StringUtils.isNotBlank(bo.getLevel()), Company::getLevel, bo.getLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getLogo()), Company::getLogo, bo.getLogo());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), Company::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getWebsite()), Company::getWebsite, bo.getWebsite());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), Company::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getTel()), Company::getTel, bo.getTel());
        lqw.eq(StringUtils.isNotBlank(bo.getContact1()), Company::getContact1, bo.getContact1());
        lqw.eq(StringUtils.isNotBlank(bo.getContact1Tel()), Company::getContact1Tel, bo.getContact1Tel());
        lqw.eq(StringUtils.isNotBlank(bo.getContact1Email()), Company::getContact1Email, bo.getContact1Email());
        lqw.eq(StringUtils.isNotBlank(bo.getContact2()), Company::getContact2, bo.getContact2());
        lqw.eq(StringUtils.isNotBlank(bo.getContact2Tel()), Company::getContact2Tel, bo.getContact2Tel());
        lqw.eq(StringUtils.isNotBlank(bo.getContact2Email()), Company::getContact2Email, bo.getContact2Email());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierFlag()), Company::getSupplierFlag, bo.getSupplierFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerFlag()), Company::getCustomerFlag, bo.getCustomerFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Company::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增公司信息
     *
     * @param bo 公司信息
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(CompanyBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getCompanyCode())) {
                bo.setCompanyCode(gen.code(BASE_COMPANY_CODE));
            }
            Company add = MapstructUtils.convert(bo, Company.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增公司信息失败");
            }

            bo.setCompanyId(add.getCompanyId());
            log.info("新增公司信息成功：{}", add.getCompanyName());
            return true;
        } catch (Exception e) {
            log.error("新增公司信息失败：{}", e.getMessage(), e);
            throw new ServiceException("新增公司信息失败：" + e.getMessage());
        }
    }

    /**
     * 修改公司信息
     *
     * @param bo 公司信息
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(CompanyBo bo) {
        try {
            Company update = MapstructUtils.convert(bo, Company.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改公司信息失败：公司不存在或数据未变更");
            }

            log.info("修改公司信息成功：{}", update.getCompanyName());
            return true;
        } catch (Exception e) {
            log.error("修改公司信息失败：{}", e.getMessage(), e);
            throw new ServiceException("修改公司信息失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Company entity) {
        // 校验公司编码唯一性
        if (StringUtils.isNotBlank(entity.getCompanyCode())) {
            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getCompanyCode, entity.getCompanyCode());
            if (entity.getCompanyId() != null) {
                wrapper.ne(Company::getCompanyId, entity.getCompanyId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("公司编码已存在：" + entity.getCompanyCode());
            }
        }

        // 校验公司名称唯一性
        if (StringUtils.isNotBlank(entity.getCompanyName())) {
            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getCompanyName, entity.getCompanyName());
            if (entity.getCompanyId() != null) {
                wrapper.ne(Company::getCompanyId, entity.getCompanyId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("公司名称已存在：" + entity.getCompanyName());
            }
        }
    }

    /**
     * 校验并批量删除公司信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验公司是否被其他业务数据引用
            List<Company> companies = baseMapper.selectByIds(ids);
            for (Company company : companies) {
                // 检查是否有关联的采购订单（作为供应商）
                // TODO: 添加purchaseOrderService依赖注入
                // if (purchaseOrderService.existsBySupplierId(company.getCompanyId())) {
                //    throw new ServiceException("公司【" + company.getCompanyName() + "】存在关联的采购订单，不允许删除");
                // }

                // 检查是否有关联的销售订单（作为客户）
                // TODO: 添加saleOrderService依赖注入
                // if (saleOrderService.existsByCustomerId(company.getCompanyId())) {
                //    throw new ServiceException("公司【" + company.getCompanyName() + "】存在关联的销售订单，不允许删除");
                // }

                // 检查是否有关联的应收账款
                // TODO: 添加finArReceivableService依赖注入
                // if (finArReceivableService.existsByCustomerId(company.getCompanyId())) {
                //    throw new ServiceException("公司【" + company.getCompanyName() + "】存在关联的应收账款，不允许删除");
                // }

                // 检查是否有关联的应付发票
                // TODO: 添加finApInvoiceService依赖注入
                // if (finApInvoiceService.existsBySupplierId(company.getCompanyId())) {
                //    throw new ServiceException("公司【" + company.getCompanyName() + "】存在关联的应付发票，不允许删除");
                // }

                log.info("删除公司校验通过：{}", company.getCompanyName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除公司信息成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除公司信息失败：{}", e.getMessage(), e);
            throw new ServiceException("删除公司信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新公司状态
     *
     * @param companyId 公司ID
     * @param status    状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCompanyStatus(Long companyId, String status) {
        try {
            // 检查公司是否存在
            Company company = baseMapper.selectById(companyId);
            if (company == null) {
                throw new ServiceException("公司不存在");
            }

            // 更新状态
            Company updateEntity = new Company();
            updateEntity.setCompanyId(companyId);
            updateEntity.setStatus(status);

            int result = baseMapper.updateById(updateEntity);
            if (result <= 0) {
                throw new ServiceException("更新公司状态失败");
            }

            String statusText = "1".equals(status) ? "启用" : "禁用";
            log.info("更新公司状态成功：{} - {}", company.getCompanyName(), statusText);
            return true;
        } catch (Exception e) {
            log.error("更新公司状态失败：{}", e.getMessage(), e);
            throw new ServiceException("更新公司状态失败：" + e.getMessage());
        }
    }

    /**
     * 启用公司
     *
     * @param companyId 公司ID
     * @return 是否启用成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableCompany(Long companyId) {
        return updateCompanyStatus(companyId, "1");
    }

    /**
     * 禁用公司
     *
     * @param companyId 公司ID
     * @return 是否禁用成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableCompany(Long companyId) {
        return updateCompanyStatus(companyId, "0");
    }

    /**
     * 获取所有启用的公司
     *
     * @return 启用的公司列表
     */
    @Override
    public List<CompanyVo> getActiveCompanies() {
        try {
            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getStatus, "1");
            wrapper.orderByAsc(Company::getCompanyCode);

            List<CompanyVo> result = baseMapper.selectVoList(wrapper);
            log.debug("查询启用公司成功，数量：{}", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询启用公司失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据公司类型获取公司列表
     *
     * @param companyType 公司类型
     * @return 公司列表
     */
    @Override
    public List<CompanyVo> getCompaniesByType(String companyType) {
        try {
            if (StringUtils.isBlank(companyType)) {
                return new ArrayList<>();
            }

            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getCompanyType, companyType);
            wrapper.eq(Company::getStatus, "1"); // 只查询启用的公司
            wrapper.orderByAsc(Company::getCompanyCode);

            List<CompanyVo> result = baseMapper.selectVoList(wrapper);
            log.debug("根据类型查询公司成功，类型：{}，数量：{}", companyType, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据类型查询公司失败，类型：{}，错误：{}", companyType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取供应商列表
     *
     * @return 供应商列表
     */
    @Override
    public List<CompanyVo> getSuppliers() {
        try {
            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getSupplierFlag, "1");
            wrapper.eq(Company::getStatus, "1");
            wrapper.orderByAsc(Company::getCompanyCode);

            List<CompanyVo> result = baseMapper.selectVoList(wrapper);
            log.debug("查询供应商成功，数量：{}", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询供应商失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取客户列表
     *
     * @return 客户列表
     */
    @Override
    public List<CompanyVo> getCustomers() {
        try {
            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getCustomerFlag, "1");
            wrapper.eq(Company::getStatus, "1");
            wrapper.orderByAsc(Company::getCompanyCode);

            List<CompanyVo> result = baseMapper.selectVoList(wrapper);
            log.debug("查询客户成功，数量：{}", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询客户失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据公司编码查询公司信息
     *
     * @param companyCode 公司编码
     * @return 公司信息
     */
    @Override
    public CompanyVo getByCompanyCode(String companyCode) {
        try {
            if (StringUtils.isBlank(companyCode)) {
                return null;
            }

            LambdaQueryWrapper<Company> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Company::getCompanyCode, companyCode);
            wrapper.eq(Company::getStatus, "1");

            CompanyVo result = baseMapper.selectVoOne(wrapper);
            log.debug("根据编码查询公司：{}", companyCode);
            return result;
        } catch (Exception e) {
            log.error("根据编码查询公司失败，编码：{}，错误：{}", companyCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量更新公司状态
     *
     * @param companyIds 公司ID列表
     * @param status     状态
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateStatus(List<Long> companyIds, String status) {
        try {
            // 检查所有公司是否存在
            List<Company> companies = baseMapper.selectByIds(companyIds);
            if (companies.size() != companyIds.size()) {
                throw new ServiceException("部分公司不存在");
            }

            // 批量更新状态
            for (Long companyId : companyIds) {
                Company updateEntity = new Company();
                updateEntity.setCompanyId(companyId);
                updateEntity.setStatus(status);
                baseMapper.updateById(updateEntity);
            }

            String statusText = "1".equals(status) ? "启用" : "禁用";
            log.info("批量更新公司状态成功：{} - 数量：{}", statusText, companyIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量更新公司状态失败：{}", e.getMessage(), e);
            throw new ServiceException("批量更新公司状态失败：" + e.getMessage());
        }
    }
}
