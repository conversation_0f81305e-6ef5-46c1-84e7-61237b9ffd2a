package com.iotlaser.spms.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.mes.domain.ProductionReport;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 生产报工记录视图对象 mes_production_report
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductionReport.class)
public class ProductionReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报工ID
     */
    @ExcelProperty(value = "报工ID")
    private Long reportId;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long orderId;

    /**
     * 实例ID
     */
    @ExcelProperty(value = "实例ID")
    private Long instanceId;

    /**
     * 步骤ID
     */
    @ExcelProperty(value = "步骤ID")
    private Long stepId;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long processId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 报工类型
     */
    @ExcelProperty(value = "报工类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "mes_production_report_type")
    private String reportType;

    /**
     * 良品数量
     */
    @ExcelProperty(value = "良品数量")
    private BigDecimal quantityGood;

    /**
     * 不良品数量
     */
    @ExcelProperty(value = "不良品数量")
    private BigDecimal quantityBad;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 报工操作员ID
     */
    @ExcelProperty(value = "报工操作员ID")
    private Long operatorId;

    /**
     * 报工操作员
     */
    @ExcelProperty(value = "报工操作员")
    private String operatorName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
