package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.dto.FinApInvoiceMatchResultDto;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.event.PurchaseInboundEvent;
import com.iotlaser.spms.erp.mapper.FinApInvoiceItemMapper;
import com.iotlaser.spms.erp.mapper.FinApInvoiceMapper;
import com.iotlaser.spms.erp.mapper.FinApPaymentInvoiceLinkMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IThreeWayMatchService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_FIN_AP_INVOICE_CODE;

/**
 * 应付单（发票）Service业务层处理
 * 管理应付单的完整生命周期，包括发票创建、审核、匹配、核销等核心财务功能
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApInvoiceServiceImpl implements IFinApInvoiceService {
    // =================================================================================================================
    // [DDD-TODO] 跨聚合调用优化 - 目标: 实现最终一致性，降低服务间耦合
    // 当前为保持业务流程的同步与完整性，暂时直接依赖其他聚合根的Service。
    // 理想架构应通过领域事件(Domain Event)或应用服务层(Application Service)进行解耦。
    // 例如：核销完成后，发布`InvoiceAppliedEvent`，由总账上下文的监听器异步订阅并创建流水。
    // -----------------------------------------------------------------------------------------------------------------
    // 参考文档: docs/design/README_OVERVIEW.md
    // TODO: [REFACTOR] - [HIGH] - 将此处的服务直接依赖重构为领域事件模式。
    // =================================================================================================================

    private final FinApInvoiceMapper baseMapper;
    private final FinApInvoiceItemMapper itemMapper;
    private final FinApPaymentInvoiceLinkMapper linkMapper;
    private final ICompanyService companyService;
    private final Gen gen;
    private final IThreeWayMatchService threeWayMatchService;

    /**
     * {@inheritDoc}
     */
    @Override
    public FinApInvoiceVo queryById(Long invoiceId) {
        return baseMapper.selectVoById(invoiceId);
    }

    /**
     * 分页查询应付单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应付单分页列表
     */
    @Override
    public TableDataInfo<FinApInvoiceVo> queryPageList(FinApInvoiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApInvoice> lqw = buildQueryWrapper(bo);
        Page<FinApInvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应付单列表
     *
     * @param bo 查询条件
     * @return 应付单列表
     */
    @Override
    public List<FinApInvoiceVo> queryList(FinApInvoiceBo bo) {
        LambdaQueryWrapper<FinApInvoice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApInvoice> buildQueryWrapper(FinApInvoiceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApInvoice> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApInvoice::getInvoiceId);
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceCode()), FinApInvoice::getInvoiceCode, bo.getInvoiceCode());
        if (bo.getPayeeType() != null) {
            lqw.eq(FinApInvoice::getPayeeType, bo.getPayeeType());
        }
        lqw.eq(bo.getPayeeId() != null, FinApInvoice::getPayeeId, bo.getPayeeId());
        lqw.like(StringUtils.isNotBlank(bo.getPayeeName()), FinApInvoice::getPayeeName, bo.getPayeeName());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), FinApInvoice::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getInvoiceDate() != null, FinApInvoice::getInvoiceDate, bo.getInvoiceDate());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinApInvoice::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxAmount() != null, FinApInvoice::getTaxAmount, bo.getTaxAmount());
        lqw.eq(bo.getAmount() != null, FinApInvoice::getAmount, bo.getAmount());
        if (bo.getInvoiceStatus() != null) {
            lqw.eq(FinApInvoice::getInvoiceStatus, bo.getInvoiceStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApInvoice::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinApInvoiceVo insertByBo(FinApInvoiceBo bo) {
        final String currentUser = LoginHelper.getUsername();

        try {
            // 1. 初始化单据编码
            if (StringUtils.isEmpty(bo.getInvoiceCode())) {
                bo.setInvoiceCode(gen.code(ERP_FIN_AP_INVOICE_CODE));
                log.debug("[insertByBo] - 单据编码为空，自动生成: {}", bo.getInvoiceCode());
            }
            // 2. 设置初始状态
            if (bo.getInvoiceStatus() == null) {
                bo.setInvoiceStatus(FinApInvoiceStatus.DRAFT);
            }
            // 3. 设置开票日期
            if (bo.getInvoiceDate() == null) {
                bo.setInvoiceDate(LocalDate.now());
            }
            // 4. 填充冗余字段
            fillRedundantFields(bo);

            // 5. 转换为实体并校验
            FinApInvoice add = MapstructUtils.convert(bo, FinApInvoice.class);
            validEntityBeforeSave(add);

            // 6. 插入数据库
            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                log.error("[insertByBo] - 失败. 数据库插入返回false. 操作人: {}, 数据: {}", currentUser, add);
                throw new ServiceException("创建应付单失败，数据库操作异常");
            }
            return MapstructUtils.convert(add, FinApInvoiceVo.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinApInvoiceVo updateByBo(FinApInvoiceBo bo) {
        final String currentUser = LoginHelper.getUsername();
        log.info("[updateByBo] - 开始. 操作人: {}, ID: {}", currentUser, bo.getInvoiceId());
        try {
            // 1. 填充冗余字段
            fillRedundantFields(bo);

            // 2. 转换为实体并校验
            FinApInvoice update = MapstructUtils.convert(bo, FinApInvoice.class);
            validEntityBeforeSave(update);

            // 3. 更新数据库
            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                log.warn("[updateByBo] - 警告. 数据库更新未生效，记录可能已被删除。操作人: {}, ID: {}", currentUser, update.getInvoiceId());
                throw new ServiceException("修改失败：更新应付单失败，请刷新后重试");
            }

            log.info("[updateByBo] - 成功. 操作人: {}, ID: {}", currentUser, update.getInvoiceId());
            return queryById(update.getInvoiceId());
        } catch (ServiceException se) {
            log.warn("[updateByBo] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getInvoiceId(), se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[updateByBo] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, bo.getInvoiceId(), e.getMessage(), e);
            throw new ServiceException(String.format("修改应付单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApInvoice entity) {
        // 业务规则1: 校验发票编号在系统中必须唯一
        if (StringUtils.isNotBlank(entity.getInvoiceCode())) {
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinApInvoice::getInvoiceCode, entity.getInvoiceCode());
            if (entity.getInvoiceId() != null) {
                wrapper.ne(FinApInvoice::getInvoiceId, entity.getInvoiceId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("操作失败：应付单号 [" + entity.getInvoiceCode() + "] 已被使用，请修改");
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        final String currentUser = LoginHelper.getUsername();
        log.info("[deleteWithValidByIds] - 开始. 操作人: {}, 请求ID: {}, 是否校验: {}", currentUser, ids, isValid);
        try {
            if (isValid) {
                List<FinApInvoice> invoices = baseMapper.selectByIds(ids);
                for (FinApInvoice invoice : invoices) {
                    if (invoice.getInvoiceStatus() != FinApInvoiceStatus.DRAFT) {
                        throw new ServiceException("删除失败：应付单 [" + invoice.getInvoiceCode() + "] 状态为“" + invoice.getInvoiceStatus().getDesc() + "”，仅草稿状态可删除");
                    }
                    if (linkMapper.existsByInvoiceId(invoice.getInvoiceId())) {
                        throw new ServiceException("删除失败：应付单 [" + invoice.getInvoiceCode() + "] 已存在付款核销记录，不允许删除");
                    }
                    log.debug("[deleteWithValidByIds] - 通过. 操作人: {}, 单号: {}", currentUser, invoice.getInvoiceCode());
                }
            } else {
                log.warn("[deleteWithValidByIds] - 警告. 跳过业务校验直接删除数据。操作人: {}", currentUser);
            }

            // 级联删除明细
            if (!ids.isEmpty()) {
                int itemResult = itemMapper.deleteByInvoiceIds(ids);
                if (itemResult > 0) {
                    log.info("[deleteWithValidByIds] - 成功删除应付单明细。操作人: {}, 删除明细数: {}", currentUser, itemResult);
                }
            }

            // 删除主表
            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("[deleteWithValidByIds] - 成功. 操作人: {}, 删除ID列表: {}", currentUser, ids);
            } else {
                log.warn("[deleteWithValidByIds] - 警告. 数据库操作未影响任何行。操作人: {}, 尝试删除的ID: {}", currentUser, ids);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("[deleteWithValidByIds] - 业务异常. 操作人: {}, 错误: {}", currentUser, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - 系统异常. 操作人: {}, 错误: {}", currentUser, e.getMessage(), e);
            throw new ServiceException(String.format("删除应付单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }

    /**
     * 判断上游
     *
     * @param directSourceId 上游单据ID
     * @return 是否上游
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId) {
        return baseMapper.existsByDirectSourceId(directSourceId);
    }

    /**
     * 查询上游
     *
     * @param directSourceId 上游单据ID
     * @return 应付单列表
     */
    @Override
    public List<FinApInvoice> selectListByDirectSourceId(Long directSourceId) {
        return baseMapper.selectListByDirectSourceId(directSourceId);
    }

    /**
     * 查询指定供应商可以继续核销的应付单
     *
     * @param payeeId 供应商ID
     * @return 应付单列表
     */
    @Override
    public List<FinApInvoice> queryByPayeeId(Long payeeId) {
        return baseMapper.queryByPayeeId(payeeId);
    }

    /**
     * 更新发票状态
     *
     * @param invoiceId     发票ID
     * @param invoiceStatus 新状态
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInvoiceStatus(Long invoiceId, FinApInvoiceStatus invoiceStatus) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            invoice.setInvoiceStatus(invoiceStatus);
            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票状态更新成功 - 发票: {}, 新状态: {}", invoice.getInvoiceCode(), invoiceStatus);
            }

            return result;
        } catch (Exception e) {
            log.error("发票状态更新失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 三单匹配 - 自动匹配
     *
     * @param invoiceId 发票ID
     * @return 匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean autoMatchThreeWay(Long invoiceId) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (invoice.getInvoiceStatus() != FinApInvoiceStatus.UNPAID) {
                throw new ServiceException("发票状态不允许匹配");
            }

            // 查找匹配的采购订单和采购入库单
            List<FinApInvoiceMatchResultDto> matchResults = findMatchingDocuments(invoice);

            if (matchResults.isEmpty()) {
                log.warn("发票自动匹配失败 - 未找到匹配的采购订单或采购入库单: {}", invoice.getInvoiceCode());
                return false;
            }

            // 执行匹配逻辑
            boolean matchResult = executeMatching(invoice, matchResults);

            if (matchResult) {
                // 更新发票匹配状态
                FinApInvoiceStatus newStatus = calculateMatchStatus(invoice, matchResults);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票自动匹配成功 - 发票: {}, 状态: {}", invoice.getInvoiceCode(), newStatus);
            }

            return matchResult;
        } catch (Exception e) {
            log.error("发票自动匹配失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票自动匹配失败：" + e.getMessage());
        }
    }

    /**
     * 三单匹配 - 手工匹配
     *
     * @param invoiceId       发票ID
     * @param purchaseOrderId 采购订单ID
     * @param inboundId       采购入库单ID
     * @param matchedAmount   匹配金额
     * @param operatorId      操作人ID
     * @param operatorName    操作人姓名
     * @return 匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualMatchThreeWay(Long invoiceId, Long purchaseOrderId, Long inboundId,
                                       BigDecimal matchedAmount, Long operatorId, String operatorName) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            // 校验匹配金额
            if (matchedAmount.compareTo(invoice.getAmount()) > 0) {
                throw new ServiceException("匹配金额不能超过发票总金额");
            }

            // 创建匹配记录
            boolean matchResult = createMatchRecord(invoice, purchaseOrderId, inboundId,
                matchedAmount, operatorId, operatorName);

            if (matchResult) {
                // 更新发票匹配状态
                FinApInvoiceStatus newStatus = calculateMatchStatusByAmount(invoice, matchedAmount);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票手工匹配成功 - 发票: {}, 匹配金额: {}, 操作人: {}",
                    invoice.getInvoiceCode(), matchedAmount, operatorName);
            }

            return matchResult;
        } catch (Exception e) {
            log.error("发票手工匹配失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票手工匹配失败：" + e.getMessage());
        }
    }

    /**
     * 查找匹配的采购订单和采购入库单
     */
    private List<FinApInvoiceMatchResultDto> findMatchingDocuments(FinApInvoice invoice) {
        List<FinApInvoiceMatchResultDto> results = new ArrayList<>();

        // 基于供应商和时间范围查找采购订单
        // 这里需要调用采购订单服务查找匹配的订单
        // 实际实现中需要根据供应商ID、物料、数量等条件进行匹配

        // 基于采购订单查找对应的采购入库单
        // 这里需要调用采购入库单服务查找匹配的采购入库单

        // 计算匹配度并排序
        // 匹配度计算基于：供应商匹配、物料匹配、数量匹配、金额匹配、时间匹配等

        return results;
    }

    /**
     * 执行匹配逻辑
     */
    private boolean executeMatching(FinApInvoice invoice, List<FinApInvoiceMatchResultDto> matchResults) {
        // 执行具体的匹配逻辑
        // 创建匹配记录
        // 更新相关单据状态
        return true;
    }

    /**
     * 计算匹配状态
     */
    private FinApInvoiceStatus calculateMatchStatus(FinApInvoice invoice, List<FinApInvoiceMatchResultDto> matchResults) {
        // 根据匹配结果计算发票状态
        BigDecimal totalMatchedAmount = matchResults.stream()
            .map(FinApInvoiceMatchResultDto::getMatchedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalMatchedAmount.compareTo(invoice.getAmount()) >= 0) {
            return FinApInvoiceStatus.FULLY_PAID;
        } else if (totalMatchedAmount.compareTo(BigDecimal.ZERO) > 0) {
            return FinApInvoiceStatus.PARTIALLY_PAID;
        } else {
            return FinApInvoiceStatus.UNPAID;
        }
    }

    /**
     * 根据金额计算匹配状态
     */
    private FinApInvoiceStatus calculateMatchStatusByAmount(FinApInvoice invoice, BigDecimal matchedAmount) {
        // 获取已匹配总金额
        BigDecimal totalMatchedAmount = getInvoiceMatchedAmount(invoice.getInvoiceId()).add(matchedAmount);

        if (totalMatchedAmount.compareTo(invoice.getAmount()) >= 0) {
            return FinApInvoiceStatus.FULLY_PAID;
        } else if (totalMatchedAmount.compareTo(BigDecimal.ZERO) > 0) {
            return FinApInvoiceStatus.PARTIALLY_PAID;
        } else {
            return FinApInvoiceStatus.UNPAID;
        }
    }

    /**
     * 创建匹配记录
     */
    private boolean createMatchRecord(FinApInvoice invoice, Long purchaseOrderId, Long inboundId,
                                      BigDecimal matchedAmount, Long operatorId, String operatorName) {
        // 创建三单匹配记录
        // 这里需要创建一个匹配记录表来记录匹配关系
        // 包含：发票ID、采购订单ID、采购入库单ID、匹配金额、匹配时间、操作人等
        return true;
    }

    /**
     * 获取发票已匹配金额
     */
    private BigDecimal getInvoiceMatchedAmount(Long invoiceId) {
        // 查询发票的已匹配金额
        // 从匹配记录表中汇总计算
        return BigDecimal.ZERO;
    }

    /**
     * 获取待匹配发票列表
     *
     * @param supplierId 供应商ID (可选)
     * @param startDate  开始日期 (可选)
     * @param endDate    结束日期 (可选)
     * @return 待匹配发票列表
     */
    public List<FinApInvoiceVo> getUnmatchedInvoices(Long supplierId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
        wrapper.in(FinApInvoice::getInvoiceStatus, FinApInvoiceStatus.UNPAID, FinApInvoiceStatus.PARTIALLY_PAID);
        if (supplierId != null) {
            wrapper.eq(FinApInvoice::getPayeeId, supplierId);
        }
        if (startDate != null) {
            wrapper.ge(FinApInvoice::getInvoiceDate, startDate);
        }
        if (endDate != null) {
            wrapper.le(FinApInvoice::getInvoiceDate, endDate);
        }
        wrapper.orderByDesc(FinApInvoice::getInvoiceDate);
        List<FinApInvoice> invoices = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(invoices, FinApInvoiceVo.class);
    }

    /**
     * 获取发票匹配详情
     *
     * @param invoiceId 发票ID
     * @return 匹配详情
     */
    public FinApInvoiceVo getInvoiceMatchDetail(Long invoiceId) {
        FinApInvoice invoice = baseMapper.selectById(invoiceId);
        if (invoice == null) {
            throw new ServiceException("发票不存在");
        }
        FinApInvoiceVo invoiceVo = MapstructUtils.convert(invoice, FinApInvoiceVo.class);
        // 获取匹配记录
        // 这里需要查询匹配记录表，获取该发票的所有匹配记录
        // 包括匹配的采购订单、采购入库单、匹配金额等信息
        return invoiceVo;
    }

    /**
     * 撤销发票匹配
     *
     * @param invoiceId     发票ID
     * @param matchRecordId 匹配记录ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 是否撤销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInvoiceMatch(Long invoiceId, Long matchRecordId, Long operatorId, String operatorName) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            // 删除匹配记录
            boolean deleteResult = deleteMatchRecord(matchRecordId, operatorId, operatorName);

            if (deleteResult) {
                // 重新计算发票匹配状态
                FinApInvoiceStatus newStatus = recalculateMatchStatus(invoiceId);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票匹配撤销成功 - 发票: {}, 操作人: {}", invoice.getInvoiceCode(), operatorName);
            }

            return deleteResult;
        } catch (Exception e) {
            log.error("发票匹配撤销失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票匹配撤销失败：" + e.getMessage());
        }
    }

    /**
     * 删除匹配记录
     */
    private boolean deleteMatchRecord(Long matchRecordId, Long operatorId, String operatorName) {
        // 删除匹配记录的具体实现
        // 这里需要操作匹配记录表
        return true;
    }

    /**
     * 重新计算匹配状态
     */
    private FinApInvoiceStatus recalculateMatchStatus(Long invoiceId) {
        // 重新计算发票的匹配状态
        BigDecimal totalMatchedAmount = getInvoiceMatchedAmount(invoiceId);
        FinApInvoice invoice = baseMapper.selectById(invoiceId);

        if (totalMatchedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return FinApInvoiceStatus.UNPAID;
        } else if (totalMatchedAmount.compareTo(invoice.getAmount()) >= 0) {
            return FinApInvoiceStatus.FULLY_PAID;
        } else {
            return FinApInvoiceStatus.PARTIALLY_PAID;
        }
    }

    /**
     * 取消应付单
     *
     * @param invoiceId    发票ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInvoice(Long invoiceId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (invoice.getInvoiceStatus() == FinApInvoiceStatus.FULLY_PAID || invoice.getInvoiceStatus() == FinApInvoiceStatus.CANCELLED) {
                throw new ServiceException("发票状态不允许取消");
            }

            invoice.setInvoiceStatus(FinApInvoiceStatus.CANCELLED);
            invoice.setRemark(cancelReason);
            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票取消成功 - 发票: {}, 取消人: {}, 原因: {}",
                    invoice.getInvoiceCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("发票取消失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票取消失败：" + e.getMessage());
        }
    }

    /**
     * 从采购订单明细生成应付单明细
     *
     * @param purchaseOrder 采购订单
     * @return 是否生成成功
     */
    @Override
    public Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder) {
        try {
            if (PurchaseOrderStatus.FULLY_RECEIVED == purchaseOrder.getOrderStatus()) {
                throw new ServiceException("只有全部到货的采购订单才能生成应付单");
            }
            if (baseMapper.existsByDirectSourceId(purchaseOrder.getOrderId())) {
                throw new ServiceException("该采购订单已生成应付单，不能重复生成");
            }
            // 创建应付单主记录
            FinApInvoice invoice = new FinApInvoice();
            // 生成应付单编号和名称
            invoice.setInvoiceCode(gen.code(ERP_FIN_AP_INVOICE_CODE));
            invoice.setSummary("采购应付-" + purchaseOrder.getSupplierName() + "-" + purchaseOrder.getOrderCode());
            // 供应商信息
            invoice.setPayeeId(purchaseOrder.getSupplierId());
            invoice.setPayeeName(purchaseOrder.getSupplierName());
            // 设置应付状态
            invoice.setInvoiceStatus(FinApInvoiceStatus.DRAFT);
            invoice.setInvoiceDate(LocalDate.now());
            // 插入应付单主记录
            int result = baseMapper.insert(invoice);
            if (result <= 0) {
                throw new ServiceException("应付单生成失败");
            }
            // 从采购入库单明细生成应付单明细
            List<FinApInvoiceItem> invoiceItems = new ArrayList<>();
            // 获取采购入库单明细信息并创建应付单明细
            for (PurchaseOrderItem purchaseOrderItem : purchaseOrder.getItems()) {
                // 创建应付单明细
                FinApInvoiceItem invoiceItem = new FinApInvoiceItem();
                invoiceItem.setInvoiceId(invoice.getInvoiceId());
                // 设置源订单信息（明细表负责维护完整的源订单关联）
                invoiceItem.setSourceId(purchaseOrder.getSourceId());
                invoiceItem.setSourceCode(purchaseOrder.getSourceCode());
                invoiceItem.setSourceType(purchaseOrder.getSourceType());
                // 设置上游信息（采购入库单）
                invoiceItem.setDirectSourceId(purchaseOrder.getOrderId());
                invoiceItem.setDirectSourceCode(purchaseOrder.getSourceCode());
                invoiceItem.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
                invoiceItem.setDirectSourceItemId(purchaseOrderItem.getItemId());
                // 从采购入库单明细复制产品信息和金额信息
                invoiceItem.setProductId(purchaseOrderItem.getProductId());
                invoiceItem.setProductCode(purchaseOrderItem.getProductCode());
                invoiceItem.setProductName(purchaseOrderItem.getProductName());
                invoiceItem.setUnitId(purchaseOrderItem.getUnitId());
                invoiceItem.setUnitCode(purchaseOrderItem.getUnitCode());
                invoiceItem.setUnitName(purchaseOrderItem.getUnitName());
                invoiceItem.setQuantity(purchaseOrderItem.getQuantity());
                invoiceItem.setPrice(purchaseOrderItem.getPrice());
                invoiceItem.setPriceExclusiveTax(purchaseOrderItem.getPriceExclusiveTax());
                invoiceItem.setAmount(purchaseOrderItem.getAmount());
                invoiceItem.setAmountExclusiveTax(purchaseOrderItem.getAmountExclusiveTax());
                invoiceItem.setTaxRate(purchaseOrderItem.getTaxRate());
                invoiceItem.setTaxAmount(purchaseOrderItem.getTaxAmount());
                invoiceItems.add(invoiceItem);
            }
            boolean itemResult = itemMapper.insertBatch(invoiceItems);
            if (!itemResult) {
                throw new ServiceException("生成应付单明细失败");
            }
            invoice.setItems(invoiceItems);
            // 更新应付单主表金额汇总
            summarizeFromItems(invoice);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从采购入库单生成应付单
     *
     * @param purchaseInbound 采购入库单
     * @return 应付单ID
     */
    @Override
    public Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound) {
        try {
            // 校验采购入库单状态
            if (PurchaseInboundStatus.COMPLETED != purchaseInbound.getInboundStatus()) {
                throw new ServiceException("只有已完成的采购入库单才能生成应付单");
            }
            // 检查是否已经生成过应付单
            if (existsByDirectSourceId(purchaseInbound.getInboundId())) {
                throw new ServiceException("该采购入库单已生成应付单，不能重复生成");
            }
            // 创建应付单主记录
            FinApInvoice invoice = new FinApInvoice();
            // 生成应付单编号和名称
            invoice.setInvoiceCode(gen.code(ERP_FIN_AP_INVOICE_CODE));
            invoice.setSummary("采购应付-" + purchaseInbound.getInboundCode() + "-" + purchaseInbound.getSupplierName());
            // 供应商信息
            invoice.setPayeeId(purchaseInbound.getSupplierId());
            invoice.setPayeeName(purchaseInbound.getSupplierName());
            invoice.setPayeeType(FinPayeeType.SUPPLIER);

            invoice.setSourceId(purchaseInbound.getSourceId());
            invoice.setSourceCode(purchaseInbound.getSourceCode());
            invoice.setSourceType(purchaseInbound.getSourceType());
            invoice.setDirectSourceId(purchaseInbound.getInboundId());
            invoice.setDirectSourceCode(purchaseInbound.getInboundCode());
            invoice.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);

            // 设置应付状态
            invoice.setInvoiceStatus(FinApInvoiceStatus.DRAFT);
            invoice.setInvoiceDate(LocalDate.now());
            // 插入应付单主记录
            int result = baseMapper.insert(invoice);
            if (result <= 0) {
                throw new ServiceException("应付单生成失败");
            }
            List<FinApInvoiceItem> invoiceItems = new ArrayList<>();
            // 获取采购入库单明细信息并创建应付单明细
            for (PurchaseInboundItem purchaseInboundItem : purchaseInbound.getItems()) {
                // 创建应付单明细
                FinApInvoiceItem invoiceItem = new FinApInvoiceItem();
                invoiceItem.setInvoiceId(invoice.getInvoiceId());
                // 设置源订单信息（明细表负责维护完整的源订单关联）
                invoiceItem.setSourceId(purchaseInbound.getSourceId());
                invoiceItem.setSourceCode(purchaseInbound.getSourceCode());
                invoiceItem.setSourceType(purchaseInbound.getSourceType());
                // 设置上游信息（采购入库单）
                invoiceItem.setDirectSourceId(purchaseInbound.getInboundId());
                invoiceItem.setDirectSourceCode(purchaseInbound.getInboundCode());
                invoiceItem.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);
                invoiceItem.setDirectSourceItemId(purchaseInboundItem.getItemId());
                // 从采购入库单明细复制产品信息和金额信息
                invoiceItem.setProductId(purchaseInboundItem.getProductId());
                invoiceItem.setProductCode(purchaseInboundItem.getProductCode());
                invoiceItem.setProductName(purchaseInboundItem.getProductName());
                invoiceItem.setUnitId(purchaseInboundItem.getUnitId());
                invoiceItem.setUnitCode(purchaseInboundItem.getUnitCode());
                invoiceItem.setUnitName(purchaseInboundItem.getUnitName());
                invoiceItem.setQuantity(purchaseInboundItem.getQuantity());
                invoiceItem.setPrice(purchaseInboundItem.getPrice());
                invoiceItem.setPriceExclusiveTax(purchaseInboundItem.getPriceExclusiveTax());
                invoiceItem.setAmount(purchaseInboundItem.getAmount());
                invoiceItem.setAmountExclusiveTax(purchaseInboundItem.getAmountExclusiveTax());
                invoiceItem.setTaxRate(purchaseInboundItem.getTaxRate());
                invoiceItem.setTaxAmount(purchaseInboundItem.getTaxAmount());
                invoiceItems.add(invoiceItem);
            }
            boolean itemResult = itemMapper.insertBatch(invoiceItems);
            if (!itemResult) {
                throw new ServiceException("生成应付单明细失败");
            }
            invoice.setItems(invoiceItems);
            // 更新应付单主表金额汇总
            summarizeFromItems(invoice);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 采购入库完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completePurchaseInboundEvent(PurchaseInboundEvent event) {
        try {
            log.info("[completePurchaseInboundEvent] - 采购入库完成事件. event: {}", event);
            PurchaseInbound purchaseInbound = event.getPurchaseInbound();
            boolean result = createFromPurchaseInbound(purchaseInbound);
            if (result) {
                log.info("[completePurchaseInboundEvent] - 采购入库完成事件处理成功. event: {}", event);
            } else {
                log.error("[completePurchaseInboundEvent] - 采购入库完成事件处理失败. event: {}", event);
            }
        } catch (Exception e) {
            log.error("[completePurchaseInboundEvent] - 采购入库完成事件处理错误. event: {}", event, e);
        }
    }


    /**
     * 更新应付单付款状态
     *
     * @param invoiceId 应付单ID
     * @return 是否更新成功
     */
    @Override
    public Boolean updateInvoicePaymentStatus(Long invoiceId) {
        BigDecimal appliedAmount = linkMapper.getAppliedAmountByInvoiceId(invoiceId);
        return updateInvoicePaymentStatus(invoiceId, appliedAmount);
    }

    /**
     * 更新应付单付款状态
     *
     * @param invoiceId     应付单ID
     * @param appliedAmount 核销金额
     * @return 是否更新成功
     */
    @Override
    public Boolean updateInvoicePaymentStatus(Long invoiceId, BigDecimal appliedAmount) {
        FinApInvoice invoice = baseMapper.selectById(invoiceId);
        if (invoice == null) {
            log.error("【应付单】[核销后状态更新] - 失败. 未找到ID为 {} 的应付单", invoiceId);
            throw new ServiceException("更新应付单状态失败：单据不存在");
        }

        // 只有处于未付、部分付、已付状态的单据才需要根据核销金额更新状态
        if (invoice.getInvoiceStatus() != FinApInvoiceStatus.UNPAID &&
            invoice.getInvoiceStatus() != FinApInvoiceStatus.PARTIALLY_PAID &&
            invoice.getInvoiceStatus() != FinApInvoiceStatus.FULLY_PAID) {
            log.info("【应付单】[核销后状态更新] - 跳过. ID: {}, 当前状态 [{}] 无需更新.", invoiceId, invoice.getInvoiceStatus().getDesc());
            return true;
        }

        FinApInvoiceStatus newStatus;

        if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            newStatus = FinApInvoiceStatus.UNPAID; // 如果没有核销，回到未付状态
        } else if (appliedAmount.compareTo(invoice.getUnpaidAmount()) == 0) {
            newStatus = FinApInvoiceStatus.FULLY_PAID;
        } else {
            newStatus = FinApInvoiceStatus.PARTIALLY_PAID;
        }

        if (invoice.getInvoiceStatus() != newStatus) {
            FinApInvoice update = new FinApInvoice();
            update.setInvoiceId(invoiceId);
            update.setPaidAmount(invoice.getPaidAmount().add(appliedAmount));
            update.setUnpaidAmount(invoice.getUnpaidAmount().subtract(appliedAmount));
            update.setInvoiceStatus(newStatus);
            if (baseMapper.updateById(update) > 0) {
                log.info("【应付单】[核销后状态更新] - 成功. ID: {}, 状态由 [{}] 更新为 [{}], 已付金额: {}",
                    invoiceId, invoice.getInvoiceStatus().getDesc(), newStatus.getDesc(), appliedAmount);
            } else {
                log.error("【应付单】[核销后状态更新] - 失败. 数据库更新未生效. ID: {}", invoiceId);
                // 抛出异常以回滚事务
                throw new ServiceException("更新应付单状态失败，数据库操作未生效");
            }
        }
        return true;
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(FinApInvoiceBo bo) {
        // 填充供应商名称
        if (bo.getPayeeId() != null && StringUtils.isEmpty(bo.getPayeeName())) {
            CompanyVo supplier = companyService.queryById(bo.getPayeeId());
            if (supplier != null) {
                bo.setPayeeName(supplier.getCompanyName());
            }
        }
    }

    /**
     * 从明细汇总发票金额
     *
     * @param invoice 发票
     */
    private void summarizeFromItems(FinApInvoice invoice) {
        try {
            if (invoice == null) {
                log.warn("发票数据为空，无法汇总金额");
                return;
            }
            // 查询发票明细
            if (invoice.getItems() == null || invoice.getItems().isEmpty()) {
                log.warn("发票没有明细数据，无法汇总金额 - 发票ID: {}", invoice.getInvoiceId());
                return;
            }
            log.info("开始汇总发票金额 - 发票ID: {}, 明细数量: {}", invoice.getInvoiceId(), invoice.getItems().size());
            // 汇总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalAmountExclusiveTax = BigDecimal.ZERO;
            BigDecimal totalTaxAmount = BigDecimal.ZERO;
            for (FinApInvoiceItem item : invoice.getItems()) {
                // 使用安全的金额加法
                totalAmount = totalAmount.add(item.getAmount());
                totalAmountExclusiveTax = totalAmountExclusiveTax.add(item.getAmountExclusiveTax());
                totalTaxAmount = totalTaxAmount.add(item.getTaxAmount());
                log.debug("累加明细金额 - 明细ID: {}, 产品: {}, 含税金额: {}, 不含税金额: {}, 税额: {}", item.getItemId(), item.getProductName(), item.getAmount(), item.getAmountExclusiveTax(), item.getTaxAmount());
            }
            // 验证汇总金额一致性（使用统一工具类）
            try {
                // 构建临时结果对象进行一致性验证
                TaxCalculationResultBo tempResult = new TaxCalculationResultBo();
                tempResult.setAmount(totalAmount);
                tempResult.setAmountExclusiveTax(totalAmountExclusiveTax);
                tempResult.setTaxAmount(totalTaxAmount);
                if (!TaxCalculationUtils.validateCalculationConsistency(tempResult)) {
                    log.warn("发票汇总金额不一致 - 发票ID: {}, 含税总额: {}, 不含税总额: {}, 税额总计: {}", invoice.getInvoiceId(), totalAmount, totalAmountExclusiveTax, totalTaxAmount);
                    // 重新计算税额以确保一致性
                    totalTaxAmount = totalAmount.subtract(totalAmountExclusiveTax);
                    log.info("重新计算税额 - 发票ID: {}, 修正后税额: {}", invoice.getInvoiceId(), totalTaxAmount);
                }
            } catch (Exception e) {
                log.error("发票汇总金额一致性验证失败 - 发票ID: {}, 错误: {}", invoice.getInvoiceId(), e.getMessage(), e);
            }

            // 记录更新前的金额
            BigDecimal oldAmount = invoice.getAmount();
            BigDecimal oldAmountExclusiveTax = invoice.getAmountExclusiveTax();
            BigDecimal oldTaxAmount = invoice.getTaxAmount();

            // 更新金额
            invoice.setAmount(totalAmount);
            invoice.setAmountExclusiveTax(totalAmountExclusiveTax);
            invoice.setTaxAmount(totalTaxAmount);

            boolean updateResult = baseMapper.updateById(invoice) > 0;

            if (updateResult) {
                log.info("发票金额汇总更新成功 - 发票ID: {}, 发票编号: {}", invoice.getInvoiceId(), invoice.getInvoiceCode());
                log.info("金额变化 - 含税金额: {} -> {}, 不含税金额: {} -> {}, 税额: {} -> {}",
                    oldAmount, totalAmount,
                    oldAmountExclusiveTax, totalAmountExclusiveTax,
                    oldTaxAmount, totalTaxAmount);
            } else {
                throw new ServiceException("发票金额更新失败");
            }

        } catch (Exception e) {
            log.error("发票金额汇总失败 - 发票ID: {}, 错误: {}", invoice.getInvoiceId(), e.getMessage(), e);
            throw new ServiceException("发票金额汇总失败：" + e.getMessage());
        }
    }


    @Override
    public List<FinApInvoiceVo> getUnpaidInvoicesBefore(Long supplierId, LocalDate date) {
        LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApInvoice::getPayeeId, supplierId);
        wrapper.lt(FinApInvoice::getInvoiceDate, date);
        wrapper.in(FinApInvoice::getInvoiceStatus, FinApInvoiceStatus.UNPAID, FinApInvoiceStatus.PARTIALLY_PAID);
        return baseMapper.selectVoList(wrapper);
    }

    // TODO: [发票明细处理方法实现] - 优先级: HIGH - 参考文档: docs/design/README_FINANCE.md
    // 需要实现以下发票明细处理方法：

    /**
     * 处理发票明细数据
     *
     * @param invoiceId 发票ID
     * @param bo        发票业务对象
     */
    private void processInvoiceItems(Long invoiceId, FinApInvoiceBo bo) {
        try {
            // TODO: 实现发票明细处理逻辑
            // 校验明细数据完整性
            // 创建发票明细记录
            // 校验明细汇总金额与主表金额一致性
            // 支持明细级别的税率设置
            log.info("处理发票明细 - 发票ID: {}", invoiceId);
        } catch (Exception e) {
            log.error("处理发票明细失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    // TODO: [核销流程优化] - 优先级: MEDIUM - 参考文档: docs/design/README_FINANCE.md
    // 需要优化核销流程：
    // 实现自动核销算法：按照FIFO原则自动匹配应付单和付款单
    // 支持部分核销：允许一张付款单核销多张应付单，或一张应付单被多张付款单核销
    // 核销撤销功能：支持核销关系的撤销和重新分配
    // 核销差异处理：处理汇率差异、折扣差异等特殊情况
    // 核销审计日志：记录完整的核销操作历史和变更轨迹

    // TODO: [价税分离计算] - 优先级: MEDIUM - 参考文档 docs/design/README_OVERVIEW.md
    // 应付单的价税分离计算应该在明细层面进行，而不是在主单据层面
    // 需要在 FinApInvoiceItemServiceImpl 中实现明细级别的价税分离计算
    // 主单据的金额应该是明细金额的汇总结果

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmInvoice(Long invoiceId) {
        final String currentUser = LoginHelper.getUsername();
        log.info("【应付单】[确认] - 开始. 操作人: {}, ID: {}", currentUser, invoiceId);

        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("确认失败：ID为 [" + invoiceId + "] 的应付单不存在");
            }
            if (invoice.getInvoiceStatus() != FinApInvoiceStatus.DRAFT) {
                throw new ServiceException("确认失败：应付单 [" + invoice.getInvoiceCode() + "] 状态为“" + invoice.getInvoiceStatus().getDesc() + "”，仅草稿状态可确认");
            }

            // TODO: 确认前可以增加更多业务校验，例如明细项是否存在

            FinApInvoice update = new FinApInvoice();
            update.setInvoiceId(invoiceId);
            update.setInvoiceStatus(FinApInvoiceStatus.UNPAID);
            boolean result = baseMapper.updateById(update) > 0;

            if (result) {
                log.info("【应付单】[确认] - 成功. 操作人: {}, 单号: {}", currentUser, invoice.getInvoiceCode());
            } else {
                log.warn("【应付单】[确认] - 警告. 数据库更新未生效。操作人: {}, ID: {}", currentUser, invoiceId);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("【应付单】[确认] - 业务异常. 操作人: {}, ID: {}, 错误: {}", currentUser, invoiceId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("【应付单】[确认] - 系统异常. 操作人: {}, ID: {}, 错误: {}", currentUser, invoiceId, e.getMessage(), e);
            throw new ServiceException(String.format("确认应付单时发生未知系统错误，请联系管理员。错误参考: %s", e.getMessage()));
        }
    }
}
