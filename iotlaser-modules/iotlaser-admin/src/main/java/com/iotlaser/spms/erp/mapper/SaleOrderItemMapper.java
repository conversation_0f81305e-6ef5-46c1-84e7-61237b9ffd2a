package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 销售订单明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface SaleOrderItemMapper extends BaseMapperPlus<SaleOrderItem, SaleOrderItemVo> {

    default List<SaleOrderItem> queryByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapper<SaleOrderItem>().eq(SaleOrderItem::getOrderId, orderId));
    }

    default int deleteByOrderIds(Collection<Long> orderIds) {
        return delete(new LambdaQueryWrapper<SaleOrderItem>().in(SaleOrderItem::getOrderId, orderIds));
    }

    /**
     * 查询销售订单明细表及其关联信息
     */
    SaleOrderItem queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询销售订单明细表及其关联信息
     */
    List<SaleOrderItem> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<SaleOrderItem> wrapper);

    default TaxCalculationResultBo calculateTotalAmount(Long orderId) {
        List<SaleOrderItem> items = Optional.ofNullable(queryByOrderId(orderId))
            .orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (SaleOrderItem item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount());
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax());
            taxAmount = taxAmount.add(item.getTaxAmount());
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }

}
