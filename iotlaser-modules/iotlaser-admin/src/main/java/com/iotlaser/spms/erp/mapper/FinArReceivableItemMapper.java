package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.FinArReceivableItem;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 应收单明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface FinArReceivableItemMapper extends BaseMapperPlus<FinArReceivableItem, FinArReceivableItemVo> {

    default List<FinArReceivableItemVo> queryByReceivableId(Long receivableId) {
        return selectVoList(new LambdaQueryWrapper<FinArReceivableItem>().eq(FinArReceivableItem::getReceivableId, receivableId));
    }

    default int deleteByReceivableIds(Collection<Long> receivableIds) {
        return delete(new LambdaQueryWrapper<FinArReceivableItem>().in(FinArReceivableItem::getReceivableId, receivableIds));
    }

    /**
     * 计算明细总金额
     *
     * @param receivableId 应收单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long receivableId) {
        List<FinArReceivableItemVo> items = Optional.ofNullable(queryByReceivableId(receivableId)).orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (FinArReceivableItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }
}
