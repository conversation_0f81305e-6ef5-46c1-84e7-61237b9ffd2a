package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售订单服务接口
 *
 * <AUTHOR>
 */
public interface ISaleOrderService {

    /**
     * 根据ID查询销售订单
     *
     * @param orderId 销售订单ID
     * @return 销售订单视图对象
     */
    SaleOrderVo queryById(Long orderId);

    /**
     * 查询销售订单分页列表
     *
     * @param bo        查询业务对象
     * @param pageQuery 分页参数
     * @return 销售订单分页数据
     */
    TableDataInfo<SaleOrderVo> queryPageList(SaleOrderBo bo, PageQuery pageQuery);

    /**
     * 查询销售订单列表
     *
     * @param bo 查询业务对象
     * @return 销售订单列表
     */
    List<SaleOrderVo> queryList(SaleOrderBo bo);

    /**
     * 新增销售订单
     *
     * @param bo 包含新销售订单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    SaleOrderVo insertByBo(SaleOrderBo bo);

    /**
     * 修改销售订单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    SaleOrderVo updateByBo(SaleOrderBo bo);

    /**
     * 校验并批量删除销售订单
     *
     * @param ids     待删除的销售订单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认销售订单
     *
     * @param orderId 销售订单ID
     * @return 如果成功则返回true，否则返回false
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 挂起销售订单
     *
     * @param orderId    销售订单ID
     * @param holdReason 挂起原因
     * @return 如果成功则返回true，否则返回false
     */
    Boolean holdOrder(Long orderId, String holdReason);

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 销售订单ID
     * @return 如果成功则返回true，否则返回false
     */
    Boolean resumeOrder(Long orderId);

    /**
     * 取消销售订单
     *
     * @param orderId      销售订单ID
     * @param cancelReason 取消原因
     * @return 如果成功则返回true，否则返回false
     */
    Boolean cancelOrder(Long orderId, String cancelReason);

    /**
     * 关闭销售订单
     *
     * @param orderId 销售订单ID
     * @return 如果成功则返回true，否则返回false
     */
    Boolean closeOrder(Long orderId);

    /**
     * 基于销售订单创建出库单
     *
     * @param orderId 销售订单ID
     * @return 如果成功则返回true，否则返回false
     */
    Boolean createOutbound(Long orderId);

    /**
     * 从销售订单生成应收单
     *
     * @param orderId 销售订单ID
     * @return 如果成功则返回true，否则返回false
     */
    Boolean createReceivable(Long orderId);

}
