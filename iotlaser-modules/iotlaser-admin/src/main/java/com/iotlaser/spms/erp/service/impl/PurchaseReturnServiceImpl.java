package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import com.iotlaser.spms.erp.event.OutboundEvent;
import com.iotlaser.spms.erp.event.PurchaseReturnEvent;
import com.iotlaser.spms.erp.mapper.PurchaseReturnItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseReturnMapper;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_RETURN_CODE;

/**
 * 采购退货服务实现
 *
 * <AUTHOR> Kai
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseReturnServiceImpl implements IPurchaseReturnService {

    private final PurchaseReturnMapper baseMapper;
    private final PurchaseReturnItemMapper itemMapper;
    private final Gen gen;
    private final IOutboundService outboundService;

    // TODO: [REFACTOR] - [MEDIUM] - 此处存在循环依赖(PurchaseReturn -> PurchaseOrder -> PurchaseReturn)，暂时使用@Lazy解决。
    // 理想架构应通过领域事件解耦，例如退货完成后发布`PurchaseReturnCompletedEvent`，由采购订单上下文监听并更新自身。
    @Autowired
    @Lazy
    private IPurchaseOrderService purchaseOrderService;

    /**
     * {@inheritDoc}
     */
    @Override
    public PurchaseReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<PurchaseReturnVo> queryPageList(PurchaseReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        Page<PurchaseReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<PurchaseReturnVo> queryList(PurchaseReturnBo bo) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseReturn> buildQueryWrapper(PurchaseReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(PurchaseReturn::getReturnId);

        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), PurchaseReturn::getReturnCode, bo.getReturnCode());
        lqw.eq(bo.getSourceId() != null, PurchaseReturn::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseReturn::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseReturn::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseReturn::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseReturn::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseReturn::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseReturn::getSupplierId, bo.getSupplierId());
        lqw.eq(bo.getReturnDate() != null, PurchaseReturn::getReturnDate, bo.getReturnDate());
        if (bo.getReturnStatus() != null) {
            lqw.eq(PurchaseReturn::getReturnStatus, bo.getReturnStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseReturn::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseReturn::getSupplierName, bo.getSupplierName());
        lqw.between(params.get("beginReturnDate") != null && params.get("endReturnDate") != null,
            PurchaseReturn::getReturnDate, params.get("beginReturnDate"), params.get("endReturnDate"));
        return lqw;
    }

    /**
     * 新增采购退货单
     *
     * @param bo 包含新采购退货单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseReturnVo insertByBo(PurchaseReturnBo bo) {
        log.info("[新增] 操作人: {}", LoginHelper.getUsername());
        // 初始化退货单编码
        if (StringUtils.isEmpty(bo.getReturnCode())) {
            bo.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));
        }
        // 设置初始状态为草稿
        if (bo.getReturnStatus() == null) {
            bo.setReturnStatus(PurchaseReturnStatus.DRAFT);
        }
        // 设置退货日期为当前日期
        if (bo.getReturnDate() == null) {
            bo.setReturnDate(LocalDate.now());
        }
        // 填充冗余字段
        fillRedundantFields(bo);

        // 转换为实体并进行保存前校验
        PurchaseReturn add = MapstructUtils.convert(bo, PurchaseReturn.class);
        validEntityBeforeSave(add);

        // 插入主表记录
        boolean result = baseMapper.insert(add) > 0;
        if (!result) {
            log.error("[新增] 失败 - 数据库插入操作未生效");
            throw new ServiceException("新增失败");
        }
        return MapstructUtils.convert(add, PurchaseReturnVo.class);
    }

    /**
     * 修改采购退货单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseReturnVo updateByBo(PurchaseReturnBo bo) {
        log.info("[修改] 操作人: {}", LoginHelper.getUsername());
        // 填充冗余字段
        fillRedundantFields(bo);

        // 转换为实体并进行保存前校验
        PurchaseReturn update = MapstructUtils.convert(bo, PurchaseReturn.class);
        validEntityBeforeSave(update);

        // 从明细重新计算总金额
        TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getReturnId());
        update.setAmount(calculated.getAmount());
        update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
        update.setTaxAmount(calculated.getTaxAmount());

        // 更新主表
        boolean result = baseMapper.updateById(update) > 0;
        if (!result) {
            log.warn("[修改] 失败 - 数据库更新未生效, ID: {}", update.getReturnId());
        }
        return MapstructUtils.convert(update, PurchaseReturnVo.class);
    }

    /**
     * 保存前的数据校验。
     *
     * @param entity 待保存的采购退货单实体
     * @throws ServiceException 如果校验失败
     */
    private void validEntityBeforeSave(PurchaseReturn entity) {
        // 业务规则1: 校验退货单编码唯一性
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<PurchaseReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(PurchaseReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("采购退货单编码 [" + entity.getReturnCode() + "] 已存在");
            }
        }
    }

    /**
     * 校验并批量删除采购退货单
     *
     * @param ids     待删除的采购退货单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("[删除] 操作人: {}, ID: {}, 是否校验: {}", LoginHelper.getUsername(), ids, isValid);
        if (isValid) {
            List<PurchaseReturn> returns = baseMapper.selectByIds(ids);
            for (PurchaseReturn purchaseReturn : returns) {
                if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
                    throw new ServiceException("删除失败：退货单 [" + purchaseReturn.getReturnCode() + "] 状态为“" + purchaseReturn.getReturnStatus().getDesc() + "”，仅草稿状态可删除");
                }
            }
        }
        // 级联删除子表
        itemMapper.deleteByOrderIds(ids);
        // 执行批量删除
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        return baseMapper.existsByDirectSourceId(directSourceId, directSourceType);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<PurchaseReturn> queryByDirectSourceId(Long directSourceId) {
        return baseMapper.queryByDirectSourceId(directSourceId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<PurchaseReturnItem> queryItemByReturnId(Long returnId) {
        return itemMapper.queryByReturnId(returnId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReturn(Long returnId) {
        try {
            // 校验退货单是否满足确认条件
            R<PurchaseReturn> returnValid = returnValid(returnId, false);
            if (R.isError(returnValid)) {
                throw new ServiceException(returnValid.getMsg());
            }
            PurchaseReturn purchaseReturn = returnValid.getData();
            if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
                throw new ServiceException("确认失败：采购退货单 [" + purchaseReturn.getReturnCode() + "] 状态为“" + purchaseReturn.getReturnStatus().getName() + "”，仅草稿状态可确认");
            }
            // 重新计算并更新总金额
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(returnId);
            purchaseReturn.setAmount(calculated.getAmount());
            purchaseReturn.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            purchaseReturn.setTaxAmount(calculated.getTaxAmount());
            purchaseReturn.setReturnStatus(PurchaseReturnStatus.PENDING_WAREHOUSE); // 推进状态
            boolean confirmed = baseMapper.updateById(purchaseReturn) > 0;
            // 确认成功后，触发创建仓库出库单
            if (confirmed) {
                log.info("[confirmReturn] - 采购退货单【{}】确认成功", purchaseReturn.getReturnCode());
                createOutboundFromConfirm(purchaseReturn);
            }
            return confirmed;
        } catch (Exception e) {
            log.error("[confirmReturn] - 确认采购入库单异常: ", e);
            throw new ServiceException("确认采购入库单失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmReturns(Collection<Long> returnIds) {
        log.info("[批量确认] 操作人: {}, 数量: {}", LoginHelper.getUsername(), returnIds.size());
        for (Long returnId : returnIds) {
            confirmReturn(returnId);
        }
        return true;
    }

    /**
     * 完成采购退货
     *
     * @param returnId 待完成的采购退货单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeReturn(Long returnId) {
        try {
            R<PurchaseReturn> validBefore = returnValid(returnId, false);
            if (R.isError(validBefore)) {
                log.error("[completeReturn] 失败 - 前置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validBefore.getMsg());
            }
            PurchaseReturn beforeData = validBefore.getData();
            if (beforeData.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
                throw new ServiceException("完成失败：退货单 [" + beforeData.getReturnCode() + "] 状态为“" + beforeData.getReturnStatus().getDesc() + "”，仅待出库状态可完成");
            }
            // 更新beforeData内明细的实际出库数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<PurchaseReturn> validAfter = returnValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.error("[completeReturn] 失败 - 后置验证失败: {}", validAfter.getMsg());
                throw new ServiceException(validAfter.getMsg());
            }
            // 更新采购退货单状态
            PurchaseReturn afterData = validAfter.getData();
            String remark = String.format(" [手动完成-%s]", DateUtils.getTime());
            afterData.setReturnStatus(PurchaseReturnStatus.COMPLETED);
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean completed = baseMapper.updateById(afterData) > 0;
            if (!completed) {
                throw new ServiceException("完成失败");
            }
            triggerNextStreamProcesses(afterData);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReturn(Long returnId, String reason) {
        log.info("[取消] 操作人: {}, ID: {}, 原因: {}", LoginHelper.getUsername(), returnId, reason);
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("取消失败：ID为 [" + returnId + "] 的采购退货单不存在");
        }

        // 校验规则: 只有草稿和待出库状态可以取消
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT &&
            purchaseReturn.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("取消失败：退货单 [" + purchaseReturn.getReturnCode() + "] 状态为“" + purchaseReturn.getReturnStatus().getDesc() + "”，无法取消");
        }

        // 更新状态为已取消，并记录原因
        PurchaseReturn update = new PurchaseReturn();
        update.setReturnId(returnId);
        update.setReturnStatus(PurchaseReturnStatus.CANCELLED);
        if (StringUtils.isNotBlank(reason)) {
            String newRemark = StringUtils.isNotBlank(purchaseReturn.getRemark()) ? purchaseReturn.getRemark() + " | " : "";
            newRemark += "取消原因: " + reason;
            update.setRemark(newRemark);
        }

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 采购退货单确认后创建仓库出库单
     *
     * @param purchaseReturn 采购退货单
     */
    private void createOutboundFromConfirm(PurchaseReturn purchaseReturn) {
        try {
            log.info("[createOutboundFromConfirm] - 开始创建仓库出库单, 来源: {}", purchaseReturn.getReturnCode());
            R<PurchaseReturn> returnValid = returnValid(purchaseReturn, false);
            if (R.isError(returnValid)) {
                throw new ServiceException(returnValid.getMsg());
            }
            boolean createResult = outboundService.createFromPurchaseReturn(purchaseReturn);
            if (!createResult) {
                throw new ServiceException("采购退货单确认后创建仓库出库单失败");
            }
            log.info("[createOutboundFromConfirm] - 仓库出库单创建成功, 来源: {}", purchaseReturn.getReturnCode());
        } catch (Exception e) {
            log.error("[createOutboundFromConfirm] - 创建仓库出库单异常, 来源: {}", purchaseReturn.getReturnCode(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 从采购入库单创建采购退货单
     *
     * @param purchaseInbound 已完成的采购入库单实体
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromPurchaseInbound(PurchaseInbound purchaseInbound) {
        try {
            if (PurchaseInboundStatus.COMPLETED != purchaseInbound.getInboundStatus()) {
                throw new ServiceException("创建失败：采购入库单状态 [" + purchaseInbound.getInboundStatus().getName() + "] ，不能创建");
            }
            boolean exists = baseMapper.existsByDirectSourceId(purchaseInbound.getInboundId(), DirectSourceType.PURCHASE_INBOUND);
            if (exists) {
                throw new ServiceException("创建失败：采购入库单 [" + purchaseInbound.getInboundCode() + "] 已生成过采购退货单，不能重复创建");
            }
            // 创建并初始化退货单主表
            PurchaseReturn add = new PurchaseReturn();
            add.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));
            add.setSourceId(purchaseInbound.getSourceId());
            add.setSourceCode(purchaseInbound.getSourceCode());
            add.setSourceType(purchaseInbound.getSourceType());
            add.setDirectSourceId(purchaseInbound.getInboundId());
            add.setDirectSourceCode(purchaseInbound.getInboundCode());
            add.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);
            add.setSupplierId(purchaseInbound.getSupplierId());
            add.setSupplierName(purchaseInbound.getSupplierName());
            add.setReturnDate(LocalDate.now());
            add.setReturnStatus(PurchaseReturnStatus.DRAFT);
            add.setSummary("[采购入库单" + purchaseInbound.getInboundCode() + "]");

            // 插入主表
            if (!(baseMapper.insert(add) > 0)) {
                throw new ServiceException("创建主记录失败");
            }

            // 根据入库单明细创建退货单明细
            if (purchaseInbound.getItems() != null && !purchaseInbound.getItems().isEmpty()) {
                List<PurchaseReturnItem> returnItems = new ArrayList<>();
                for (PurchaseInboundItem purchaseInboundItem : purchaseInbound.getItems()) {
                    PurchaseReturnItem returnItem = MapstructUtils.convert(purchaseInboundItem, PurchaseReturnItem.class);
                    returnItem.setReturnId(add.getReturnId()); // 关联主表ID
                    returnItem.setRemark("[采购入库单" + purchaseInbound.getInboundCode() + "]");
                    returnItems.add(returnItem);
                }
                if (!itemMapper.insertBatch(returnItems)) {
                    throw new ServiceException("创建明细记录失败");
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 仓库出库完成事件
     *
     * @param event 事件
     */
    @Async
    @EventListener
    public void completeOutboundEvent(OutboundEvent event) {
        try {
            Outbound outbound = event.getOutbound();
            if (outbound == null || outbound.getDirectSourceType() != DirectSourceType.PURCHASE_RETURN) {
                return;
            }
            Long returnId = outbound.getDirectSourceId();
            R<PurchaseReturn> returnValidBefore = returnValid(returnId, false);
            if (R.isError(returnValidBefore)) {
                log.error("[completeOutboundEvent] 失败 - 前置验证失败: {}", returnValidBefore.getMsg());
                return;
            }
            PurchaseReturn beforeData = returnValidBefore.getData();
            if (beforeData.getReturnStatus() == PurchaseReturnStatus.COMPLETED) {
                log.warn("[completeOutboundEvent] 跳过 - 采购出库单已完成，跳过更新: {}", beforeData.getReturnCode());
                return;
            }
            // 更新beforeData内明细的实际出库数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<PurchaseReturn> returnValidAfter = returnValid(beforeData, true);
            if (R.isError(returnValidAfter)) {
                log.error("[completeOutboundEvent] 失败 - 后置验证失败: {}", returnValidAfter.getMsg());
                return;
            }
            // 更新采购退货单状态
            PurchaseReturn afterData = returnValidAfter.getData();
            String remark = String.format(" [自动完成-仓库出库-%s-%s]", outbound.getOutboundCode(), DateUtils.getTime());
            afterData.setReturnStatus(PurchaseReturnStatus.COMPLETED);
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean complete = baseMapper.updateById(afterData) > 0;
            if (!complete) {
                throw new ServiceException("更新失败");
            }
            triggerNextStreamProcesses(afterData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 更新采购退货单明细实退数量
     */
    private void updateActualQuantities(PurchaseReturn purchaseReturn) {
        try {
            List<Outbound> outbounds = outboundService.queryCompleteByDirectSourceId(purchaseReturn.getReturnId(), DirectSourceType.PURCHASE_RETURN);
            Map<Long, BigDecimal> finishQuantityMap = new HashMap<>();
            for (Outbound inbound : outbounds) {
                List<OutboundItem> outboundItems = outboundService.queryItemByOutboundId(inbound.getOutboundId());
                for (OutboundItem inboundItem : outboundItems) {
                    finishQuantityMap.merge(inboundItem.getDirectSourceItemId(), inboundItem.getFinishQuantity(), BigDecimal::add);
                }
            }
            List<PurchaseReturnItem> updates = new ArrayList<>();
            for (PurchaseReturnItem purchaseInboundItem : purchaseReturn.getItems()) {
                BigDecimal finishQuantity = finishQuantityMap.getOrDefault(purchaseInboundItem.getItemId(), BigDecimal.ZERO);
                if (finishQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    purchaseInboundItem.setFinishQuantity(finishQuantity);
                    updates.add(purchaseInboundItem);
                }
            }
            boolean resultItem = itemMapper.updateBatchById(updates);
            if (!resultItem) {
                log.error("更新采购退货单明细实退数量失败 - 采购退货单ID: {}", purchaseReturn.getReturnId());
                throw new ServiceException("更新采购退货单明细实退数量失败");
            }
            log.info("更新采购退货单明细实退数量成功 - 采购退货单ID: {}", purchaseReturn.getReturnId());
        } catch (Exception e) {
            log.error("更新采购退货单明细实退数量失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("更新采购退货单明细实退数量失败：" + e.getMessage());
        }
    }

    /**
     * 触发后续业务流程
     */
    private void triggerNextStreamProcesses(PurchaseReturn purchaseReturn) {
        // 触发采购退货完成事件
        try {
            PurchaseReturnEvent event = new PurchaseReturnEvent();
            event.setPurchaseReturn(purchaseReturn);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error("[triggerNextStreamProcesses] - 异常, 数据: {}", purchaseReturn, e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 确认和完成采购退货的前置统一验证。
     *
     * @param returnId   采购退货ID
     * @param isComplete 是否为“完成”操作的校验。true表示需要校验实退数量，false表示不需要。
     * @return 校验通过的退货单信息VO（包含明细）
     */
    private R<PurchaseReturn> returnValid(Long returnId, boolean isComplete) {
        if (returnId == null) {
            return R.fail("校验失败：必须提供采购退货单ID");
        }
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        return returnValid(purchaseReturn, isComplete);
    }

    /**
     * 确认和完成采购退货的前置统一验证。
     *
     * @param purchaseReturn 采购退货
     * @param isComplete     是否为“完成”操作的校验。true表示需要校验实退数量，false表示不需要。
     * @return 校验通过的退货单信息VO（包含明细）
     */
    private R<PurchaseReturn> returnValid(PurchaseReturn purchaseReturn, boolean isComplete) {
        if (purchaseReturn == null) {
            return R.fail("校验失败：采购退货单不存在");
        }
        List<PurchaseReturnItem> purchaseReturnItems = purchaseReturn.getItems();
        if (purchaseReturnItems == null || purchaseReturnItems.isEmpty()) {
            purchaseReturnItems = itemMapper.queryByReturnId(purchaseReturn.getReturnId());
            if (purchaseReturnItems == null || purchaseReturnItems.isEmpty()) {
                return R.fail("校验失败：退货单 [" + purchaseReturn.getReturnCode() + "] 没有明细项，无法操作");
            }
        }
        for (PurchaseReturnItem purchaseReturnItem : purchaseReturnItems) {
            if (purchaseReturnItem.getQuantity() == null || purchaseReturnItem.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("校验失败：明细项 [" + purchaseReturnItem.getProductName() + "] 的应退数量必须大于0");
            }
            if (isComplete) {
                if (purchaseReturnItem.getFinishQuantity() == null || purchaseReturnItem.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    return R.fail("校验失败：明细项 [" + purchaseReturnItem.getProductName() + "] 的实退数量必须大于0才能完成");
                }
                if (purchaseReturnItem.getQuantity().compareTo(purchaseReturnItem.getFinishQuantity()) != 0) {
                    return R.fail("校验失败：明细项 [" + purchaseReturnItem.getProductName() + "] 的实退数量与应退数量不符");
                }
            }
        }
        purchaseReturn.setItems(purchaseReturnItems);
        return R.ok(purchaseReturn);
    }

    /**
     * 填充订单中的冗余字段，如供应商名称、经办人信息等。
     *
     * @param bo 待填充的业务对象
     */
    private void fillRedundantFields(PurchaseReturnBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            if (bo.getReturnId() == null) { // 新增时记录申请人
                bo.setApplicantId(loginUser.getUserId());
                bo.setApplicantName(loginUser.getNickname());
            }
            bo.setHandlerId(loginUser.getUserId()); // 每次操作都更新经办人
            bo.setHandlerName(loginUser.getNickname());
        }
    }
}
