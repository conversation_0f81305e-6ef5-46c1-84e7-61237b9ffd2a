package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptOrderVo;
import com.iotlaser.spms.erp.service.IFinArReceiptOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收款单
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finArReceiptOrder")
public class FinArReceiptOrderController extends BaseController {

    private final IFinArReceiptOrderService finArReceiptOrderService;

    /**
     * 查询收款单列表
     */
    @SaCheckPermission("erp:finArReceiptOrder:list")
    @GetMapping("/list")
    public TableDataInfo<FinArReceiptOrderVo> list(FinArReceiptOrderBo bo, PageQuery pageQuery) {
        return finArReceiptOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出收款单列表
     */
    @SaCheckPermission("erp:finArReceiptOrder:export")
    @Log(title = "收款单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinArReceiptOrderBo bo, HttpServletResponse response) {
        List<FinArReceiptOrderVo> list = finArReceiptOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "收款单", FinArReceiptOrderVo.class, response);
    }

    /**
     * 获取收款单详细信息
     *
     * @param receiptId 主键
     */
    @SaCheckPermission("erp:finArReceiptOrder:query")
    @GetMapping("/{receiptId}")
    public R<FinArReceiptOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long receiptId) {
        return R.ok(finArReceiptOrderService.queryById(receiptId));
    }

    /**
     * 新增收款单
     */
    @SaCheckPermission("erp:finArReceiptOrder:add")
    @Log(title = "收款单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<FinArReceiptOrderVo> add(@Validated(AddGroup.class) @RequestBody FinArReceiptOrderBo bo) {
        return R.ok(finArReceiptOrderService.insertByBo(bo));
    }

    /**
     * 修改收款单
     */
    @SaCheckPermission("erp:finArReceiptOrder:edit")
    @Log(title = "收款单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<FinArReceiptOrderVo> edit(@Validated(EditGroup.class) @RequestBody FinArReceiptOrderBo bo) {
        return R.ok(finArReceiptOrderService.updateByBo(bo));
    }

    /**
     * 删除收款单
     *
     * @param receiptIds 主键串
     */
    @SaCheckPermission("erp:finArReceiptOrder:remove")
    @Log(title = "收款单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{receiptIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] receiptIds) {
        return toAjax(finArReceiptOrderService.deleteWithValidByIds(List.of(receiptIds), true));
    }
}
