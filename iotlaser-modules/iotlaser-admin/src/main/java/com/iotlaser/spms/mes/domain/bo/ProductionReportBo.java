package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionReport;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产报工记录业务对象 mes_production_report
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionReport.class, reverseConvertGenerate = false)
public class ProductionReportBo extends BaseEntity {

    /**
     * 报工ID
     */
    private Long reportId;

    /**
     * 生产订单ID
     */
    @NotNull(message = "生产订单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 实例ID
     */
    @NotNull(message = "实例ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long instanceId;

    /**
     * 步骤ID
     */
    @NotNull(message = "步骤ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long stepId;

    /**
     * 工序ID
     */
    @NotNull(message = "工序ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 报工类型
     */
    private String reportType;

    /**
     * 良品数量
     */
    private BigDecimal quantityGood;

    /**
     * 不良品数量
     */
    private BigDecimal quantityBad;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 报工操作员ID
     */
    private Long operatorId;

    /**
     * 报工操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
