package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.InventoryCheck;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 库存盘点业务对象 wms_inventory_check
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryCheck.class, reverseConvertGenerate = false)
public class InventoryCheckBo extends BaseEntity {

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 盘点编码
     */
    private String checkCode;

    /**
     * 盘点名称
     */
    private String checkName;

    /**
     * 盘点类型
     */
    private String checkType;

    /**
     * 盘点范围
     */
    private String checkScope;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际完成时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 盘点状态
     */
    @NotBlank(message = "盘点状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String checkStatus;

    /**
     * 盘点负责人 ID
     */
    private Long supervisorId;

    /**
     * 盘点负责人
     */
    private String supervisorName;

    /**
     * 参与盘点人员IDS
     */
    private String operatorIds;

    /**
     * 参与盘点人员
     */
    private String operatorNames;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
