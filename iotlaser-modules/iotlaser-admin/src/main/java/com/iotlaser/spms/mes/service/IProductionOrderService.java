package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionOrderBo;
import com.iotlaser.spms.mes.domain.vo.ProductionOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产订单Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-04-23
 */
public interface IProductionOrderService {

    /**
     * 查询生产订单
     *
     * @param orderId 主键
     * @return 生产订单
     */
    ProductionOrderVo queryById(Long orderId);

    /**
     * 分页查询生产订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产订单分页列表
     */
    TableDataInfo<ProductionOrderVo> queryPageList(ProductionOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产订单列表
     *
     * @param bo 查询条件
     * @return 生产订单列表
     */
    List<ProductionOrderVo> queryList(ProductionOrderBo bo);

    /**
     * 新增生产订单
     *
     * @param bo 生产订单
     * @return 创建的生产订单
     */
    ProductionOrderVo insertByBo(ProductionOrderBo bo);

    /**
     * 修改生产订单
     *
     * @param bo 生产订单
     * @return 修改后的生产订单
     */
    ProductionOrderVo updateByBo(ProductionOrderBo bo);

    /**
     * 校验并批量删除生产订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 下达生产订单
     *
     * @param orderId 订单ID
     * @return 是否下达成功
     */
    Boolean releaseOrder(Long orderId);

    /**
     * 批量下达生产订单
     *
     * @param orderIds 订单ID集合
     * @return 是否下达成功
     */
    Boolean batchReleaseOrders(Collection<Long> orderIds);

    /**
     * 开始生产
     *
     * @param orderId 订单ID
     * @return 是否开始成功
     */
    Boolean startProduction(Long orderId);

    /**
     * 完工入库
     *
     * @param orderId        订单ID
     * @param finishQuantity 完工数量
     * @return 是否完工成功
     */
    Boolean finishProduction(Long orderId, java.math.BigDecimal finishQuantity);

    /**
     * 关闭生产订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    Boolean closeOrder(Long orderId);

    /**
     * 根据销售订单创建生产订单
     *
     * @param saleOrderId 销售订单ID
     * @return 创建的生产订单
     */
    Boolean createFromSaleOrder(Long saleOrderId);

    /**
     * 确认生产订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 批量确认生产订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOrders(Collection<Long> orderIds);

    /**
     * 取消生产订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    Boolean cancelOrder(Long orderId, String cancelReason);

    /**
     * 暂停生产
     *
     * @param orderId     订单ID
     * @param pauseReason 暂停原因
     * @return 是否暂停成功
     */
    Boolean pauseProduction(Long orderId, String pauseReason);

    /**
     * 恢复生产
     *
     * @param orderId 订单ID
     * @return 是否恢复成功
     */
    Boolean resumeProduction(Long orderId);

    /**
     * 完成生产
     *
     * @param orderId 订单ID
     * @return 是否完成成功
     */
    Boolean completeProduction(Long orderId);
}
