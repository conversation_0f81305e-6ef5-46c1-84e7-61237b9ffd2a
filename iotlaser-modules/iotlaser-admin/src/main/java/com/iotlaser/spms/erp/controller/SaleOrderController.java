package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOrder")
public class SaleOrderController extends BaseController {

    private final ISaleOrderService saleOrderService;

    /**
     * 查询销售订单列表
     *
     * @param bo        查询业务对象
     * @param pageQuery 分页参数
     * @return 分页列表
     */
    @SaCheckPermission("erp:saleOrder:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOrderVo> list(SaleOrderBo bo, PageQuery pageQuery) {
        return saleOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售订单列表
     *
     * @param bo       查询业务对象
     * @param response HTTP响应
     */
    @SaCheckPermission("erp:saleOrder:export")
    @Log(title = "销售订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOrderBo bo, HttpServletResponse response) {
        List<SaleOrderVo> list = saleOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售订单", SaleOrderVo.class, response);
    }

    /**
     * 根据ID获取销售订单详细信息
     *
     * @param orderId 销售订单ID
     * @return 详细信息
     */
    @SaCheckPermission("erp:saleOrder:query")
    @GetMapping("/{orderId}")
    public R<SaleOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        return R.ok(saleOrderService.queryById(orderId));
    }

    /**
     * 新增销售订单
     *
     * @param bo 包含新销售订单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @SaCheckPermission("erp:saleOrder:add")
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<SaleOrderVo> add(@Validated(AddGroup.class) @RequestBody SaleOrderBo bo) {
        return R.ok(saleOrderService.insertByBo(bo));
    }

    /**
     * 修改销售订单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SaleOrderVo> edit(@Validated(EditGroup.class) @RequestBody SaleOrderBo bo) {
        return R.ok(saleOrderService.updateByBo(bo));
    }

    /**
     * 删除销售订单
     *
     * @param orderIds 待删除的销售订单ID列表
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:remove")
    @Log(title = "销售订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] orderIds) {
        return toAjax(saleOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 确认销售订单
     *
     * @param orderId 销售订单ID
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    public R<Void> confirmOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.confirmOrder(orderId));
    }

    /**
     * 挂起销售订单
     *
     * @param orderId    销售订单ID
     * @param holdReason 挂起原因
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/hold/{orderId}")
    public R<Void> holdOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                             @RequestParam(required = false) String holdReason) {
        return toAjax(saleOrderService.holdOrder(orderId, holdReason));
    }

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 销售订单ID
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{orderId}")
    public R<Void> resumeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.resumeOrder(orderId));
    }

    /**
     * 取消销售订单
     *
     * @param orderId      销售订单ID
     * @param cancelReason 取消原因
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancelOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                               @RequestParam(required = false) String cancelReason) {
        return toAjax(saleOrderService.cancelOrder(orderId, cancelReason));
    }

    /**
     * 关闭销售订单
     *
     * @param orderId 销售订单ID
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> closeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.closeOrder(orderId));
    }

    /**
     * 从销售订单创建应收单
     *
     * @param orderId 销售订单ID
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @PostMapping("/createReceivable/{orderId}")
    public R<Void> createReceivable(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.createReceivable(orderId));
    }

    /**
     * 从销售订单创建出库单
     *
     * @param orderId 销售订单ID
     * @return 操作结果
     */
    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @PostMapping("/createOutbound/{orderId}")
    public R<Void> createOutbound(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.createOutbound(orderId));
    }

    // TODO: [销售订单状态流转接口] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
    // 需要添加以下状态流转接口：
    // 提交审核: POST /submit/{orderId} - 将订单从 DRAFT 转为 PENDING_APPROVAL
    // 审核通过: POST /approve/{orderId} - 将订单从 PENDING_APPROVAL 转为 CONFIRMED
    // 审核驳回: POST /reject/{orderId} - 将订单从 PENDING_APPROVAL 转为 DRAFT
    // 取消订单: POST /cancel/{orderId} - 将订单转为 CANCELLED 状态
    // 完成订单: POST /complete/{orderId} - 将订单转为 COMPLETED 状态
    // 实现思路：每个接口调用对应的 Service 方法，包含状态校验和业务逻辑

    // TODO: [销售订单批量操作接口] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
    // 需要添加批量操作接口：
    // 批量审核: POST /batchApprove - 批量审核多个订单
    // 批量取消: POST /batchCancel - 批量取消多个订单
    // 批量创建出库单: POST /batchCreateOutbound - 为多个订单批量创建出库单
    // 实现思路：接收订单ID列表，循环调用单个操作方法，支持部分成功的结果返回

    // TODO: [销售订单统计分析接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加统计分析接口：
    // 订单统计: GET /statistics - 按状态、时间、客户等维度统计订单数量和金额
    // 销售趋势: GET /trends - 销售趋势分析，支持按月、季度、年度统计
    // 客户分析: GET /customerAnalysis - 客户销售排行和分析
    // 实现思路：使用聚合查询，返回统计数据和图表所需的数据结构
}
