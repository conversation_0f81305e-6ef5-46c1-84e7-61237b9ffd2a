package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionOrderBo;
import com.iotlaser.spms.mes.domain.vo.ProductionOrderVo;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产订单
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionOrder")
public class ProductionOrderController extends BaseController {

    private final IProductionOrderService productionOrderService;

    /**
     * 查询生产订单列表
     */
    @SaCheckPermission("mes:productionOrder:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionOrderVo> list(ProductionOrderBo bo, PageQuery pageQuery) {
        return productionOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产订单列表
     */
    @SaCheckPermission("mes:productionOrder:export")
    @Log(title = "生产订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionOrderBo bo, HttpServletResponse response) {
        List<ProductionOrderVo> list = productionOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产订单", ProductionOrderVo.class, response);
    }

    /**
     * 获取生产订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("mes:productionOrder:query")
    @GetMapping("/{orderId}")
    public R<ProductionOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long orderId) {
        return R.ok(productionOrderService.queryById(orderId));
    }

    /**
     * 新增生产订单
     */
    @SaCheckPermission("mes:productionOrder:add")
    @Log(title = "生产订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<ProductionOrderVo> add(@Validated(AddGroup.class) @RequestBody ProductionOrderBo bo) {
        return R.ok(productionOrderService.insertByBo(bo));
    }

    /**
     * 修改生产订单
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "生产订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<ProductionOrderVo> edit(@Validated(EditGroup.class) @RequestBody ProductionOrderBo bo) {
        return R.ok(productionOrderService.updateByBo(bo));
    }

    /**
     * 删除生产订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("mes:productionOrder:remove")
    @Log(title = "生产订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(productionOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 下达生产订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "下达生产订单", businessType = BusinessType.UPDATE)
    @PostMapping("/release/{orderId}")
    public R<Void> release(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(productionOrderService.releaseOrder(orderId));
    }

    /**
     * 批量下达生产订单
     *
     * @param orderIds 订单ID集合
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "批量下达生产订单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchRelease")
    public R<Void> batchRelease(@NotEmpty(message = "订单ID不能为空") @RequestBody Long[] orderIds) {
        return toAjax(productionOrderService.batchReleaseOrders(List.of(orderIds)));
    }

    /**
     * 开始生产
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "开始生产", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{orderId}")
    public R<Void> start(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(productionOrderService.startProduction(orderId));
    }

    /**
     * 完工入库
     *
     * @param orderId        订单ID
     * @param finishQuantity 完工数量
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "完工入库", businessType = BusinessType.UPDATE)
    @PostMapping("/finish/{orderId}")
    public R<Void> finish(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                          @NotNull(message = "完工数量不能为空") @RequestParam java.math.BigDecimal finishQuantity) {
        return toAjax(productionOrderService.finishProduction(orderId, finishQuantity));
    }

    /**
     * 关闭生产订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("mes:productionOrder:edit")
    @Log(title = "关闭生产订单", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> close(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(productionOrderService.closeOrder(orderId));
    }

    /**
     * 根据销售订单创建生产订单
     *
     * @param saleOrderId 销售订单ID
     */
    @SaCheckPermission("mes:productionOrder:add")
    @Log(title = "从销售订单创建生产订单", businessType = BusinessType.INSERT)
    @PostMapping("/createFromSaleOrder/{saleOrderId}")
    public R<Void> createFromSaleOrder(@NotNull(message = "销售订单ID不能为空") @PathVariable Long saleOrderId) {
        return toAjax(productionOrderService.createFromSaleOrder(saleOrderId));
    }
}
