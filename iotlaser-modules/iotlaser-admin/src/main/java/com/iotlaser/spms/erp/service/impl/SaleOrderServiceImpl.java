package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.event.SaleOutboundEvent;
import com.iotlaser.spms.erp.event.SaleReturnEvent;
import com.iotlaser.spms.erp.mapper.SaleOrderItemMapper;
import com.iotlaser.spms.erp.mapper.SaleOrderMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.UserService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_SALE_ORDER_CODE;

/**
 * 销售订单服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOrderServiceImpl implements ISaleOrderService {

    private final SaleOrderMapper baseMapper;
    private final SaleOrderItemMapper itemMapper;
    private final ISaleOutboundService saleOutboundService;
    private final ISaleReturnService saleReturnService;
    private final ICompanyService companyService;
    private final IProductService productService;
    private final Gen gen;

    @Lazy
    @Autowired
    private IInventoryService inventoryService;
    @Lazy
    @Autowired
    private IFinArReceivableService finArReceivableService;
    @Lazy
    @Autowired
    private IFinancialReconciliationService financialReconciliationService;

    /**
     * {@inheritDoc}
     */
    @Override
    public SaleOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<SaleOrderVo> queryPageList(SaleOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOrder> lqw = buildQueryWrapper(bo);
        Page<SaleOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SaleOrderVo> queryList(SaleOrderBo bo) {
        LambdaQueryWrapper<SaleOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询包装器
     *
     * @param bo 查询业务对象
     * @return 查询包装器
     */
    private LambdaQueryWrapper<SaleOrder> buildQueryWrapper(SaleOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(SaleOrder::getOrderId); // 默认按ID降序
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), SaleOrder::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getSourceId() != null, SaleOrder::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), SaleOrder::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(SaleOrder::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, SaleOrder::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), SaleOrder::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(SaleOrder::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getCustomerId() != null, SaleOrder::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleOrder::getCustomerName, bo.getCustomerName());
        if (bo.getOrderStatus() != null) {
            lqw.eq(SaleOrder::getOrderStatus, bo.getOrderStatus());
        }
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            SaleOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOrderVo insertByBo(SaleOrderBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getOrderCode())) {
                bo.setOrderCode(gen.code(ERP_SALE_ORDER_CODE));
            }
            if (bo.getOrderStatus() == null) {
                bo.setOrderStatus(SaleOrderStatus.DRAFT);
            }
            if (bo.getOrderDate() == null) {
                bo.setOrderDate(LocalDate.now());
            }
            fillRedundantFields(bo);
            SaleOrder add = MapstructUtils.convert(bo, SaleOrder.class);
            validEntityBeforeSave(add);

            if (!(baseMapper.insert(add) > 0)) {
                throw new ServiceException("新增失败");
            }

            // 回填源单信息
            if (add.getSourceType() == null || add.getSourceType() == SourceType.SALE_ORDER) {
                add.setSourceId(add.getOrderId());
                add.setSourceCode(add.getOrderCode());
                add.setSourceType(SourceType.SALE_ORDER);
            }
            if (add.getDirectSourceType() == null || add.getDirectSourceType() == DirectSourceType.SALE_ORDER) {
                add.setDirectSourceId(add.getOrderId());
                add.setDirectSourceCode(add.getOrderCode());
                add.setDirectSourceType(DirectSourceType.SALE_ORDER);
            }
            baseMapper.updateById(add);
            log.info("[insertByBo] - 新增成功, 订单号: {}", add.getOrderCode());
            return MapstructUtils.convert(add, SaleOrderVo.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOrderVo updateByBo(SaleOrderBo bo) {
        try {
            fillRedundantFields(bo);
            SaleOrder update = MapstructUtils.convert(bo, SaleOrder.class);
            validEntityBeforeSave(update);

            // 修改时重新计算总金额
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getOrderId());
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());

            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("修改失败");
            }
            log.info("[updateByBo] - 修改成功, 订单号: {}", update.getOrderCode());
            return MapstructUtils.convert(update, SaleOrderVo.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 保存前实体校验
     */
    private void validEntityBeforeSave(SaleOrder entity) {
        if (StringUtils.isNotBlank(entity.getOrderCode())) {
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOrder::getOrderCode, entity.getOrderCode());
            if (entity.getOrderId() != null) {
                wrapper.ne(SaleOrder::getOrderId, entity.getOrderId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("销售订单编码 [" + entity.getOrderCode() + "] 已存在");
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        try {
            if (isValid) {
                List<SaleOrder> orders = baseMapper.selectByIds(ids);
                for (SaleOrder order : orders) {
                    if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                        throw new ServiceException("删除失败：订单 [" + order.getOrderCode() + "] 不是草稿状态");
                    }
                    if (saleOutboundService.existsByDirectSourceId(order.getOrderId())) {
                        throw new ServiceException("删除失败：订单 [" + order.getOrderCode() + "] 已关联出库单");
                    }
                }
            }
            itemMapper.deleteByOrderIds(ids);
            if (!(baseMapper.deleteByIds(ids) > 0)) {
                throw new ServiceException("删除失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            if (SaleOrderStatus.DRAFT != saleOrder.getOrderStatus()) {
                throw new ServiceException("确认失败：订单 [" + saleOrder.getOrderCode() + "] 不是草稿状态");
            }
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(saleOrder.getOrderId());
            SaleOrder update = new SaleOrder();
            update.setOrderId(orderId);
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());
            update.setOrderStatus(SaleOrderStatus.CONFIRMED);
            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("确认失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean holdOrder(Long orderId, String holdReason) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            if (SaleOrderStatus.CONFIRMED != saleOrder.getOrderStatus()) {
                throw new ServiceException("挂起失败：订单 [" + saleOrder.getOrderCode() + "] 不是已确认状态");
            }
            SaleOrder update = new SaleOrder();
            update.setOrderId(orderId);
            update.setOrderStatus(SaleOrderStatus.ON_HOLD);
            if (StringUtils.isNotBlank(holdReason)) {
                String currentRemark = StringUtils.isNotBlank(saleOrder.getRemark()) ? saleOrder.getRemark() + " | " : "";
                update.setRemark(currentRemark + "挂起原因: " + holdReason);
            }
            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("挂起失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resumeOrder(Long orderId) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            if (SaleOrderStatus.ON_HOLD != saleOrder.getOrderStatus()) {
                throw new ServiceException("恢复失败：订单 [" + saleOrder.getOrderCode() + "] 不是挂起状态");
            }
            SaleOrder update = new SaleOrder();
            update.setOrderId(orderId);
            update.setOrderStatus(SaleOrderStatus.CONFIRMED);
            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("恢复失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, String cancelReason) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("取消失败：订单ID [" + orderId + "] 不存在");
            }
            if (saleOutboundService.existsByDirectSourceId(orderId)) {
                throw new ServiceException("取消失败：订单 [" + order.getOrderCode() + "] 已关联出库记录");
            }
            SaleOrder update = new SaleOrder();
            update.setOrderId(orderId);
            update.setOrderStatus(SaleOrderStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() + " | " : "";
                update.setRemark(currentRemark + "取消原因: " + cancelReason);
            }
            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("取消失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrder(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("关闭失败：订单ID [" + orderId + "] 不存在");
            }
            if (SaleOrderStatus.FULLY_SHIPPED != order.getOrderStatus()) {
                throw new ServiceException("关闭失败：订单 [" + order.getOrderCode() + "] 不是全部发货状态");
            }
            SaleOrder update = new SaleOrder();
            update.setOrderId(orderId);
            update.setOrderStatus(SaleOrderStatus.CLOSED);
            if (!(baseMapper.updateById(update) > 0)) {
                throw new ServiceException("关闭失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 完成销售订单
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeOrder(Long orderId) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            if (SaleOrderStatus.FULLY_SHIPPED != saleOrder.getOrderStatus()) {
                throw new ServiceException("完成失败：订单 [" + saleOrder.getOrderCode() + "] 不是全部发货状态");
            }
            // 检查订单是否全部发货
            checkAllOutboundCompleted(orderId);
            saleOrder.setOrderStatus(SaleOrderStatus.COMPLETED);
            if (!(baseMapper.updateById(saleOrder) > 0)) {
                throw new ServiceException("完成失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 检查所有关联出库单是否都已完成
     */
    private void checkAllOutboundCompleted(Long orderId) {
        List<SaleOutbound> saleOutbounds = saleOutboundService.queryByDirectSourceId(orderId);
        for (SaleOutbound saleOutbound : saleOutbounds) {
            if(SaleOutboundStatus.COMPLETED!=saleOutbound.getOutboundStatus()) {
                throw new ServiceException("销售出库单[" +saleOutbound.getOutboundCode()+ "]未完成，状态: " + saleOutbound.getOutboundStatus().getName());
            }
        }
    }

    /**
     * 检查库存是否可用
     */
    private void checkInventoryAvailable(Long orderId) {
        List<SaleOrderItem> saleOrderItems = itemMapper.queryByOrderId(orderId);
        for (SaleOrderItem item : saleOrderItems) {
            BigDecimal availableQuantity = inventoryService.sumAvailableQuantityByProductId(item.getProductId());
            if (availableQuantity == null || availableQuantity.compareTo(item.getQuantity()) < 0) {
                throw new ServiceException(String.format("产品【%s】的库存不足，需要：%s，可用：%s",
                    item.getProductName(), item.getQuantity(), availableQuantity != null ? availableQuantity : BigDecimal.ZERO));
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReceivable(Long orderId) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            boolean result = finArReceivableService.createFromSaleOrder(saleOrder);
            if (!result) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 手动从销售订单单创建销售出库单
     *
     * @param orderId 销售订单ID
     * @return 操作成功返回 {@code true}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutbound(Long orderId) {
        try {
            R<SaleOrder> saleOrderValid = saleOrderValid(orderId);
            if(R.isError(saleOrderValid)) {
                throw new ServiceException(saleOrderValid.getMsg());
            }
            SaleOrder saleOrder = saleOrderValid.getData();
            // 检查库存
            checkInventoryAvailable(orderId);
            if (!saleOutboundService.createFromSaleOrder(saleOrder)) {
                throw new ServiceException("创建失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 销售出库完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeSaleOutboundEvent(SaleOutboundEvent event) {
        try {
            Long orderId = event.getSaleOutbound().getDirectSourceId();
            List<SaleOutbound> saleOutbounds = saleOutboundService.queryByDirectSourceId(orderId);
            Map<Long, BigDecimal> outboundQuantityMap = new HashMap<>();
            if (saleOutbounds != null && !saleOutbounds.isEmpty()) {
                for (SaleOutbound saleOutbound : saleOutbounds) {
                    List<SaleOutboundItem> outboundItems = saleOutboundService.queryItemByOutboundId(saleOutbound.getOutboundId());
                    if (outboundItems != null && !outboundItems.isEmpty()) {
                        for (SaleOutboundItem outboundItem : outboundItems) {
                            if (outboundItem.getFinishQuantity() != null && outboundItem.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                                outboundQuantityMap.merge(outboundItem.getProductId(), outboundItem.getFinishQuantity(), BigDecimal::add);
                            }
                        }
                    }
                }
            }
            //更新和填充销售订单明细已发货数量
            List<SaleOrderItem> saleOrderItems = itemMapper.queryByOrderId(orderId);
            List<SaleOrderItem> updates = new ArrayList<>();
            for (SaleOrderItem saleOrderItem : saleOrderItems) {
                BigDecimal shippedQty = outboundQuantityMap.getOrDefault(saleOrderItem.getProductId(), BigDecimal.ZERO);
                if (saleOrderItem.getShippedQuantity() == null || saleOrderItem.getShippedQuantity().compareTo(shippedQty) != 0) {
                    saleOrderItem.setShippedQuantity(shippedQty);
                    updates.add(saleOrderItem);
                }
            }
            if (!itemMapper.updateBatchById(updates)) {
                throw new ServiceException("批量更新销售订单行出库数量失败");
            }

            BigDecimal totalQty = saleOrderItems.stream().map(SaleOrderItem::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalShippedQty = saleOrderItems.stream().map(SaleOrderItem::getShippedQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            SaleOrderStatus newStatus = null;
            if (totalShippedQty.compareTo(BigDecimal.ZERO) > 0 && totalShippedQty.compareTo(totalQty) < 0) {
                newStatus = SaleOrderStatus.PARTIALLY_SHIPPED;
            } else if (totalShippedQty.compareTo(totalQty) >= 0) {
                newStatus = SaleOrderStatus.FULLY_SHIPPED;
            }

            if (newStatus != null) {
                SaleOrder currentOrder = baseMapper.selectById(orderId);
                if (currentOrder != null && currentOrder.getOrderStatus() != newStatus) {
                    SaleOrder orderUpdate = new SaleOrder();
                    orderUpdate.setOrderId(orderId);
                    orderUpdate.setOrderStatus(newStatus);
                    if (baseMapper.updateById(orderUpdate) > 0) {
                        log.info("[completeSaleOutboundEvent] - 状态更新成功. orderId: {}, from: {}, to: {}", orderId, currentOrder.getOrderStatus().getDesc(), newStatus.getDesc());
                    } else {
                        log.error("[completeSaleOutboundEvent] - 状态更新失败. orderId: {}", orderId);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 销售退货完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeSaleReturnEvent(SaleReturnEvent event) {
        try {
            log.info("[completeSaleReturnEvent] - 开始. event: {}", event);
            Long orderId = event.getSaleReturn().getSourceId();
            List<SaleReturn> saleReturns = saleReturnService.queryByDirectSourceId(orderId);
            Map<Long, BigDecimal> returnQuantityMap = new HashMap<>();
            if (saleReturns != null && !saleReturns.isEmpty()) {
                for (SaleReturn saleReturn : saleReturns) {
                    List<SaleReturnItem> saleReturnItems = saleReturnService.queryItemByReturnId(saleReturn.getReturnId());
                    if (saleReturnItems != null && !saleReturnItems.isEmpty()) {
                        for (SaleReturnItem saleReturnItem : saleReturnItems) {
                            if (saleReturnItem.getFinishQuantity() != null && saleReturnItem.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                                returnQuantityMap.merge(saleReturnItem.getProductId(), saleReturnItem.getFinishQuantity(), BigDecimal::add);
                            }
                        }
                    }
                }
            }

            List<SaleOrderItem> saleOrderItems = itemMapper.queryByOrderId(orderId);
            List<SaleOrderItem> updates = new ArrayList<>();
            for (SaleOrderItem saleOrderItem : saleOrderItems) {
                BigDecimal returnedQty = returnQuantityMap.getOrDefault(saleOrderItem.getProductId(), BigDecimal.ZERO);
                if (saleOrderItem.getReturnedQuantity() == null || saleOrderItem.getReturnedQuantity().compareTo(returnedQty) != 0) {
                    saleOrderItem.setReturnedQuantity(returnedQty);
                    updates.add(saleOrderItem);
                }
            }
            if (!updates.isEmpty()) {
                if (!itemMapper.updateBatchById(updates)) {
                    throw new ServiceException("批量更新销售订单明细的已退货数量失败");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 校验销售订单及其明细的有效性
     */
    private R<SaleOrder> saleOrderValid(Long orderId) {
        SaleOrder saleOrder = baseMapper.selectById(orderId);
        if (saleOrder == null) {
            return R.fail("校验失败：销售订单ID [" + orderId + "] 不存在");
        }
        List<SaleOrderItem> saleOrderItems = itemMapper.queryByOrderId(orderId);
        if (saleOrderItems.isEmpty()) {
            return R.fail("校验失败：订单 [" + saleOrder.getOrderCode() + "] 无明细项");
        }
        for (SaleOrderItem item : saleOrderItems) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("校验失败：产品 [" + item.getProductName() + "] 销售数量必须大于0");
            }
        }
        saleOrder.setItems(saleOrderItems);
        return R.ok(saleOrder);
    }

    /**
     * 填充冗余字段，如客户名称、经手人等
     */
    private void fillRedundantFields(SaleOrderBo bo) {
        if (bo.getCustomerId() != null && StringUtils.isEmpty(bo.getCustomerName())) {
            CompanyVo customer = companyService.queryById(bo.getCustomerId());
            if (customer != null) {
                bo.setCustomerName(customer.getCompanyName());
            }
        }
        if (bo.getHandlerId() == null) {
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser != null) {
                bo.setHandlerId(loginUser.getUserId());
                bo.setHandlerName(loginUser.getNickname());
            }
        }
    }
}
