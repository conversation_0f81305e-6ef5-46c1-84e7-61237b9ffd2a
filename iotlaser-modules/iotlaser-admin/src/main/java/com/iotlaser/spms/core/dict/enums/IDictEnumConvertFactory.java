package com.iotlaser.spms.core.dict.enums;

import cn.hutool.core.util.StrUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.stereotype.Component;

/**
 * 用户前端直接接收枚举
 */
@Component
public class IDictEnumConvertFactory implements ConverterFactory<String, IDictEnum> {


    public static <T extends IDictEnum> T fromValue(Class<T> enumType, String source) {
        if (enumType == null || source == null) {
            return null;
        }

        T[] enumConstants = enumType.getEnumConstants();
        if (enumConstants == null) {
            return null;
        }

        for (T enumObj : enumConstants) {
            String value = String.valueOf(enumObj.getValue());
            if (source.equals(value)) {
                return enumObj;
            }
        }
        return null;
    }

    @NotNull
    @Override
    public <T extends IDictEnum> Converter<String, T> getConverter(@NotNull Class<T> targetType) {
        return new StringToEnumConverter<>(targetType);
    }

    @SuppressWarnings("all")
    private static class StringToEnumConverter<T extends IDictEnum> implements Converter<String, T> {
        private Class<T> enumType;

        public StringToEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        @Override
        public T convert(String source) {
            if (StrUtil.isEmpty(source)) {
                return null;
            }
            return (T) IDictEnumConvertFactory.fromValue(this.enumType, source);
        }
    }
}
