package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinStatement;
import com.iotlaser.spms.erp.enums.FinStatementStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 对账单业务对象 erp_fin_statement
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinStatement.class, reverseConvertGenerate = false)
public class FinStatementBo extends BaseEntity {

    /**
     * 对账ID
     */
    private Long statementId;

    /**
     * 对账编码
     */
    private String statementCode;

    /**
     * 对账名称
     */
    private String statementName;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 此前余额
     */
    private BigDecimal openingBalance;

    /**
     * 期末余额 (应收余额)
     */
    private BigDecimal closingBalance;

    /**
     * 对账状态
     */
    private FinStatementStatus statementStatus;

    /**
     * 摘要
     */
    @NotBlank(message = "摘要不能为空", groups = {AddGroup.class, EditGroup.class})
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
