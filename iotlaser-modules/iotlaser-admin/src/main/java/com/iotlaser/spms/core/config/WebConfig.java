package com.iotlaser.spms.core.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.iotlaser.spms.core.dict.enums.DictEnumWebSerializer;
import com.iotlaser.spms.core.dict.enums.IDictEnumConvertFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.stream().filter(converter -> converter instanceof MappingJackson2HttpMessageConverter).forEach(converter -> {
            MappingJackson2HttpMessageConverter jsonConverter = (MappingJackson2HttpMessageConverter) converter;
            DictEnumWebSerializer dictEnumSerializer = new DictEnumWebSerializer();
            SimpleModule simpleModule = new SimpleModule();
            simpleModule.addSerializer(dictEnumSerializer);
            jsonConverter.getObjectMapper().registerModule(simpleModule);
        });
    }


    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverterFactory(new IDictEnumConvertFactory());
    }

}
