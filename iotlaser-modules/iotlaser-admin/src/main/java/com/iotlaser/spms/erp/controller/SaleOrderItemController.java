package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import com.iotlaser.spms.erp.service.ISaleOrderItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOrderItem")
public class SaleOrderItemController extends BaseController {

    private final ISaleOrderItemService saleOrderItemService;

    /**
     * 查询销售订单明细列表
     */
    @SaCheckPermission("erp:saleOrderItem:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOrderItemVo> list(SaleOrderItemBo bo, PageQuery pageQuery) {
        return saleOrderItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售订单明细列表
     */
    @SaCheckPermission("erp:saleOrderItem:export")
    @Log(title = "销售订单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOrderItemBo bo, HttpServletResponse response) {
        List<SaleOrderItemVo> list = saleOrderItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售订单明细", SaleOrderItemVo.class, response);
    }

    /**
     * 获取销售订单明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("erp:saleOrderItem:query")
    @GetMapping("/{itemId}")
    public R<SaleOrderItemVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long itemId) {
        return R.ok(saleOrderItemService.queryById(itemId));
    }

    /**
     * 新增销售订单明细
     */
    @SaCheckPermission("erp:saleOrderItem:add")
    @Log(title = "销售订单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<SaleOrderItemBo> bos) {
        return toAjax(saleOrderItemService.insertOrUpdateBatch(bos));
    }


    /**
     * 新增销售订单明细
     */
    @SaCheckPermission("erp:saleOrderItem:add")
    @Log(title = "销售订单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SaleOrderItemBo bo) {
        return toAjax(saleOrderItemService.insertByBo(bo));
    }

    /**
     * 修改销售订单明细
     */
    @SaCheckPermission("erp:saleOrderItem:edit")
    @Log(title = "销售订单明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SaleOrderItemBo bo) {
        return toAjax(saleOrderItemService.updateByBo(bo));
    }

    /**
     * 删除销售订单明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("erp:saleOrderItem:remove")
    @Log(title = "销售订单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(saleOrderItemService.deleteWithValidByIds(List.of(itemIds), true));
    }


    /**
     * 获取销售订单明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("erp:saleOrderItem:query")
    @GetMapping("with/{id}")
    public R<SaleOrderItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(saleOrderItemService.queryByIdWith(id));
    }

    /**
     * 查询销售订单明细表列表以及关联详细信息
     */
    @SaCheckPermission("erp:saleOrderItem:list")
    @GetMapping("with/list")
    public TableDataInfo<SaleOrderItemVo> queryPageListWith(SaleOrderItemBo bo, PageQuery pageQuery) {
        return saleOrderItemService.queryPageListWith(bo, pageQuery);
    }


}
