package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购订单服务接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-17
 */
public interface IPurchaseOrderService {

    /**
     * 根据ID查询采购订单
     */
    PurchaseOrderVo queryById(Long orderId);

    /**
     * 分页查询采购订单列表
     */
    TableDataInfo<PurchaseOrderVo> queryPageList(PurchaseOrderBo bo, PageQuery pageQuery);

    /**
     * 查询采购订单列表
     */
    List<PurchaseOrderVo> queryList(PurchaseOrderBo bo);

    /**
     * 新增采购订单
     */
    PurchaseOrderVo insertByBo(PurchaseOrderBo bo);

    /**
     * 修改采购订单
     */
    PurchaseOrderVo updateByBo(PurchaseOrderBo bo);

    /**
     * 校验并批量删除采购订单
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认采购订单
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 取消采购订单
     */
    Boolean cancelOrder(Long orderId, String reason);

    /**
     * 关闭采购订单
     */
    Boolean closeOrder(Long orderId);

    /**
     * 创建采购入库单
     */
    Boolean createPurchaseInbound(Long orderId);

    /**
     * 创建仓库入库单
     */
    Boolean createInbound(Long orderId);

    /**
     * 采购订单财务对账
     */
    Map<String, Object> reconcilePurchaseOrder(Long purchaseOrderId);

}
