package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购订单服务接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-17
 */
public interface IPurchaseOrderService {

    /**
     * 根据ID查询采购订单
     */
    PurchaseOrderVo queryById(Long orderId);

    /**
     * 分页查询采购订单列表
     */
    TableDataInfo<PurchaseOrderVo> queryPageList(PurchaseOrderBo bo, PageQuery pageQuery);

    /**
     * 查询采购订单列表
     */
    List<PurchaseOrderVo> queryList(PurchaseOrderBo bo);

    /**
     * 新增采购订单
     */
    PurchaseOrderVo insertByBo(PurchaseOrderBo bo);

    /**
     * 修改采购订单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    PurchaseOrderVo updateByBo(PurchaseOrderBo bo);

    /**
     * 校验并批量删除采购订单
     *
     * @param ids     待删除的采购订单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认采购订单
     *
     * @param orderId 采购订单的唯一主键ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 取消采购订单
     *
     * @param orderId 采购订单的唯一主键ID
     * @param reason  取消原因说明
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    Boolean cancelOrder(Long orderId, String reason);

    /**
     * 关闭采购订单
     *
     * @param orderId 采购订单的唯一主键ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    Boolean closeOrder(Long orderId);

    /**
     * 创建采购入库单
     *
     * @param orderId 采购订单的唯一主键ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    Boolean createPurchaseInbound(Long orderId);

    /**
     * 创建仓库入库单
     *
     * @param orderId 采购订单的唯一主键ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    Boolean createInbound(Long orderId);

    /**
     * 采购订单财务对账
     *
     * @param purchaseOrderId 采购订单的唯一主键ID
     * @return 包含对账结果的Map对象，包含订单金额、已付款金额、差异等信息
     */
    Map<String, Object> reconcilePurchaseOrder(Long purchaseOrderId);

}
