package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.TransferItem;
import com.iotlaser.spms.wms.domain.bo.TransferBo;
import com.iotlaser.spms.wms.domain.bo.TransferItemBo;
import com.iotlaser.spms.wms.domain.vo.TransferItemVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品移库Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface ITransferService {

    /**
     * 查询产品移库
     *
     * @param transferId 主键
     * @return 产品移库
     */
    TransferVo queryById(Long transferId);

    /**
     * 分页查询产品移库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库分页列表
     */
    TableDataInfo<TransferVo> queryPageList(TransferBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品移库列表
     *
     * @param bo 查询条件
     * @return 产品移库列表
     */
    List<TransferVo> queryList(TransferBo bo);

    /**
     * 新增产品移库
     *
     * @param bo 产品移库
     * @return 是否新增成功
     */
    TransferVo insertByBo(TransferBo bo);

    /**
     * 修改产品移库
     *
     * @param bo 产品移库
     * @return 是否修改成功
     */
    TransferVo updateByBo(TransferBo bo);

    /**
     * 校验并批量删除产品移库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询产品移库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品移库明细表
     */
    TransferItemVo queryItemById(Long itemId);

    /**
     * 查询产品移库明细表列表及其关联信息
     *
     * @param transferId 移库ID
     * @return 产品移库明细表列表
     */
    List<TransferItem> queryItemByTransferId(Long transferId);

    /**
     * 查询产品移库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品移库明细
     */
    List<TransferItemVo> queryItemList(TransferItemBo bo);

    /**
     * 分页查询产品移库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库明细表分页列表
     */
    TableDataInfo<TransferItemVo> queryItemPageList(TransferItemBo bo, PageQuery pageQuery);

    /**
     * 确认移库单
     *
     * @param transferId 出库单ID
     * @return 是否确认成功
     */
    Boolean confirmTransfer(Long transferId);

    /**
     * 仓库出库完成后状态回传
     * 由WMS系统调用，通知WMS移库出库单已完成
     *
     * @param outbound 出库单
     * @return 是否更新成功
     */
    Boolean updateStatusByWms(Outbound outbound);

    /**
     * 仓库出库异常回传
     * 由WMS系统调用，通知销售出库单发生异常
     *
     * @param outboundId      销售出库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    Boolean handleWmsOutboundException(Long outboundId, String exceptionReason);

    /**
     * 仓库入库完成后状态回传
     * 由WMS系统调用，通知WMS移库入库单已完成
     *
     * @param inbound 入库单
     * @return 是否更新成功
     */
    Boolean updateStatusByWms(Inbound inbound);

    /**
     * 仓库出库异常回传
     * 由WMS系统调用，通知销售出库单发生异常
     *
     * @param inboundId       入库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    Boolean handleWmsInboundException(Long inboundId, String exceptionReason);

    /**
     * 更新产品移库为已完成
     *
     * @param transferId 移库ID
     */
    void finish(Long transferId);
}
