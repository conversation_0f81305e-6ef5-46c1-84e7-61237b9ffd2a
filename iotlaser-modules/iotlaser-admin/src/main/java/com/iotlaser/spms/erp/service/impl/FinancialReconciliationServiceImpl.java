package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.domain.vo.FinancialReconciliationVo;
import com.iotlaser.spms.erp.enums.FinArReceivableStatus;
import com.iotlaser.spms.erp.mapper.FinArReceiptReceivableLinkMapper;
import com.iotlaser.spms.erp.mapper.SaleOrderMapper;
import com.iotlaser.spms.erp.service.IFinArReceiptOrderService;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import com.iotlaser.spms.erp.service.IFinancialReconciliationService;
import com.iotlaser.spms.erp.service.ISaleOrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 财务对账服务实现类
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinancialReconciliationServiceImpl implements IFinancialReconciliationService {

    private final SaleOrderMapper saleOrderMapper;
    private final ISaleOrderItemService saleOrderItemService;
    private final IFinArReceivableService finArReceivableService;
    private final IFinArReceiptOrderService finArReceiptOrderService;
    private final FinArReceiptReceivableLinkMapper receiptReceivableLinkMapper;

    /**
     * 对单个销售订单进行财务对账
     *
     * @param orderId 销售订单ID
     * @return 对账结果
     */
    @Override
    public FinancialReconciliationVo reconcileOrder(Long orderId) {
        try {
            if (orderId == null) {
                throw new ServiceException("订单ID不能为空");
            }

            // 获取销售订单信息
            SaleOrder order = saleOrderMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 创建对账结果对象
            FinancialReconciliationVo reconciliation = new FinancialReconciliationVo();
            reconciliation.setOrderId(orderId);
            reconciliation.setOrderCode(order.getOrderCode());
            reconciliation.setCustomerId(order.getCustomerId());
            reconciliation.setCustomerName(order.getCustomerName());
            reconciliation.setOrderDate(order.getOrderDate());
            reconciliation.setOrderStatus(order.getOrderStatus().name());
            reconciliation.setReconciliationTime(LocalDateTime.now());

            // 计算订单总金额
            BigDecimal orderAmount = calculateOrderReceivableAmount(orderId);
            reconciliation.setOrderAmount(orderAmount);

            // 计算订单不含税金额和税额
            List<SaleOrderItem> saleOrderItems = saleOrderItemService.queryByOrderId(orderId);
            BigDecimal orderAmountExclusiveTax = saleOrderItems.stream()
                .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal orderTaxAmount = saleOrderItems.stream()
                .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            reconciliation.setOrderAmountExclusiveTax(orderAmountExclusiveTax);
            reconciliation.setOrderTaxAmount(orderTaxAmount);

            // 计算已开票金额
            BigDecimal invoicedAmount = calculateOrderInvoicedAmount(orderId);
            reconciliation.setInvoicedAmount(invoicedAmount);

            // 计算已收款金额
            BigDecimal receivedAmount = calculateOrderReceivedAmount(orderId);
            reconciliation.setReceivedAmount(receivedAmount);

            // 计算未开票金额和未收款金额
            BigDecimal uninvoicedAmount = orderAmount.subtract(invoicedAmount);
            BigDecimal unreceivedAmount = orderAmount.subtract(receivedAmount);
            reconciliation.setUninvoicedAmount(uninvoicedAmount);
            reconciliation.setUnreceivedAmount(unreceivedAmount);

            // 计算差异金额（已收款 - 已开票）
            BigDecimal differenceAmount = receivedAmount.subtract(invoicedAmount);
            reconciliation.setDifferenceAmount(differenceAmount);

            // 判断对账状态
            FinancialReconciliationVo.ReconciliationStatus status = determineReconciliationStatus(
                orderAmount, invoicedAmount, receivedAmount, differenceAmount);
            reconciliation.setReconciliationStatus(status);

            // 设置差异说明
            if (differenceAmount.abs().compareTo(new BigDecimal("0.01")) > 0) {
                reconciliation.setDifferenceRemark(generateDifferenceRemark(differenceAmount));
            }

            // 获取相关单据信息
            // TODO: 获取应收发票列表
            // reconciliation.setReceivables(finArReceivableService.queryByOrderId(orderId));

            // TODO: 获取收款单列表
            // reconciliation.setReceipts(finArReceiptOrderService.queryByOrderId(orderId));

            // TODO: 获取核销记录列表
            // reconciliation.setApplyRecords(finArReceiptReceivableLinkService.queryByOrderId(orderId));

            log.info("订单财务对账完成 - 订单: {}, 状态: {}, 订单金额: {}, 已收款: {}, 已开票: {}, 差异: {}",
                order.getOrderCode(), status, orderAmount, receivedAmount, invoicedAmount, differenceAmount);

            return reconciliation;
        } catch (Exception e) {
            log.error("订单财务对账失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 批量对账销售订单
     *
     * @param orderIds 销售订单ID列表
     * @return 对账结果列表
     */
    @Override
    public List<FinancialReconciliationVo> batchReconcileOrders(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<FinancialReconciliationVo> results = new ArrayList<>();
        for (Long orderId : orderIds) {
            try {
                FinancialReconciliationVo reconciliation = reconcileOrder(orderId);
                results.add(reconciliation);
            } catch (Exception e) {
                log.error("批量对账失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
                // 继续处理其他订单
            }
        }

        log.info("批量对账完成 - 总数: {}, 成功: {}", orderIds.size(), results.size());
        return results;
    }

    /**
     * 按客户进行财务对账
     *
     * @param customerId 客户ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 对账结果列表
     */
    @Override
    public List<FinancialReconciliationVo> reconcileByCustomer(Long customerId, LocalDate startDate, LocalDate endDate) {
        try {
            // 查询客户在指定日期范围内的订单
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOrder::getCustomerId, customerId);
            if (startDate != null) {
                wrapper.ge(SaleOrder::getOrderDate, startDate);
            }
            if (endDate != null) {
                wrapper.le(SaleOrder::getOrderDate, endDate);
            }
            wrapper.orderByDesc(SaleOrder::getOrderDate);

            List<SaleOrder> orders = saleOrderMapper.selectList(wrapper);
            List<Long> orderIds = orders.stream().map(SaleOrder::getOrderId).collect(Collectors.toList());

            return batchReconcileOrders(orderIds);
        } catch (Exception e) {
            log.error("按客户对账失败 - 客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
            throw new ServiceException("按客户对账失败：" + e.getMessage());
        }
    }

    /**
     * 按日期范围进行财务对账
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param pageQuery 分页参数
     * @return 对账结果分页列表
     */
    @Override
    public TableDataInfo<FinancialReconciliationVo> reconcileByDateRange(LocalDate startDate, LocalDate endDate, PageQuery pageQuery) {
        try {
            // 查询指定日期范围内的订单
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            if (startDate != null) {
                wrapper.ge(SaleOrder::getOrderDate, startDate);
            }
            if (endDate != null) {
                wrapper.le(SaleOrder::getOrderDate, endDate);
            }
            wrapper.orderByDesc(SaleOrder::getOrderDate);

            Page<SaleOrder> page = saleOrderMapper.selectPage(pageQuery.build(), wrapper);
            List<Long> orderIds = page.getRecords().stream().map(SaleOrder::getOrderId).collect(Collectors.toList());

            List<FinancialReconciliationVo> reconciliations = batchReconcileOrders(orderIds);

            return TableDataInfo.build(reconciliations);
        } catch (Exception e) {
            log.error("按日期范围对账失败 - 开始日期: {}, 结束日期: {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("按日期范围对账失败：" + e.getMessage());
        }
    }

    /**
     * 计算订单的应收金额
     *
     * @param orderId 销售订单ID
     * @return 应收金额
     */
    @Override
    public BigDecimal calculateOrderReceivableAmount(Long orderId) {
        try {
            List<SaleOrderItem> saleOrderItems = saleOrderItemService.queryByOrderId(orderId);
            return saleOrderItems.stream()
                .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算订单应收金额失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算订单的已收款金额
     *
     * @param orderId 销售订单ID
     * @return 已收款金额
     */
    @Override
    public BigDecimal calculateOrderReceivedAmount(Long orderId) {
        try {
            // 查询订单相关的应收单
            List<FinArReceivableVo> receivables = finArReceivableService.queryBySourceId(orderId);

            BigDecimal totalReceivedAmount = BigDecimal.ZERO;

            for (FinArReceivableVo receivable : receivables) {
                // 根据应收单状态计算已收金额
                if (FinArReceivableStatus.FULLY_PAID == receivable.getReceivableStatus()) {
                    // 全额收款
                    totalReceivedAmount = totalReceivedAmount.add(receivable.getAmount());
                } else if (FinArReceivableStatus.PARTIALLY_PAID == receivable.getReceivableStatus()) {
                    // 部分收款 - 查询核销记录
                    try {
                        List<FinArReceiptReceivableLinkVo> links = receiptReceivableLinkMapper.queryByReceivableId(receivable.getReceivableId());
                        BigDecimal partialAmount = links.stream()
                            .map(link -> link.getAppliedAmount() != null ? link.getAppliedAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalReceivedAmount = totalReceivedAmount.add(partialAmount);
                    } catch (Exception ex) {
                        log.warn("查询应收单核销记录失败 - 应收单ID: {}, 错误: {}",
                            receivable.getReceivableId(), ex.getMessage());
                    }
                }
                // PENDING、OVERDUE状态的应收单不计入已收款
            }

            log.info("计算订单已收款金额完成 - 订单ID: {}, 应收单数量: {}, 已收款: {}",
                orderId, receivables.size(), totalReceivedAmount);
            return totalReceivedAmount;
        } catch (Exception e) {
            log.error("计算订单已收款金额失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算订单的已开票金额
     *
     * @param orderId 销售订单ID
     * @return 已开票金额
     */
    @Override
    public BigDecimal calculateOrderInvoicedAmount(Long orderId) {
        try {
            // 查询订单相关的应收单（应收单代表已开票）
            List<FinArReceivableVo> receivables = finArReceivableService.queryBySourceId(orderId);

            // 汇总应收单金额（即已开票金额）
            BigDecimal totalInvoicedAmount = receivables.stream()
                .filter(r -> FinArReceivableStatus.CANCELLED != r.getReceivableStatus()) // 排除已取消的
                .map(r -> r.getAmount() != null ? r.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("计算订单已开票金额完成 - 订单ID: {}, 应收单数量: {}, 已开票: {}",
                orderId, receivables.size(), totalInvoicedAmount);
            return totalInvoicedAmount;
        } catch (Exception e) {
            log.error("计算订单已开票金额失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 判断对账状态
     */
    private FinancialReconciliationVo.ReconciliationStatus determineReconciliationStatus(
        BigDecimal orderAmount, BigDecimal invoicedAmount, BigDecimal receivedAmount, BigDecimal differenceAmount) {

        // 容差范围（0.01元）
        BigDecimal tolerance = new BigDecimal("0.01");

        // 如果订单金额为0，认为未对账
        if (orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return FinancialReconciliationVo.ReconciliationStatus.UNRECONCILED;
        }

        // 如果已收款和已开票都为0，认为未对账
        if (receivedAmount.compareTo(BigDecimal.ZERO) <= 0 && invoicedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return FinancialReconciliationVo.ReconciliationStatus.UNRECONCILED;
        }

        // 如果差异金额在容差范围内，认为对账一致
        if (differenceAmount.abs().compareTo(tolerance) <= 0) {
            // 进一步检查是否完全对账
            if (receivedAmount.compareTo(orderAmount) >= 0 && invoicedAmount.compareTo(orderAmount) >= 0) {
                return FinancialReconciliationVo.ReconciliationStatus.MATCHED;
            } else {
                return FinancialReconciliationVo.ReconciliationStatus.PARTIAL;
            }
        }

        // 存在差异
        return FinancialReconciliationVo.ReconciliationStatus.DIFFERENCE;
    }

    /**
     * 生成差异说明
     */
    private String generateDifferenceRemark(BigDecimal differenceAmount) {
        if (differenceAmount.compareTo(BigDecimal.ZERO) > 0) {
            return String.format("收款超过开票金额 %.2f 元", differenceAmount);
        } else {
            return String.format("开票超过收款金额 %.2f 元", differenceAmount.abs());
        }
    }

    /**
     * 获取对账差异报告
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 差异报告列表
     */
    @Override
    public List<FinancialReconciliationVo> getDifferenceReport(LocalDate startDate, LocalDate endDate) {
        try {
            // 查询指定日期范围内的订单
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            if (startDate != null) {
                wrapper.ge(SaleOrder::getOrderDate, startDate);
            }
            if (endDate != null) {
                wrapper.le(SaleOrder::getOrderDate, endDate);
            }
            wrapper.orderByDesc(SaleOrder::getOrderDate);

            List<SaleOrder> orders = saleOrderMapper.selectList(wrapper);
            List<Long> orderIds = orders.stream().map(SaleOrder::getOrderId).collect(Collectors.toList());

            // 批量对账
            List<FinancialReconciliationVo> reconciliations = batchReconcileOrders(orderIds);

            // 过滤出存在差异的记录
            return reconciliations.stream()
                .filter(r -> r.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.DIFFERENCE)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取对账差异报告失败 - 开始日期: {}, 结束日期: {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("获取对账差异报告失败：" + e.getMessage());
        }
    }

    /**
     * 检查订单是否存在对账差异
     *
     * @param orderId 销售订单ID
     * @return 是否存在差异
     */
    @Override
    public Boolean hasReconciliationDifference(Long orderId) {
        try {
            FinancialReconciliationVo reconciliation = reconcileOrder(orderId);
            return reconciliation.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.DIFFERENCE;
        } catch (Exception e) {
            log.error("检查订单对账差异失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取对账差异详情
     *
     * @param orderId 销售订单ID
     * @return 差异详情列表
     */
    @Override
    public List<FinancialReconciliationVo.ReconciliationDifference> getReconciliationDifferences(Long orderId) {
        try {
            List<FinancialReconciliationVo.ReconciliationDifference> differences = new ArrayList<>();

            FinancialReconciliationVo reconciliation = reconcileOrder(orderId);

            // 检查收款与开票差异
            if (reconciliation.getDifferenceAmount() != null &&
                reconciliation.getDifferenceAmount().abs().compareTo(new BigDecimal("0.01")) > 0) {

                FinancialReconciliationVo.ReconciliationDifference diff = new FinancialReconciliationVo.ReconciliationDifference();
                diff.setDifferenceType("收款开票差异");
                diff.setDifferenceAmount(reconciliation.getDifferenceAmount());
                diff.setDifferenceRemark(reconciliation.getDifferenceRemark());
                differences.add(diff);
            }

            // 检查未收款差异
            if (reconciliation.getUnreceivedAmount() != null &&
                reconciliation.getUnreceivedAmount().compareTo(BigDecimal.ZERO) > 0) {

                FinancialReconciliationVo.ReconciliationDifference diff = new FinancialReconciliationVo.ReconciliationDifference();
                diff.setDifferenceType("未收款");
                diff.setDifferenceAmount(reconciliation.getUnreceivedAmount());
                diff.setDifferenceRemark("订单存在未收款金额");
                differences.add(diff);
            }

            // 检查未开票差异
            if (reconciliation.getUninvoicedAmount() != null &&
                reconciliation.getUninvoicedAmount().compareTo(BigDecimal.ZERO) > 0) {

                FinancialReconciliationVo.ReconciliationDifference diff = new FinancialReconciliationVo.ReconciliationDifference();
                diff.setDifferenceType("未开票");
                diff.setDifferenceAmount(reconciliation.getUninvoicedAmount());
                diff.setDifferenceRemark("订单存在未开票金额");
                differences.add(diff);
            }

            return differences;
        } catch (Exception e) {
            log.error("获取对账差异详情失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 标记对账差异已处理
     *
     * @param orderId 销售订单ID
     * @param remark  处理备注
     * @return 是否处理成功
     */
    @Override
    public Boolean markDifferenceResolved(Long orderId, String remark) {
        try {
            // TODO: 实现差异处理标记功能
            // 可以在订单表中添加差异处理标记字段，或者创建专门的差异处理记录表
            log.info("标记对账差异已处理 - 订单ID: {}, 备注: {}", orderId, remark);
            return true;
        } catch (Exception e) {
            log.error("标记对账差异处理失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取对账统计信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    @Override
    public ReconciliationStatistics getReconciliationStatistics(LocalDate startDate, LocalDate endDate) {
        try {
            // 查询指定日期范围内的订单
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            if (startDate != null) {
                wrapper.ge(SaleOrder::getOrderDate, startDate);
            }
            if (endDate != null) {
                wrapper.le(SaleOrder::getOrderDate, endDate);
            }

            List<SaleOrder> orders = saleOrderMapper.selectList(wrapper);
            List<Long> orderIds = orders.stream().map(SaleOrder::getOrderId).collect(Collectors.toList());

            // 批量对账
            List<FinancialReconciliationVo> reconciliations = batchReconcileOrders(orderIds);

            // 统计各种状态的订单数量
            ReconciliationStatistics statistics = new ReconciliationStatistics();
            statistics.setTotalOrders(reconciliations.size());

            long matchedCount = reconciliations.stream()
                .filter(r -> r.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.MATCHED)
                .count();
            statistics.setMatchedOrders((int) matchedCount);

            long differenceCount = reconciliations.stream()
                .filter(r -> r.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.DIFFERENCE)
                .count();
            statistics.setDifferenceOrders((int) differenceCount);

            long unreconciledCount = reconciliations.stream()
                .filter(r -> r.getReconciliationStatus() == FinancialReconciliationVo.ReconciliationStatus.UNRECONCILED)
                .count();
            statistics.setUnreconciledOrders((int) unreconciledCount);

            // 统计金额
            BigDecimal totalOrderAmount = reconciliations.stream()
                .map(r -> r.getOrderAmount() != null ? r.getOrderAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setTotalOrderAmount(totalOrderAmount);

            BigDecimal totalReceivedAmount = reconciliations.stream()
                .map(r -> r.getReceivedAmount() != null ? r.getReceivedAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setTotalReceivedAmount(totalReceivedAmount);

            BigDecimal totalInvoicedAmount = reconciliations.stream()
                .map(r -> r.getInvoicedAmount() != null ? r.getInvoicedAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setTotalInvoicedAmount(totalInvoicedAmount);

            BigDecimal totalDifferenceAmount = reconciliations.stream()
                .map(r -> r.getDifferenceAmount() != null ? r.getDifferenceAmount().abs() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setTotalDifferenceAmount(totalDifferenceAmount);

            log.info("对账统计完成 - 日期范围: {} 到 {}, 总订单: {}, 一致: {}, 差异: {}, 未对账: {}",
                startDate, endDate, statistics.getTotalOrders(), statistics.getMatchedOrders(),
                statistics.getDifferenceOrders(), statistics.getUnreconciledOrders());

            return statistics;
        } catch (Exception e) {
            log.error("获取对账统计信息失败 - 开始日期: {}, 结束日期: {}, 错误: {}", startDate, endDate, e.getMessage(), e);
            throw new ServiceException("获取对账统计信息失败：" + e.getMessage());
        }
    }
}
