package com.iotlaser.spms.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库位类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Getter
@AllArgsConstructor
public enum LocationType implements IDictEnum<String> {

    WAREHOUSE("warehouse", "仓库", "顶级仓库节点"),
    AREA("area", "库区", "仓库下的区域划分"),
    SHELF("shelf", "货架", "库区内的货架"),
    POSITION("position", "货位", "货架上的具体存储位置"),
    PRODUCTION_LINE("production_line", "生产线", "生产车间的生产线"),
    WORK_CENTER("work_center", "工作中心", "生产线上的工作中心"),
    QUALITY_AREA("quality_area", "质检区", "质量检验区域"),
    QUARANTINE_AREA("quarantine_area", "隔离区", "不合格品隔离区域"),
    STAGING_AREA("staging_area", "暂存区", "临时存放区域"),
    SHIPPING_DOCK("shipping_dock", "发货台", "发货装卸区域"),
    RECEIVING_DOCK("receiving_dock", "收货台", "收货装卸区域");

    public final static String DICT_CODE = "base_location_type";
    public final static String DICT_NAME = "库位类型";
    public final static String DICT_DESC = "定义仓储和生产场所的位置分类类型，包括仓库、货架、生产线等不同层级的位置类型";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 库位类型枚举
     */
    public static LocationType getByValue(String value) {
        for (LocationType locationType : values()) {
            if (locationType.getValue().equals(value)) {
                return locationType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为仓储类型
     *
     * @return 是否为仓储类型
     */
    public boolean isStorageType() {
        return this == WAREHOUSE || this == AREA || this == SHELF || this == POSITION;
    }

    /**
     * 判断是否为生产类型
     *
     * @return 是否为生产类型
     */
    public boolean isProductionType() {
        return this == PRODUCTION_LINE || this == WORK_CENTER;
    }

    /**
     * 判断是否为质检类型
     *
     * @return 是否为质检类型
     */
    public boolean isQualityType() {
        return this == QUALITY_AREA || this == QUARANTINE_AREA;
    }

    /**
     * 判断是否为物流类型
     *
     * @return 是否为物流类型
     */
    public boolean isLogisticsType() {
        return this == STAGING_AREA || this == SHIPPING_DOCK || this == RECEIVING_DOCK;
    }

    /**
     * 获取层级深度
     *
     * @return 层级深度
     */
    public int getHierarchyLevel() {
        switch (this) {
            case WAREHOUSE:
                return 1;
            case AREA:
                return 2;
            case SHELF:
                return 3;
            case POSITION:
                return 4;
            case PRODUCTION_LINE:
            case QUALITY_AREA:
            case QUARANTINE_AREA:
            case STAGING_AREA:
            case SHIPPING_DOCK:
            case RECEIVING_DOCK:
                return 2;
            case WORK_CENTER:
                return 3;
            default:
                return 0;
        }
    }
}
