package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购退货单状态
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum PurchaseReturnStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "退货单已创建，但未通知仓库"),
    PENDING_WAREHOUSE("pending_warehouse", "待出库", "已通知仓库，等待仓库执行出库操作"),
    COMPLETED("completed", "已出库", "仓库已完成所有退货出库操作，库存已减少"),
    CANCELLED("cancelled", "已取消", "退货流程已被取消"),
    ;


    public final static String DICT_CODE = "erp_purchase_return_status";
    public final static String DICT_NAME = "采购退货单状态枚举";
    public final static String DICT_DESC = "采购退货单状态枚举";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 采购退货状态枚举
     */
    public static PurchaseReturnStatus getByValue(String value) {
        for (PurchaseReturnStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
