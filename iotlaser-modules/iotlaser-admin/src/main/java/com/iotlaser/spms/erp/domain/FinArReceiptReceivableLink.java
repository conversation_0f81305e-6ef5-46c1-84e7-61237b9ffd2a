package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款单与应收单核销关系对象 erp_fin_ar_receipt_receivable_link
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receipt_receivable_link")
public class FinArReceiptReceivableLink extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @TableId(value = "link_id")
    private Long linkId;

    /**
     * 收款ID
     */
    private Long receiptId;

    /**
     * 应收ID
     */
    private Long receivableId;

    /**
     * 核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
