package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import com.iotlaser.spms.erp.mapper.FinArReceiptReceivableLinkMapper;
import com.iotlaser.spms.erp.service.IFinArReceiptReceivableLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 收款单与应收单核销关系Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceiptReceivableLinkServiceImpl implements IFinArReceiptReceivableLinkService {

    private final FinArReceiptReceivableLinkMapper baseMapper;

    /**
     * 查询收款单与应收单核销关系
     *
     * @param linkId 主键
     * @return 收款单与应收单核销关系
     */
    @Override
    public FinArReceiptReceivableLinkVo queryById(Long linkId) {
        return baseMapper.selectVoById(linkId);
    }

    /**
     * 分页查询收款单与应收单核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单与应收单核销关系分页列表
     */
    @Override
    public TableDataInfo<FinArReceiptReceivableLinkVo> queryPageList(FinArReceiptReceivableLinkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = buildQueryWrapper(bo);
        Page<FinArReceiptReceivableLinkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的收款单与应收单核销关系列表
     *
     * @param bo 查询条件
     * @return 收款单与应收单核销关系列表
     */
    @Override
    public List<FinArReceiptReceivableLinkVo> queryList(FinArReceiptReceivableLinkBo bo) {
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceiptReceivableLink> buildQueryWrapper(FinArReceiptReceivableLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceiptReceivableLink> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinArReceiptReceivableLink::getLinkId);
        lqw.eq(bo.getReceiptId() != null, FinArReceiptReceivableLink::getReceiptId, bo.getReceiptId());
        lqw.eq(bo.getReceivableId() != null, FinArReceiptReceivableLink::getReceivableId, bo.getReceivableId());
        lqw.eq(bo.getAppliedAmount() != null, FinArReceiptReceivableLink::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getCancellationDate() != null, FinArReceiptReceivableLink::getCancellationDate, bo.getCancellationDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceiptReceivableLink::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinArReceiptReceivableLinkBo bo) {
        FinArReceiptReceivableLink add = MapstructUtils.convert(bo, FinArReceiptReceivableLink.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLinkId(add.getLinkId());
        }
        return flag;
    }

    /**
     * 修改收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinArReceiptReceivableLinkBo bo) {
        FinArReceiptReceivableLink update = MapstructUtils.convert(bo, FinArReceiptReceivableLink.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceiptReceivableLink entity) {
        // 数据校验：检查唯一约束和必填字段
    }

    /**
     * 校验并批量删除收款单与应收单核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 业务校验：检查是否可以删除
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}
