package com.iotlaser.spms.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 生产领料对象 mes_production_issue
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mes_production_issue")
public class ProductionIssue extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 领料单ID
     */
    @TableId(value = "issue_id")
    private Long issueId;

    /**
     * 领料单编号
     */
    private String issueCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 领料时间
     */
    private LocalDateTime issueTime;

    /**
     * 领料状态
     */
    private ProductionIssueStatus issueStatus;

    /**
     * 领料申请人ID
     */
    private Long applicantId;

    /**
     * 领料申请人
     */
    private String applicantName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
