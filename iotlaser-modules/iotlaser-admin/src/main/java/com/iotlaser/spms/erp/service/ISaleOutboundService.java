package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售出库单 服务层
 *
 * <AUTHOR>
 */
public interface ISaleOutboundService {

    /**
     * 查询销售出库
     *
     * @param outboundId 主键
     * @return 销售出库视图对象
     */
    SaleOutboundVo queryById(Long outboundId);

    /**
     * 查询销售出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库分页列表
     */
    TableDataInfo<SaleOutboundVo> queryPageList(SaleOutboundBo bo, PageQuery pageQuery);

    /**
     * 查询销售出库列表
     *
     * @param bo 查询条件
     * @return 销售出库列表
     */
    List<SaleOutboundVo> queryList(SaleOutboundBo bo);

    /**
     * 新增销售出库
     *
     * @param bo 销售出库业务对象
     * @return 销售出库视图对象
     */
    SaleOutboundVo insertByBo(SaleOutboundBo bo);

    /**
     * 修改销售出库
     *
     * @param bo 销售出库业务对象
     * @return 销售出库视图对象
     */
    SaleOutboundVo updateByBo(SaleOutboundBo bo);

    /**
     * 校验并批量删除销售出库
     *
     * @param ids     主键集合
     * @param isValid 是否校验, true-删除前校验, false-不校验
     * @return 操作结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查销售订单是否已存在关联的出库单
     *
     * @param orderId 销售订单ID
     * @return true-存在, false-不存在
     */
    Boolean existsByDirectSourceId(Long orderId);

    /**
     * 根据销售订单ID查询出库单列表
     *
     * @param orderId 销售订单ID
     * @return 销售出库单列表
     */
    List<SaleOutbound> queryByDirectSourceId(Long orderId);

    /**
     * 根据出库单ID查询明细
     *
     * @param outboundId 出库单ID
     * @return 销售出库单明细列表
     */
    List<SaleOutboundItem> queryItemByOutboundId(Long outboundId);

    /**
     * 确认销售出库单
     *
     * @param outboundId 待确认的出库单ID
     * @return 操作结果
     */
    Boolean confirmOutbound(Long outboundId);

    /**
     * 完成销售出库
     *
     * @param outboundId 已完成的出库单ID
     * @return 操作结果
     */
    Boolean completeOutbound(Long outboundId);

    /**
     * 取消销售出库单
     *
     * @param outboundId 待取消的出库单ID
     * @param reason     取消原因
     * @return 操作结果
     */
    Boolean cancelOutbound(Long outboundId, String reason);

    /**
     * 创建仓库出库单
     *
     * @param outboundId 销售出库单ID
     * @return 操作结果
     */
    Boolean createOutbound(Long outboundId);

    /**
     * 从销售出库单创建销售退货单
     *
     * @param outboundId 已出库的销售出库单ID
     * @return 操作结果
     */
    Boolean createReturn(Long outboundId);

    /**
     * 创建销售出库单的收付款单
     *
     * @param saleOutboundId 销售出库单ID
     * @return 操作结果
     */
    Boolean createFinReceivable(Long saleOutboundId);

    /**
     * 根据销售订单创建出库单
     *
     * @param saleOrder 销售订单
     * @return 操作结果
     */
    Boolean createFromSaleOrder(SaleOrder saleOrder);
}
