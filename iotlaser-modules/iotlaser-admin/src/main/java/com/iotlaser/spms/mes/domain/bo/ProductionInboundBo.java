package com.iotlaser.spms.mes.domain.bo;

import com.iotlaser.spms.mes.domain.ProductionInbound;
import com.iotlaser.spms.mes.enums.ProductionInboundStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 生产入库业务对象 mes_production_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductionInbound.class, reverseConvertGenerate = false)
public class ProductionInboundBo extends BaseEntity {

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 入库单编号
     */
    @NotBlank(message = "入库单编号不能为空", groups = {EditGroup.class})
    private String inboundCode;

    /**
     * 源头单据ID (整个业务流程的最初发起单据)
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头单据编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头单据类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游单据ID (当前单据的直接创建来源单据)
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游单据编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游单据类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 入库时间
     */
    private LocalDateTime inboundTime;

    /**
     * 入库状态
     */
    private ProductionInboundStatus inboundStatus;

    /**
     * 完工处理人ID
     */
    private Long handlerId;

    /**
     * 完工处理人
     */
    private String handlerName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
