package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import com.iotlaser.spms.erp.mapper.PurchaseReturnItemMapper;
import com.iotlaser.spms.erp.service.IPurchaseReturnItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购退货明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseReturnItemServiceImpl implements IPurchaseReturnItemService {

    private final PurchaseReturnItemMapper baseMapper;
    private final ILocationService locationService;

    /**
     * 查询采购退货明细
     *
     * @param itemId 主键
     * @return 采购退货明细
     */
    @Override
    public PurchaseReturnItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询采购退货明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseReturnItemVo> queryPageList(PurchaseReturnItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseReturnItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseReturnItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购退货明细列表
     *
     * @param bo 查询条件
     * @return 采购退货明细列表
     */
    @Override
    public List<PurchaseReturnItemVo> queryList(PurchaseReturnItemBo bo) {
        LambdaQueryWrapper<PurchaseReturnItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseReturnItem> buildQueryWrapper(PurchaseReturnItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseReturnItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseReturnItem::getItemId);
        lqw.eq(bo.getReturnId() != null, PurchaseReturnItem::getReturnId, bo.getReturnId());
        lqw.eq(bo.getProductId() != null, PurchaseReturnItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseReturnItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseReturnItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, PurchaseReturnItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, PurchaseReturnItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, PurchaseReturnItem::getPrice, bo.getPrice());
        lqw.eq(bo.getLocationId() != null, PurchaseReturnItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), PurchaseReturnItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), PurchaseReturnItem::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseReturnItem::getStatus, bo.getStatus());
        //lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), PurchaseReturnItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增采购退货明细
     *
     * @param bo 采购退货明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PurchaseReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        PurchaseReturnItem add = MapstructUtils.convert(bo, PurchaseReturnItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改采购退货明细
     *
     * @param bo 采购退货明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PurchaseReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        PurchaseReturnItem update = MapstructUtils.convert(bo, PurchaseReturnItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseReturnItem entity) {
        // 校验同一退货单中产品不能重复
        if (entity.getReturnId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<PurchaseReturnItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseReturnItem::getReturnId, entity.getReturnId());
            wrapper.eq(PurchaseReturnItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(PurchaseReturnItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一采购退货单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除采购退货明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<PurchaseReturnItem> items = baseMapper.selectByIds(ids);
            for (PurchaseReturnItem item : items) {
                log.info("删除采购退货明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    /**
     * 批量新增或更新采购退货明细
     *
     * @param items 采购退货明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<PurchaseReturnItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<PurchaseReturnItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, PurchaseReturnItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据退货单ID查询明细列表
     *
     * @param returnId 退货单ID
     * @return 明细列表
     */
    @Override
    public List<PurchaseReturnItemVo> queryByReturnId(Long returnId) {
        LambdaQueryWrapper<PurchaseReturnItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseReturnItem::getReturnId, returnId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(PurchaseReturnItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }

}
