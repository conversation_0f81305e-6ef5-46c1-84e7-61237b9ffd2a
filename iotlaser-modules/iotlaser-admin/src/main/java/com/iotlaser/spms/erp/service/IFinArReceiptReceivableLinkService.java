package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptReceivableLinkVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 收款单与应收单核销关系Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinArReceiptReceivableLinkService {

    /**
     * 查询收款单与应收单核销关系
     *
     * @param linkId 主键
     * @return 收款单与应收单核销关系
     */
    FinArReceiptReceivableLinkVo queryById(Long linkId);

    /**
     * 分页查询收款单与应收单核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单与应收单核销关系分页列表
     */
    TableDataInfo<FinArReceiptReceivableLinkVo> queryPageList(FinArReceiptReceivableLinkBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的收款单与应收单核销关系列表
     *
     * @param bo 查询条件
     * @return 收款单与应收单核销关系列表
     */
    List<FinArReceiptReceivableLinkVo> queryList(FinArReceiptReceivableLinkBo bo);

    /**
     * 新增收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否新增成功
     */
    Boolean insertByBo(FinArReceiptReceivableLinkBo bo);

    /**
     * 修改收款单与应收单核销关系
     *
     * @param bo 收款单与应收单核销关系
     * @return 是否修改成功
     */
    Boolean updateByBo(FinArReceiptReceivableLinkBo bo);

    /**
     * 校验并批量删除收款单与应收单核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
