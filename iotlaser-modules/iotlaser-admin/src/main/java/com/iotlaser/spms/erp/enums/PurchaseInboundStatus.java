package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购入库状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum PurchaseInboundStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "入库单已创建，但未通知仓库"),
    PENDING_WAREHOUSE("pending_warehouse", "待入库", "已通知仓库，等待仓库执行入库操作"),
    COMPLETED("completed", "已入库", "仓库已完成所有入库操作，库存已增加"),
    CANCELLED("cancelled", "已取消", "入库单已取消"),
    ;

    public final static String DICT_CODE = "erp_purchase_inbound_status";
    public final static String DICT_NAME = "采购入库状态";
    public final static String DICT_DESC = "管理采购入库单的流程状态，从草稿创建到入库完成的完整流程";
    @EnumValue
    private final String value;
    private final String name;
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static PurchaseInboundStatus getByValue(String value) {
        for (PurchaseInboundStatus inboundStatus : values()) {
            if (inboundStatus.getValue().equals(value)) {
                return inboundStatus;
            }
        }
        return null;
    }

    /**
     * 检查状态是否可以转换
     *
     * @param fromStatus 源状态
     * @param toStatus   目标状态
     * @return 是否可以转换
     */
    public static boolean canTransition(PurchaseInboundStatus fromStatus, PurchaseInboundStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        switch (fromStatus) {
            case DRAFT:
                return toStatus == PENDING_WAREHOUSE || toStatus == CANCELLED;
            case PENDING_WAREHOUSE:
                return toStatus == COMPLETED || toStatus == CANCELLED;
            case COMPLETED:
            case CANCELLED:
                return false; // 终态，不能再转换
            default:
                return false;
        }
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
