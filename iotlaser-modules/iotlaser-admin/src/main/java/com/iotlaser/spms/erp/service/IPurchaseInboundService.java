package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 采购入库领域服务接口
 * <p>
 * 定义了采购入库聚合的核心业务能力, 同时隐藏底层复杂的业务逻辑和数据持久化细节。
 *
 * <AUTHOR> Kai
 * @version 1.2
 * @since 2025-07-17
 */
public interface IPurchaseInboundService {

    /**
     * 根据ID查询采购入库单
     *
     * @param inboundId 采购入库单的唯一主键ID
     * @return 采购入库单的详细视图对象 (VO)，若不存在则返回 {@code null}
     */
    PurchaseInboundVo queryById(Long inboundId);

    /**
     * 分页查询采购入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封装了分页结果的 TableDataInfo 对象
     */
    TableDataInfo<PurchaseInboundVo> queryPageList(PurchaseInboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购入库单列表
     *
     * @param bo 查询条件业务对象 (BO)
     * @return 采购入库单视图对象 (VO) 的列表
     */
    List<PurchaseInboundVo> queryList(PurchaseInboundBo bo);

    /**
     * 新增采购入库单
     *
     * @param bo 包含新采购入库单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    PurchaseInboundVo insertByBo(PurchaseInboundBo bo);

    /**
     * 修改采购入库单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    PurchaseInboundVo updateByBo(PurchaseInboundBo bo);

    /**
     * 校验并批量删除采购入库单
     *
     * @param ids     待删除的采购入库单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查上游ID是否存在关联的采购入库单
     *
     * @param directSourceId 上游 ID
     * @return 如果存在则返回 {@code true}，否则返回 {@code false}
     */
    Boolean existsByDirectSourceId(Long directSourceId);

    /**
     * 根据上游ID查询所有关联的采购入库单
     *
     * @param directSourceId 上游ID
     * @return 采购入库单实体列表
     */
    List<PurchaseInbound> queryByDirectSourceId(Long directSourceId);

    /**
     * 根据入库单ID查询其所有明细项
     *
     * @param inboundId 入库单ID
     * @return 入库单明细实体列表
     */
    List<PurchaseInboundItem> queryByInboundId(Long inboundId);

    /**
     * 确认采购入库单
     *
     * @param inboundId 待确认的入库单ID
     * @return 操作成功返回 {@code true}
     */
    Boolean confirmInbound(Long inboundId);

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 待确认的入库单ID集合
     * @return 操作成功返回 {@code true}
     */
    Boolean batchConfirmInbounds(Collection<Long> inboundIds);

    /**
     * 完成采购入库
     *
     * @param inboundId 已完成入库的入库单ID
     * @return 操作成功返回 {@code true}
     */
    Boolean completeInbound(Long inboundId);

    /**
     * 手动从采购入库单创建仓库入库单
     *
     * @param puerchaseInboundId 采购入库单ID
     */
    Boolean createInbound(Long puerchaseInboundId);

    /**
     * 手动从采购入库单创建采购退货单
     *
     * @param inboundId 已完成的采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    Boolean createPurchaseReturn(Long inboundId);

    /**
     * 手动从采购入库单创建财务应付单
     *
     * @param inboundId 已完成的采购入库单ID
     * @return 操作成功返回 {@code true}
     */
    Boolean createFinApInvoice(Long inboundId);

    /**
     * 从采购订单创建采购入库单
     *
     * @param purchaseOrder 已确认的采购订单实体
     * @return 操作成功返回 {@code true}
     */
    Boolean createFromPurchaseOrder(PurchaseOrder purchaseOrder);

}
