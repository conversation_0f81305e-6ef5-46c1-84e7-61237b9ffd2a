package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import com.iotlaser.spms.mes.config.ProductionConfig;
import com.iotlaser.spms.mes.domain.ProductionOrder;
import com.iotlaser.spms.mes.domain.bo.ProductionOrderBo;
import com.iotlaser.spms.mes.domain.vo.ProductionOrderVo;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.mes.mapper.ProductionOrderMapper;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.MES_PRODUCTION_ORDER_CODE;

/**
 * 生产订单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionOrderServiceImpl implements IProductionOrderService {

    private final ProductionOrderMapper baseMapper;
    private final Gen gen;
    private final ProductionConfig productionConfig;

    // TODO: [DDD重构-跨聚合调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，聚合根Service不应该直接依赖其他聚合根Service
    // 当前保留这些依赖以维持业务功能完整性，后续需要重构为：
    // 使用领域事件处理跨聚合的业务协调（如从销售订单创建生产订单）
    // 使用应用服务层协调多个聚合根的操作
    // 将只读查询操作移到查询服务中
    private final ISaleOrderService saleOrderService;  // TEMP: 跨聚合调用，需要重构
    private final IProductService productService;      // TEMP: 基础数据查询

    /**
     * 查询生产订单
     *
     * @param orderId 主键
     * @return 生产订单
     */
    @Override
    public ProductionOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * 分页查询生产订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产订单分页列表
     */
    @Override
    public TableDataInfo<ProductionOrderVo> queryPageList(ProductionOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionOrder> lqw = buildQueryWrapper(bo);
        Page<ProductionOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产订单列表
     *
     * @param bo 查询条件
     * @return 生产订单列表
     */
    @Override
    public List<ProductionOrderVo> queryList(ProductionOrderBo bo) {
        LambdaQueryWrapper<ProductionOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionOrder> buildQueryWrapper(ProductionOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionOrder::getOrderId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), ProductionOrder::getOrderCode, bo.getOrderCode());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), ProductionOrder::getOrderType, bo.getOrderType());
        lqw.eq(bo.getSourceId() != null, ProductionOrder::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), ProductionOrder::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(ProductionOrder::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, ProductionOrder::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), ProductionOrder::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(ProductionOrder::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getProductId() != null, ProductionOrder::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionOrder::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionOrder::getProductName, bo.getProductName());
        lqw.eq(bo.getBomId() != null, ProductionOrder::getBomId, bo.getBomId());
        lqw.eq(StringUtils.isNotBlank(bo.getBomCode()), ProductionOrder::getBomCode, bo.getBomCode());
        lqw.like(StringUtils.isNotBlank(bo.getBomName()), ProductionOrder::getBomName, bo.getBomName());
        // ✅ 启用：数量范围查询支持
        // 生产数量范围查询
        lqw.between(params.get("minQuantity") != null && params.get("maxQuantity") != null,
            ProductionOrder::getQuantity, params.get("minQuantity"), params.get("maxQuantity"));
        // 完工数量范围查询
        lqw.between(params.get("minFinishQuantity") != null && params.get("maxFinishQuantity") != null,
            ProductionOrder::getFinishQuantity, params.get("minFinishQuantity"), params.get("maxFinishQuantity"));

        // ✅ 优化：将日期字段改为范围查询，更符合实际业务需求
        // 下达时间范围查询
        lqw.between(params.get("beginReleaseTime") != null && params.get("endReleaseTime") != null,
            ProductionOrder::getReleaseTime, params.get("beginReleaseTime"), params.get("endReleaseTime"));
        // 计划开始日期范围查询
        lqw.between(params.get("beginPlannedStartDate") != null && params.get("endPlannedStartDate") != null,
            ProductionOrder::getPlannedStartDate, params.get("beginPlannedStartDate"), params.get("endPlannedStartDate"));
        // 计划结束日期范围查询
        lqw.between(params.get("beginPlannedEndDate") != null && params.get("endPlannedEndDate") != null,
            ProductionOrder::getPlannedEndDate, params.get("beginPlannedEndDate"), params.get("endPlannedEndDate"));
        // 实际开始时间范围查询
        lqw.between(params.get("beginActualStartTime") != null && params.get("endActualStartTime") != null,
            ProductionOrder::getActualStartTime, params.get("beginActualStartTime"), params.get("endActualStartTime"));
        // 实际结束时间范围查询
        lqw.between(params.get("beginActualEndTime") != null && params.get("endActualEndTime") != null,
            ProductionOrder::getActualEndTime, params.get("beginActualEndTime"), params.get("endActualEndTime"));
        lqw.eq(bo.getOrderStatus() != null, ProductionOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionOrder::getStatus, bo.getStatus());

        // ✅ 优化：移除原有的不正确的日期范围查询逻辑，已在上面使用标准的between查询替代

        return lqw;
    }

    /**
     * 新增生产订单
     *
     * @param bo 生产订单
     * @return 创建的生产订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(ProductionOrderBo bo) {
        try {
            // 生成订单编码
            if (StringUtils.isEmpty(bo.getOrderCode())) {
                bo.setOrderCode(gen.code(MES_PRODUCTION_ORDER_CODE));
            }

            // 设置初始状态
            if (bo.getOrderStatus() == null) {
                bo.setOrderStatus(ProductionOrderStatus.DRAFT);
            }

            // 设置初始完工数量
            if (bo.getFinishQuantity() == null) {
                bo.setFinishQuantity(BigDecimal.ZERO);
            }

            // 设置计划时间
            if (bo.getPlannedStartDate() == null) {
                bo.setPlannedStartDate(LocalDate.now());
            }
            if (bo.getPlannedEndDate() == null) {
                bo.setPlannedEndDate(LocalDate.now().plusDays(7)); // 默认7天
            }

            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            ProductionOrder add = MapstructUtils.convert(bo, ProductionOrder.class);
            validEntityBeforeSave(add);

            // 插入数据库
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setOrderId(add.getOrderId());
                log.info("新增生产订单成功：{}", add.getOrderCode());
            } else {
                if (add.getSourceType() == null || add.getSourceType() == SourceType.PRODUCTION_ORDER) {
                    add.setSourceId(add.getOrderId());
                    add.setSourceCode(add.getOrderCode());
                    add.setSourceType(SourceType.PRODUCTION_ORDER);
                }
                if (add.getDirectSourceType() == null || add.getDirectSourceType() == DirectSourceType.PRODUCTION_ORDER) {
                    add.setDirectSourceId(add.getOrderId());
                    add.setDirectSourceCode(add.getOrderCode());
                    add.setDirectSourceType(DirectSourceType.PRODUCTION_ORDER);
                }
                baseMapper.updateById(add);
            }
            return flag;
        } catch (Exception e) {
            log.error("新增生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("新增生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产订单
     *
     * @param bo 生产订单
     * @return 修改后的生产订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(ProductionOrderBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            ProductionOrder update = MapstructUtils.convert(bo, ProductionOrder.class);
            validEntityBeforeSave(update);

            // 更新数据库
            boolean flag = baseMapper.updateById(update) > 0;
            if (flag) {
                log.info("修改生产订单成功：{}", update.getOrderCode());
            }
            return flag;
        } catch (Exception e) {
            log.error("修改生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("修改生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionOrder entity) {
        // 校验生产订单编号唯一性
        if (StringUtils.isNotBlank(entity.getOrderCode())) {
            LambdaQueryWrapper<ProductionOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionOrder::getOrderCode, entity.getOrderCode());
            if (entity.getOrderId() != null) {
                wrapper.ne(ProductionOrder::getOrderId, entity.getOrderId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("生产订单编号已存在：" + entity.getOrderCode());
            }
        }

        // 校验计划时间
        if (entity.getPlannedStartDate() != null && entity.getPlannedEndDate() != null) {
            if (entity.getPlannedStartDate().isAfter(entity.getPlannedEndDate())) {
                throw new ServiceException("计划开始时间不能晚于计划结束时间");
            }
        }
    }

    /**
     * 保存前的数据填充
     */
    private void redundancyEntityBeforeSave(ProductionOrder entity) {

    }

    /**
     * 保存后的数据填充
     */
    private void redundancyEntityAfterSave(ProductionOrder entity) {

    }

    /**
     * 校验并批量删除生产订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验生产订单是否可以删除
            List<ProductionOrder> orders = baseMapper.selectByIds(ids);
            for (ProductionOrder order : orders) {
                // 检查生产订单状态，只有草稿状态的订单才能删除
                if (ProductionOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("生产订单【" + order.getOrderCode() + "】状态为【" +
                        order.getOrderStatus() + "】，不允许删除");
                }

                // 检查是否有关联的生产入库单
                // TODO: 添加productionInboundService依赖注入
                // if (productionInboundService.existsByOrderId(order.getOrderId())) {
                //    throw new ServiceException("生产订单【" + order.getOrderCode() + "】存在关联的生产入库单，不允许删除");
                // }

                // 检查是否有关联的生产退料单
                // TODO: 添加productionReturnService依赖注入
                // if (productionReturnService.existsByOrderId(order.getOrderId())) {
                //    throw new ServiceException("生产订单【" + order.getOrderCode() + "】存在关联的生产退料单，不允许删除");
                // }

                log.info("删除生产订单校验通过：{}", order.getOrderCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除生产订单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 下达生产订单
     *
     * @param orderId 订单ID
     * @return 是否下达成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean releaseOrder(Long orderId) {
        ProductionOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 校验状态
        if (ProductionOrderStatus.DRAFT != order.getOrderStatus()) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】状态为【" +
                order.getOrderStatus() + "】，不允许下达");
        }

        // TODO: 集成数据权限模块，确保用户只能下达有权限的生产订单
        // 需要实现：部门权限、工厂权限、产品权限等多维度权限控制
        validateDataPermission(order, "RELEASE");

        try {
            // 更新状态为已下达
            order.setOrderStatus(ProductionOrderStatus.RELEASED);
            order.setPlannedStartDate(LocalDate.now()); // 设置计划开始时间

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("下达生产订单失败：订单【" + order.getOrderCode() + "】");
            }

            log.info("生产订单【{}】下达成功", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("生产订单【{}】下达失败：{}", order.getOrderCode(), e.getMessage(), e);
            throw new ServiceException("下达生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 批量下达生产订单
     *
     * @param orderIds 订单ID集合
     * @return 是否下达成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchReleaseOrders(Collection<Long> orderIds) {
        for (Long orderId : orderIds) {
            releaseOrder(orderId);
        }
        return true;
    }

    /**
     * 开始生产
     *
     * @param orderId 订单ID
     * @return 是否开始成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean startProduction(Long orderId) {
        ProductionOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 校验状态
        if (!ProductionOrderStatus.RELEASED.equals(order.getOrderStatus())) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】状态为【" +
                order.getOrderStatus() + "】，不允许开始生产");
        }

        try {
            // 更新状态为生产中
            order.setOrderStatus(ProductionOrderStatus.IN_PROGRESS);
            order.setActualStartTime(LocalDateTime.now());

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("开始生产失败：订单【" + order.getOrderCode() + "】");
            }

            // 同步更新销售订单状态为生产中
            if (order.getSourceId() != null) {
                updateSaleOrderStatus(order.getSourceId(), "IN_PRODUCTION");
            }

            log.info("生产订单【{}】开始生产", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("生产订单【{}】开始生产失败：{}", order.getOrderCode(), e.getMessage(), e);
            throw new ServiceException("开始生产失败：" + e.getMessage());
        }
    }

    /**
     * 完工入库
     *
     * @param orderId        订单ID
     * @param finishQuantity 完工数量
     * @return 是否完工成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean finishProduction(Long orderId, BigDecimal finishQuantity) {
        ProductionOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 校验状态
        if (!ProductionOrderStatus.IN_PROGRESS.equals(order.getOrderStatus()) &&
            !ProductionOrderStatus.PARTIALLY_COMPLETED.equals(order.getOrderStatus())) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】状态为【" +
                order.getOrderStatus() + "】，不允许完工入库");
        }

        // 校验完工数量
        if (finishQuantity == null || finishQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("完工数量必须大于0");
        }

        BigDecimal currentFinishQuantity = order.getFinishQuantity() != null ? order.getFinishQuantity() : BigDecimal.ZERO;
        BigDecimal newFinishQuantity = currentFinishQuantity.add(finishQuantity);

        // 超产控制：检查完工数量是否超过计划生产数量
        if (newFinishQuantity.compareTo(order.getQuantity()) > 0) {
            // 计算超产数量和比例
            BigDecimal overQuantity = newFinishQuantity.subtract(order.getQuantity());
            BigDecimal overPercentage = overQuantity.divide(order.getQuantity(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

            // ✅ 启用：使用配置化的允许超产比例
            BigDecimal allowedOverPercentage = productionConfig.getOrder().getAllowedOverPercentage();

            if (overPercentage.compareTo(allowedOverPercentage) > 0) {
                throw new ServiceException(String.format(
                    "完工数量超产过多：计划数量[%s]，当前完工[%s]，本次完工[%s]，超产比例[%s%%]，允许超产比例[%s%%]",
                    order.getQuantity(), currentFinishQuantity, finishQuantity,
                    overPercentage.setScale(2, RoundingMode.HALF_UP), allowedOverPercentage
                ));
            } else {
                // 在允许范围内的超产，记录警告日志
                log.warn("生产订单【{}】存在超产：计划数量[{}]，完工数量[{}]，超产比例[{}%]",
                    order.getOrderCode(), order.getQuantity(), newFinishQuantity,
                    overPercentage.setScale(2, RoundingMode.HALF_UP));
            }
        }

        try {
            // 更新完工数量
            order.setFinishQuantity(newFinishQuantity);

            // 判断是否全部完工
            if (newFinishQuantity.compareTo(order.getQuantity()) >= 0) {
                order.setOrderStatus(ProductionOrderStatus.COMPLETED);
                order.setActualEndTime(LocalDateTime.now());
                log.info("生产订单【{}】全部完工", order.getOrderCode());

                // 同步更新销售订单状态为待发货
                if (order.getSourceId() != null) {
                    updateSaleOrderStatus(order.getSourceId(), "READY_TO_SHIP");
                }
            } else {
                order.setOrderStatus(ProductionOrderStatus.PARTIALLY_COMPLETED);
                log.info("生产订单【{}】部分完工，完工数量：{}", order.getOrderCode(), newFinishQuantity);
            }

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("更新完工数量失败：订单【" + order.getOrderCode() + "】");
            }

            return true;
        } catch (Exception e) {
            log.error("生产订单【{}】完工入库失败：{}", order.getOrderCode(), e.getMessage(), e);
            throw new ServiceException("完工入库失败：" + e.getMessage());
        }
    }

    /**
     * 关闭生产订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean closeOrder(Long orderId) {
        ProductionOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 校验状态，只有已完工的订单才能关闭
        if (!ProductionOrderStatus.COMPLETED.equals(order.getOrderStatus())) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】状态为【" +
                order.getOrderStatus() + "】，不允许关闭");
        }

        // 更新状态为已关闭
        order.setOrderStatus(ProductionOrderStatus.CLOSED);
        boolean result = baseMapper.updateById(order) > 0;

        if (result) {
            log.info("生产订单【{}】关闭成功", order.getOrderCode());
        }

        return result;
    }

    /**
     * 根据销售订单创建生产订单
     *
     * @param saleOrderId 销售订单ID
     * @return 创建的生产订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromSaleOrder(Long saleOrderId) {
        // 根据实际的销售订单服务来获取销售订单信息
        try {
            // TODO: 集成销售订单模块，实现从销售订单创建生产订单
            // 需要实现：
            // 根据销售订单ID获取销售订单详细信息
            // 验证销售订单状态是否允许创建生产订单
            // 根据销售订单信息创建生产订单
            // 自动填充产品、数量、交期、BOM等信息
            // 建立销售订单与生产订单的关联关系

            // 获取销售订单信息
            SaleOrderVo saleOrder = saleOrderService.queryById(saleOrderId);
            if (saleOrder == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 验证销售订单状态是否允许创建生产订单
            if (!"CONFIRMED".equals(saleOrder.getOrderStatus()) && !"PENDING_PRODUCTION".equals(saleOrder.getOrderStatus())) {
                throw new ServiceException("销售订单【" + saleOrder.getOrderCode() + "】状态不允许创建生产订单");
            }

            // 创建生产订单
            ProductionOrder productionOrder = new ProductionOrder();
            productionOrder.setOrderCode(gen.code(MES_PRODUCTION_ORDER_CODE));
            productionOrder.setOrderType("SALE_ORDER"); // 销售订单类型
            productionOrder.setSourceId(saleOrder.getOrderId());
            productionOrder.setSourceCode(saleOrder.getOrderCode());

            // TODO 需完善: 从销售订单明细获取产品信息和数量
            // 当前简化实现：使用销售订单的主要产品信息
            // 实际应该遍历销售订单明细，为每个产品创建对应的生产订单
            /*if (saleOrder.getItems() != null && !saleOrder.getItems().isEmpty()) {
                // 取第一个明细作为主要产品（简化实现）
                var firstItem = saleOrder.getItems().get(0);
                productionOrder.setProductId(firstItem.getProductId());
                productionOrder.setProductCode(firstItem.getProductCode());
                productionOrder.setProductName(firstItem.getProductName());
                productionOrder.setQuantity(firstItem.getQuantity());
            }*/

            productionOrder.setFinishQuantity(BigDecimal.ZERO);
            productionOrder.setPlannedStartDate(LocalDate.now());
            // 计划结束时间可以根据产品的生产周期来计算
            productionOrder.setPlannedEndDate(LocalDate.now().plusDays(7)); // 默认7天
            productionOrder.setOrderStatus(ProductionOrderStatus.DRAFT);
            productionOrder.setSummary("[销售订单" + saleOrder.getOrderCode() + "]");

            // 填充默认BOM信息
            fillDefaultBomInfo(productionOrder);

            // 插入生产订单
            boolean flag = baseMapper.insert(productionOrder) > 0;
            if (!flag) {
                throw new ServiceException("创建生产订单失败");
            }

            // 更新销售订单状态为待生产
            updateSaleOrderStatus(saleOrderId, "PENDING_PRODUCTION");

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新销售订单状态
     *
     * @param saleOrderId 销售订单ID
     * @param status      新状态
     */
    private void updateSaleOrderStatus(Long saleOrderId, String status) {
        try {
            // TODO: 调用销售订单服务更新状态
            // saleOrderService.updateStatus(saleOrderId, status);
            log.info("销售订单【{}】状态更新为【{}】", saleOrderId, status);
        } catch (Exception e) {
            log.error("更新销售订单状态失败：{}", e.getMessage(), e);
            // 状态更新失败不影响主流程，只记录日志
        }
    }

    /**
     * 填充默认BOM信息
     *
     * @param entity 生产订单
     */
    private void fillDefaultBomInfo(ProductionOrder entity) {
        try {
            if (entity.getProductId() != null) {
                // TODO: 集成BOM模块，自动填充产品的默认BOM信息
                // 需要实现：
                // 根据产品ID获取默认BOM（状态为ACTIVE的最新版本）
                // 自动填充BOM相关字段到生产订单
                // 验证BOM的有效性和完整性

                // 实现示例：
                // Bom defaultBom = bomService.getDefaultByProductId(entity.getProductId());
                // if (defaultBom != null) {
                //    entity.setBomId(defaultBom.getBomId());
                //    entity.setBomCode(defaultBom.getBomCode());
                //    entity.setBomName(defaultBom.getBomName());
                //    entity.setBomVersion(defaultBom.getVersion());
                // }

                log.info("填充产品【{}】的默认BOM信息", entity.getProductId());
            }
        } catch (Exception e) {
            log.error("填充默认BOM信息失败：{}", e.getMessage(), e);
            // 填充失败不影响主流程，只记录日志
        }
    }

    /**
     * 生产订单保存后的后续处理
     *
     * @param entity 生产订单
     */
    private void processProductionOrderAfterSave(ProductionOrder entity) {
        try {
            // TODO: 集成相关模块，实现生产订单保存后的自动化处理
            // 需要实现：
            // 根据BOM创建物料需求计划
            // 生成默认的工艺路线和工序计划
            // 创建生产任务和工作安排
            // 发送生产通知给相关人员
            // 触发库存预留和采购建议

            // 实现示例：
            // createMaterialRequirement(entity);  // 创建物料需求
            // createDefaultRouting(entity);       // 生成工艺路线
            // createProductionTasks(entity);      // 创建生产任务
            // sendProductionNotification(entity); // 发送通知

            log.info("生产订单【{}】保存后续处理完成", entity.getOrderCode());
        } catch (Exception e) {
            log.error("生产订单保存后续处理失败：{}", e.getMessage(), e);
            // 后续处理失败不影响主流程，只记录日志
        }
    }

    /**
     * 校验数据权限
     * TODO: 重要功能 - 数据权限控制，确保用户只能操作有权限的数据
     *
     * @param order     生产订单
     * @param operation 操作类型
     */
    private void validateDataPermission(ProductionOrder order, String operation) {
        try {
            // TODO: 集成数据权限模块，实现多维度权限控制
            // 需要实现：
            // 获取当前登录用户信息和权限范围
            // 检查用户是否有操作该生产订单的权限
            // 检查用户的部门权限（只能操作本部门的订单）
            // 检查用户的角色权限（不同操作需要不同角色）
            // 检查数据范围权限（只能操作指定工厂的订单）
            // 检查产品权限（只能操作有权限的产品）

            // 实现示例：
            // LoginUser loginUser = getLoginUser();
            // if (currentUser == null) {
            //    throw new ServiceException("用户未登录");
            // }
            //
            // // 检查部门权限
            // if (!hasDataScopePermission(currentUser, order.getDeptId())) {
            //    throw new ServiceException("无权限操作该生产订单：超出数据权限范围");
            // }
            //
            // // 检查角色权限
            // if ("RELEASE".equals(operation) && !hasRolePermission(currentUser, "production:order:release")) {
            //    throw new ServiceException("无权限下达生产订单：缺少相应角色权限");
            // }
            //
            // // 检查工厂权限
            // if (order.getFactoryId() != null && !hasFactoryPermission(currentUser, order.getFactoryId())) {
            //    throw new ServiceException("无权限操作该工厂的生产订单");
            // }

            log.debug("生产订单【{}】数据权限校验通过，操作：{}", order.getOrderCode(), operation);
        } catch (Exception e) {
            log.error("生产订单【{}】数据权限校验失败：{}", order.getOrderCode(), e.getMessage(), e);
            throw new ServiceException("数据权限校验失败：" + e.getMessage());
        }
    }

    /**
     * 确认生产订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        try {
            ProductionOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("生产订单不存在");
            }

            // 校验订单状态
            if (ProductionOrderStatus.DRAFT != order.getOrderStatus()) {
                throw new ServiceException("只有草稿状态的订单才能确认");
            }

            // 校验数据权限
            validateDataPermission(order, "CONFIRM");

            // 校验订单明细和物料可用性
            validateOrderForConfirm(order);

            // 更新订单状态
            order.setOrderStatus(ProductionOrderStatus.CONFIRMED);
            String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
            order.setRemark(currentRemark + " [确认时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("确认生产订单失败");
            }

            log.info("确认生产订单成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("确认生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("确认生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 批量确认生产订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmOrders(Collection<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                throw new ServiceException("订单ID集合不能为空");
            }

            int successCount = 0;
            for (Long orderId : orderIds) {
                try {
                    confirmOrder(orderId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量确认生产订单失败，订单ID：{}，错误：{}", orderId, e.getMessage());
                }
            }

            if (successCount == 0) {
                throw new ServiceException("批量确认失败，没有订单被成功确认");
            }

            log.info("批量确认生产订单完成：成功【{}】个，总计【{}】个", successCount, orderIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量确认生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("批量确认生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 取消生产订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, String cancelReason) {
        try {
            ProductionOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("生产订单不存在");
            }

            // 校验订单状态
            if (ProductionOrderStatus.CANCELLED.equals(order.getOrderStatus()) ||
                ProductionOrderStatus.CLOSED.equals(order.getOrderStatus())) {
                throw new ServiceException("订单已取消或已关闭，不能重复操作");
            }

            // 校验数据权限
            validateDataPermission(order, "CANCEL");

            // 校验是否可以取消
            validateOrderForCancel(order);

            // 更新订单状态和取消原因
            order.setOrderStatus(ProductionOrderStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
                order.setRemark(currentRemark + " [取消原因：" + cancelReason + "，取消时间：" + DateUtils.getTime() + "]");
            }

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("取消生产订单失败");
            }

            // 同步更新销售订单状态为已确认（回退到可重新安排生产）
            if (order.getSourceId() != null) {
                updateSaleOrderStatus(order.getSourceId(), "CONFIRMED");
            }

            log.info("取消生产订单成功：订单【{}】，原因【{}】", order.getOrderCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("取消生产订单失败：{}", e.getMessage(), e);
            throw new ServiceException("取消生产订单失败：" + e.getMessage());
        }
    }

    /**
     * 暂停生产
     *
     * @param orderId     订单ID
     * @param pauseReason 暂停原因
     * @return 是否暂停成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseProduction(Long orderId, String pauseReason) {
        try {
            ProductionOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("生产订单不存在");
            }

            // 校验订单状态
            if (!ProductionOrderStatus.IN_PROGRESS.equals(order.getOrderStatus())) {
                throw new ServiceException("只有生产中状态的订单才能暂停");
            }

            // 校验数据权限
            validateDataPermission(order, "PAUSE");

            // 更新订单状态和暂停原因
            order.setOrderStatus(ProductionOrderStatus.ON_HOLD);
            if (StringUtils.isNotBlank(pauseReason)) {
                String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
                order.setRemark(currentRemark + " [暂停原因：" + pauseReason + "，暂停时间：" + DateUtils.getTime() + "]");
            }

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("暂停生产失败");
            }

            log.info("暂停生产成功：订单【{}】，原因【{}】", order.getOrderCode(), pauseReason);
            return true;
        } catch (Exception e) {
            log.error("暂停生产失败：{}", e.getMessage(), e);
            throw new ServiceException("暂停生产失败：" + e.getMessage());
        }
    }

    /**
     * 恢复生产
     *
     * @param orderId 订单ID
     * @return 是否恢复成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resumeProduction(Long orderId) {
        try {
            ProductionOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("生产订单不存在");
            }

            // 校验订单状态
            if (!ProductionOrderStatus.ON_HOLD.equals(order.getOrderStatus())) {
                throw new ServiceException("只有暂停状态的订单才能恢复");
            }

            // 校验数据权限
            validateDataPermission(order, "RESUME");

            // 恢复订单状态为生产中
            order.setOrderStatus(ProductionOrderStatus.IN_PROGRESS);
            String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
            order.setRemark(currentRemark + " [恢复生产时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("恢复生产失败");
            }

            log.info("恢复生产成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("恢复生产失败：{}", e.getMessage(), e);
            throw new ServiceException("恢复生产失败：" + e.getMessage());
        }
    }

    /**
     * 完成生产
     *
     * @param orderId 订单ID
     * @return 是否完成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeProduction(Long orderId) {
        try {
            ProductionOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("生产订单不存在");
            }

            // 校验订单状态
            if (ProductionOrderStatus.IN_PROGRESS != order.getOrderStatus() &&
                ProductionOrderStatus.PARTIALLY_COMPLETED != order.getOrderStatus()) {
                throw new ServiceException("只有生产中或部分完工状态的订单才能完成");
            }

            // 校验数据权限
            validateDataPermission(order, "COMPLETE");

            // 校验是否可以完成
            validateOrderForComplete(order);

            // 更新订单状态
            // 修复enum类型转换：Entity中orderStatus是enum类型，直接设置enum值
            order.setOrderStatus(ProductionOrderStatus.COMPLETED);
            order.setActualEndTime(LocalDateTime.now());
            String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
            order.setRemark(currentRemark + " [完成生产时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("完成生产失败");
            }

            log.info("完成生产成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("完成生产失败：{}", e.getMessage(), e);
            throw new ServiceException("完成生产失败：" + e.getMessage());
        }
    }

    /**
     * 校验订单是否可以确认
     *
     * @param order 生产订单
     */
    private void validateOrderForConfirm(ProductionOrder order) {
        // 校验订单基本信息
        if (order.getProductId() == null) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】缺少产品信息");
        }
        if (order.getQuantity() == null || order.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】生产数量必须大于0");
        }
        if (order.getPlannedStartDate() == null) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】缺少计划开始时间");
        }

        // 校验物料可用性
        try {
            // 检查BOM中的物料是否有足够库存
            // TODO: 集成BOM模块获取完整的物料清单
            // 当前简化实现：检查主要产品的库存可用性

            BigDecimal productionQty = order.getQuantity();
            log.debug("生产订单【{}】需要生产数量：{}", order.getOrderCode(), productionQty);

            // 检查关键物料的采购状态
            // TODO: 集成采购模块检查物料采购状态
            log.debug("物料可用性校验通过");

        } catch (Exception e) {
            log.error("校验物料可用性失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
            throw new ServiceException("物料可用性校验失败：" + e.getMessage());
        }

        log.debug("生产订单【{}】确认校验通过", order.getOrderCode());
    }

    /**
     * 校验订单是否可以取消
     *
     * @param order 生产订单
     */
    private void validateOrderForCancel(ProductionOrder order) {
        // 检查是否已有生产进度
        if (ProductionOrderStatus.IN_PROGRESS == order.getOrderStatus() ||
            ProductionOrderStatus.PARTIALLY_COMPLETED == order.getOrderStatus()) {

            // TODO: 检查是否有关联的领料单、报工单等
            // 如果已有生产活动，需要特殊处理
            log.warn("生产订单【{}】已有生产进度，取消需要处理在制品和已领物料", order.getOrderCode());
        }

        log.debug("生产订单【{}】取消校验通过", order.getOrderCode());
    }

    /**
     * 校验订单是否可以完成
     *
     * @param order 生产订单
     */
    private void validateOrderForComplete(ProductionOrder order) {
        // 校验生产数量
        BigDecimal finishQuantity = order.getFinishQuantity() != null ? order.getFinishQuantity() : BigDecimal.ZERO;
        if (finishQuantity.compareTo(order.getQuantity()) < 0) {
            throw new ServiceException("生产订单【" + order.getOrderCode() + "】完工数量不足，无法完成");
        }

        // TODO: 校验质检状态
        // 检查是否所有产品都已通过质检

        log.debug("生产订单【{}】完成校验通过", order.getOrderCode());
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(ProductionOrderBo bo) {
        // 填充产品信息
        if (bo.getProductId() != null) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product != null) {
                bo.setProductCode(product.getProductCode());
                bo.setProductName(product.getProductName());
            }
        }

        // 填充销售订单信息（如果有关联）
        if (bo.getSourceId() != null) {
            SaleOrderVo saleOrder = saleOrderService.queryById(bo.getSourceId());
            if (saleOrder != null) {
                bo.setSourceCode(saleOrder.getOrderCode());
                //bo.setCustomerId(saleOrder.getCustomerId());
                //bo.setCustomerCode(saleOrder.getCustomerCode());
                //bo.setCustomerName(saleOrder.getCustomerName());
            }
        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(ProductionOrderBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // TODO 如果是新增，设置申请人
        if (bo.getOrderId() == null) {
            //bo.setApplicantId(currentUserId);
            //bo.setApplicantName(currentUserName);
        }

        // TODO 设置经办人（每次更新都更新）
        //bo.setHandlerId(currentUserId);
        //bo.setHandlerName(currentUserName);
    }
}
