package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.*;
import com.iotlaser.spms.erp.domain.bo.SaleReturnBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.erp.event.InboundEvent;
import com.iotlaser.spms.erp.event.SaleReturnEvent;
import com.iotlaser.spms.erp.mapper.SaleReturnItemMapper;
import com.iotlaser.spms.erp.mapper.SaleReturnMapper;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_SALE_RETURN_CODE;

/**
 * 销售退货单 服务层实现
 *
 * <AUTHOR> Kai
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleReturnServiceImpl implements ISaleReturnService {

    private final SaleReturnMapper baseMapper;
    private final SaleReturnItemMapper itemMapper;
    private final Gen gen;
    private final IInboundService inboundService;

    @Autowired
    @Lazy
    private ISaleOrderService saleOrderService;

    /**
     * {@inheritDoc}
     */
    @Override
    public SaleReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TableDataInfo<SaleReturnVo> queryPageList(SaleReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleReturn> lqw = buildQueryWrapper(bo);
        Page<SaleReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SaleReturnVo> queryList(SaleReturnBo bo) {
        LambdaQueryWrapper<SaleReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleReturn> buildQueryWrapper(SaleReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(SaleReturn::getReturnId);
        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), SaleReturn::getReturnCode, bo.getReturnCode());
        lqw.eq(bo.getSourceId() != null, SaleReturn::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), SaleReturn::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(SaleReturn::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, SaleReturn::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), SaleReturn::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(SaleReturn::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getCustomerId() != null, SaleReturn::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleReturn::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getReturnTime() != null, SaleReturn::getReturnTime, bo.getReturnTime());
        if (bo.getReturnStatus() != null) {
            lqw.eq(SaleReturn::getReturnStatus, bo.getReturnStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleReturn::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增销售退货单
     *
     * @param bo 包含新销售退货单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleReturnVo insertByBo(SaleReturnBo bo) {
        log.info("[insertByBo] - start");
        try {
            if (StringUtils.isEmpty(bo.getReturnCode())) {
                bo.setReturnCode(gen.code(ERP_SALE_RETURN_CODE));
            }
            if (bo.getReturnStatus() == null) {
                bo.setReturnStatus(SaleReturnStatus.DRAFT);
            }
            if (bo.getReturnTime() == null) {
                bo.setReturnTime(LocalDateTime.now());
            }
            fillRedundantFields(bo);

            SaleReturn add = MapstructUtils.convert(bo, SaleReturn.class);
            validEntityBeforeSave(add);

            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("创建失败");
            }
            bo.setReturnId(add.getReturnId());
            log.info("[insertByBo] - success, id: {}", add.getReturnId());
            return MapstructUtils.convert(add, SaleReturnVo.class);
        } catch (ServiceException se) {
            log.warn("[insertByBo] - validation error: {}", se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[insertByBo] - system error: {}", e.getMessage(), e);
            throw new ServiceException("创建失败，请联系管理员");
        }
    }

    /**
     * 修改销售退货单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回最新的视图对象 (VO)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleReturnVo updateByBo(SaleReturnBo bo) {
        log.info("[updateByBo] - start, id: {}", bo.getReturnId());
        try {
            fillRedundantFields(bo);
            SaleReturn update = MapstructUtils.convert(bo, SaleReturn.class);
            validEntityBeforeSave(update);

            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(bo.getReturnId());
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改失败，记录可能已被删除");
            }
            log.info("[updateByBo] - success, id: {}", update.getReturnId());
            return MapstructUtils.convert(update, SaleReturnVo.class);
        } catch (ServiceException se) {
            log.warn("[updateByBo] - validation error, id: {}, error: {}", bo.getReturnId(), se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[updateByBo] - system error, id: {}, error: {}", bo.getReturnId(), e.getMessage(), e);
            throw new ServiceException("修改失败，请联系管理员");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleReturn entity) {
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<SaleReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(SaleReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("销售退货单编码 [" + entity.getReturnCode() + "] 已存在");
            }
        }
    }

    /**
     * 校验并批量删除销售退货单
     *
     * @param ids     待删除的销售退货单主键ID集合
     * @param isValid 是否进行业务校验的开关。{@code true} 表示需要检查状态等删除条件
     * @return 操作成功返回 {@code true}，否则在业务校验不通过时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("[deleteWithValidByIds] - start, ids: {}, isValid: {}", ids, isValid);
        try {
            if (isValid) {
                List<SaleReturn> saleReturns = baseMapper.selectByIds(ids);
                for (SaleReturn saleReturn : saleReturns) {
                    if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT) {
                        throw new ServiceException("退货单 [" + saleReturn.getReturnCode() + "] 状态为“" + saleReturn.getReturnStatus().getDesc() + "”，仅草稿状态可删除");
                    }
                }
            } else {
                log.warn("[deleteWithValidByIds] - skipping validation");
            }
            itemMapper.deleteByReturnIds(ids);
            boolean result = baseMapper.deleteByIds(ids) > 0;
            if (result) {
                log.info("[deleteWithValidByIds] - success, deleted count: {}", ids.size());
            } else {
                log.warn("[deleteWithValidByIds] - no records deleted, ids: {}", ids);
            }
            return result;
        } catch (ServiceException se) {
            log.warn("[deleteWithValidByIds] - validation error: {}", se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[deleteWithValidByIds] - system error: {}", e.getMessage(), e);
            throw new ServiceException("删除失败，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Boolean existsByDirectSourceId(Long directSourceId) {
        return baseMapper.existsByDirectSourceId(directSourceId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SaleReturn> queryByDirectSourceId(Long directSourceId) {
        return baseMapper.queryByDirectSourceId(directSourceId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SaleReturnItem> queryItemByReturnId(Long returnId) {
        return itemMapper.queryByReturnId(returnId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReturn(Long returnId) {
        log.info("[confirmReturn] - start, id: {}", returnId);
        try {
            R<SaleReturn> vaildReturn = returnValid(returnId, false);
            if (R.isError(vaildReturn)) {
                throw new ServiceException(vaildReturn.getMsg());
            }
            SaleReturn saleReturn = vaildReturn.getData();
            if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT) {
                throw new ServiceException("退货单 [" + saleReturn.getReturnCode() + "] 状态为“" + saleReturn.getReturnStatus().getDesc() + "”，仅草稿状态可确认");
            }
            TaxCalculationResultBo calculated = itemMapper.calculateTotalAmount(returnId);
            SaleReturn update = new SaleReturn();
            update.setReturnId(returnId);
            update.setAmount(calculated.getAmount());
            update.setAmountExclusiveTax(calculated.getAmountExclusiveTax());
            update.setTaxAmount(calculated.getTaxAmount());
            update.setReturnStatus(SaleReturnStatus.AWAITING_RETURN);
            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("[confirmReturn] - success, id: {}", returnId);
            } else {
                throw new ServiceException("确认失败，记录可能已被删除");
            }
            return true;
        } catch (ServiceException se) {
            log.warn("[confirmReturn] - validation error, id: {}, error: {}", returnId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[confirmReturn] - system error, id: {}, error: {}", returnId, e.getMessage(), e);
            throw new ServiceException("确认失败，请联系管理员");
        }
    }

    /**
     * 完成销售退货
     *
     * @param returnId 待完成的销售退货单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeReturn(Long returnId) {
        try {
            R<SaleReturn> validBefore = returnValid(returnId, false);
            if (R.isError(validBefore)) {
                log.error("[completeReturn] - 前置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validBefore.getMsg());
            }
            SaleReturn beforeData = validBefore.getData();
            if (SaleReturnStatus.PENDING_WAREHOUSE != beforeData.getReturnStatus()) {
                throw new ServiceException("销售退货单 [" + beforeData.getReturnCode() + "] 状态为“" + beforeData.getReturnStatus().getDesc() + "”，仅待入库状态可完成");
            }
            // 更新beforeData内明细的实收退货数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<SaleReturn> validAfter = returnValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.warn("[completeReturn] - 后置验证失败: {}", validBefore.getMsg());
                throw new ServiceException(validAfter.getMsg());
            }
            // 更新销售退货单状态为已完成
            SaleReturn afterData = validAfter.getData();
            afterData.setReturnStatus(SaleReturnStatus.COMPLETED);
            String remark = String.format(" [手动完成-%s]", DateUtils.getTime());
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean completed = baseMapper.updateById(afterData) > 0;
            if (!completed) {
                throw new ServiceException("完成失败");
            }
            triggerNextStreamProcesses(afterData);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 手动从销售退货单创建仓库入库单
     *
     * @param returnId 已确认的销售退货单ID
     * @return 操作成功返回 {@code true}，失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createInbound(Long returnId) {
        try {
            log.info("[createInbound] - 销售退货单ID: {}", returnId);
            R<SaleReturn> returnValid = returnValid(returnId, false);
            if (R.isError(returnValid)) {
                throw new ServiceException(returnValid.getMsg());
            }
            SaleReturn saleReturn = returnValid.getData();
            boolean result = inboundService.createFromSaleReturn(saleReturn);
            if (!result) {
                throw new ServiceException("创建销售退货单失败");
            }
            log.info("[createInbound] - 成功 - 销售退货单ID: {}", returnId);
            return true;
        } catch (Exception e) {
            log.error("[createInbound] - 错误 - 销售退货单ID: {}", returnId, e);
            throw new ServiceException("创建销售退货单：" + e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReturn(Long returnId, String reason) {
        log.info("[cancelReturn] - start, id: {}, reason: {}", returnId, reason);
        try {
            SaleReturn saleReturn = baseMapper.selectById(returnId);
            if (saleReturn == null) {
                throw new ServiceException("ID为 [" + returnId + "] 的退货单不存在");
            }
            if (saleReturn.getReturnStatus() != SaleReturnStatus.DRAFT &&
                saleReturn.getReturnStatus() != SaleReturnStatus.AWAITING_RETURN) {
                throw new ServiceException("退货单 [" + saleReturn.getReturnCode() + "] 状态为“" + saleReturn.getReturnStatus().getDesc() + "”，无法取消");
            }
            SaleReturn update = new SaleReturn();
            update.setReturnId(returnId);
            update.setReturnStatus(SaleReturnStatus.CANCELLED);
            if (StringUtils.isNotBlank(reason)) {
                String newRemark = StringUtils.isNotBlank(saleReturn.getRemark()) ? saleReturn.getRemark() + " | " : "";
                newRemark += "取消原因: " + reason;
                update.setRemark(newRemark);
            }
            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("[cancelReturn] - success, id: {}", returnId);
            } else {
                throw new ServiceException("取消失败，记录可能已被删除");
            }
            return true;
        } catch (ServiceException se) {
            log.warn("[cancelReturn] - validation error, id: {}, error: {}", returnId, se.getMessage());
            throw se;
        } catch (Exception e) {
            log.error("[cancelReturn] - system error, id: {}, error: {}", returnId, e.getMessage(), e);
            throw new ServiceException("取消失败，请联系管理员");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromSaleOutbound(SaleOutbound saleOutbound) {
        try {
            if (SaleOutboundStatus.COMPLETED != saleOutbound.getOutboundStatus()) {
                throw new ServiceException("创建失败：销售退货单状态 [" + saleOutbound.getOutboundStatus().getName() + "] ，不能创建");
            }
            boolean exists = existsByDirectSourceId(saleOutbound.getOutboundId());
            if (exists) {
                throw new ServiceException("创建失败：销售退货单 [" + saleOutbound.getOutboundCode() + "] 已生成过仓库入库单，不能重复创建");
            }
            SaleReturn add = new SaleReturn();
            add.setReturnCode(gen.code(ERP_SALE_RETURN_CODE));
            add.setCustomerId(saleOutbound.getCustomerId());
            add.setCustomerName(saleOutbound.getCustomerName());
            add.setSourceId(saleOutbound.getSourceId());
            add.setSourceCode(saleOutbound.getSourceCode());
            add.setSourceType(saleOutbound.getSourceType());
            add.setDirectSourceId(saleOutbound.getOutboundId());
            add.setDirectSourceCode(saleOutbound.getOutboundCode());
            add.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);
            add.setReturnTime(LocalDateTime.now());
            add.setReturnStatus(SaleReturnStatus.DRAFT);
            add.setSummary("[销售出库单" + saleOutbound.getOutboundCode() + "]");

            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("创建主记录失败");
            }
            List<SaleReturnItem> returnItems = new ArrayList<>();
            for (SaleOutboundItem outboundItem : saleOutbound.getItems()) {
                SaleReturnItem returnItem = new SaleReturnItem();
                returnItem.setReturnId(add.getReturnId());
                returnItem.setProductId(outboundItem.getProductId());
                returnItem.setProductCode(outboundItem.getProductCode());
                returnItem.setProductName(outboundItem.getProductName());
                returnItem.setUnitId(outboundItem.getUnitId());
                returnItem.setUnitCode(outboundItem.getUnitCode());
                returnItem.setUnitName(outboundItem.getUnitName());
                returnItem.setQuantity(outboundItem.getQuantity());
                returnItem.setPrice(outboundItem.getPrice());
                returnItem.setPriceExclusiveTax(outboundItem.getPriceExclusiveTax());
                returnItem.setAmount(outboundItem.getAmount());
                returnItem.setAmountExclusiveTax(outboundItem.getAmountExclusiveTax());
                returnItem.setTaxRate(outboundItem.getTaxRate());
                returnItem.setTaxAmount(outboundItem.getTaxAmount());
                returnItem.setLocationId(outboundItem.getLocationId());
                returnItem.setLocationCode(outboundItem.getLocationCode());
                returnItem.setLocationName(outboundItem.getLocationName());
                returnItem.setRemark("[销售出库单" + saleOutbound.getOutboundCode() + "]");
                returnItems.add(returnItem);
            }
            if (!itemMapper.insertBatch(returnItems)) {
                throw new ServiceException("创建明细记录失败");
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 仓库入库完成事件
     *
     * @param event 事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void completeInboundEvent(InboundEvent event) {
        try {
            log.info("[completeInboundEvent] - 仓库入库事件: {}", event);
            Inbound inbound = event.getInbound();
            if (inbound == null || inbound.getDirectSourceType() != DirectSourceType.SALE_RETURN) {
                return;
            }
            Long returnId = inbound.getDirectSourceId();
            R<SaleReturn> validBefore = returnValid(returnId, false);
            if (R.isError(validBefore)) {
                log.warn("[completeInboundEvent] - 前置验证失败: {}", validBefore.getMsg());
                return;
            }
            SaleReturn beforeData = validBefore.getData();
            if (beforeData.getReturnStatus() == SaleReturnStatus.COMPLETED) {
                log.warn("[completeInboundEvent] - 销售退货单已完成，跳过更新: {}", beforeData);
                return;
            }
            // 更新beforeData内明细的实收退货数量
            updateActualQuantities(beforeData);
            // 校验更新后的beforeData数据
            R<SaleReturn> validAfter = returnValid(beforeData, true);
            if (R.isError(validAfter)) {
                log.warn("[completeInboundEvent] - 后置验证失败: {}", validBefore.getMsg());
                return;
            }
            // 更新销售退货单状态为已完成
            SaleReturn afterData = validAfter.getData();
            afterData.setReturnStatus(SaleReturnStatus.COMPLETED);
            String remark = String.format(" [自动完成-仓库入库-%s-%s]", inbound.getInboundCode(), DateUtils.getTime());
            afterData.setRemark(StringUtils.isNotBlank(afterData.getRemark()) ? afterData.getRemark() + remark : remark);
            boolean complete = baseMapper.updateById(afterData) > 0;
            if (!complete) {
                throw new ServiceException("更新失败");
            }
            triggerNextStreamProcesses(afterData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 不抛出异常，避免影响主流程，可后续增加重试或补偿机制
        }
    }

    /**
     * 更新销售退货单明细实收退货数量
     */
    private void updateActualQuantities(SaleReturn saleReturn) {
        log.debug("[updateActualQuantities] - start, returnId: {}", saleReturn.getReturnId());
        try {
            List<Inbound> inbounds = inboundService.queryCompleteByDirectSourceId(saleReturn.getReturnId(), DirectSourceType.SALE_RETURN);
            Map<Long, BigDecimal> finishQuantityMap = new HashMap<>();
            for (Inbound inbound : inbounds) {
                List<InboundItem> inboundItems = inboundService.queryItemByInboundId(inbound.getInboundId());
                for (InboundItem inboundItem : inboundItems) {
                    finishQuantityMap.merge(inboundItem.getDirectSourceItemId(), inboundItem.getFinishQuantity(), BigDecimal::add);
                }
            }
            List<SaleReturnItem> updates = new ArrayList<>();
            for (SaleReturnItem saleReturnItem : saleReturn.getItems()) {
                BigDecimal finishQuantity = finishQuantityMap.getOrDefault(saleReturnItem.getItemId(), BigDecimal.ZERO);
                if (finishQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    saleReturnItem.setFinishQuantity(finishQuantity);
                    updates.add(saleReturnItem);
                }
            }
            if (!updates.isEmpty()) {
                boolean resultItem = itemMapper.updateBatchById(updates);
                if (!resultItem) {
                    throw new ServiceException("更新明细实收数量失败");
                }
            }
            log.info("[updateActualQuantities] - success, returnId: {}", saleReturn.getReturnId());
        } catch (Exception e) {
            log.error("[updateActualQuantities] - error, returnId: {}, error: {}", saleReturn.getReturnId(), e.getMessage(), e);
            throw new ServiceException("更新明细实收数量失败：" + e.getMessage());
        }
    }

    /**
     * 触发后续业务流程
     */
    private void triggerNextStreamProcesses(SaleReturn saleReturn) {
        try {
            // 触发销售退货完成事件
            SaleReturnEvent event = new SaleReturnEvent();
            event.setSaleReturn(saleReturn);
            SpringUtils.context().publishEvent(event);
        } catch (Exception e) {
            log.error("[triggerNextStreamProcesses] - error, returnId: {}, error: {}", saleReturn.getReturnId(), e.getMessage(), e);
        }
    }

    /**
     * 校验退货单
     */
    private R<SaleReturn> returnValid(Long returnId, boolean isComplete) {
        if (returnId == null) {
            throw new ServiceException("必须提供ID");
        }
        SaleReturn saleReturn = baseMapper.selectById(returnId);
        return returnValid(saleReturn, isComplete);
    }

    /**
     * 校验退货单
     */
    private R<SaleReturn> returnValid(SaleReturn saleReturn, boolean isComplete) {
        if (saleReturn == null) {
            throw new ServiceException("退货单不存在");
        }
        List<SaleReturnItem> items = saleReturn.getItems();
        if (items == null || items.isEmpty()) {
            items = itemMapper.queryByReturnId(saleReturn.getReturnId());
            if (items == null || items.isEmpty()) {
                throw new ServiceException("退货单 [" + saleReturn.getReturnCode() + "] 没有明细项");
            }
        }
        for (SaleReturnItem item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("产品 [" + item.getProductName() + "] 退货数量必须大于0");
            }
            if (isComplete) {
                if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("产品 [" + item.getProductName() + "] 实退数量必须大于0");
                }
                if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                    throw new ServiceException("产品 [" + item.getProductName() + "] 实退数量与应退数量不符");
                }
            }
        }
        saleReturn.setItems(items);
        return R.ok(saleReturn);
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleReturnBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setHandlerId(loginUser.getUserId());
            bo.setHandlerName(loginUser.getNickname());
        }
    }
}
