package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 采购订单视图对象 erp_purchase_order
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseOrder.class)
public class PurchaseOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_order_status")
    private PurchaseOrderStatus orderStatus;

    /**
     * 申请人ID
     */
    @ExcelProperty(value = "申请人ID")
    private Long applicantId;

    /**
     * 申请人
     */
    @ExcelProperty(value = "申请人")
    private String applicantName;

    /**
     * 采购员ID
     */
    @ExcelProperty(value = "采购员ID")
    private Long handlerId;

    /**
     * 采购员
     */
    @ExcelProperty(value = "采购员")
    private String handlerName;

    /**
     * 审批人ID
     */
    @ExcelProperty(value = "审批人ID")
    private Long approverId;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approverName;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<PurchaseOrderItemVo> items;
}
