package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应付单（发票）状态枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApInvoiceStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "应付单已录入，但未提交"),
    UNPAID("unpaid", "未付款", "应付账款已确认，等待付款"),
    PARTIALLY_PAID("partially_paid", "部分付款", "已支付并核销了部分应付单金额"),
    FULLY_PAID("fully_paid", "全部付清", "应付单金额已全部核销完毕"),
    CANCELLED("cancelled", "已取消", "应付单在付款前被取消"),
    CLOSED("closed", "已关闭", "应付单完工且财务核销等结束，应付单归档");

    public final static String DICT_CODE = "erp_fin_ap_invoice_status";
    public final static String DICT_NAME = "应付单状态";
    public final static String DICT_DESC = "应付单的处理流程状态，从录入、审批到付款完成的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 应付单状态枚举
     */
    public static FinApInvoiceStatus getByValue(String value) {
        for (FinApInvoiceStatus invoiceStatus : values()) {
            if (invoiceStatus.getValue().equals(value)) {
                return invoiceStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

}
