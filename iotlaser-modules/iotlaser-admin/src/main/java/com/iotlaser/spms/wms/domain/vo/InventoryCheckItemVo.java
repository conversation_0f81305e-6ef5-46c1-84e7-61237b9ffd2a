package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.InventoryCheckItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 库存盘点明细视图对象 wms_inventory_check_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryCheckItem.class)
public class InventoryCheckItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 盘点明细ID
     */
    @ExcelProperty(value = "盘点明细ID")
    private Long itemId;

    /**
     * 盘点ID
     */
    @ExcelProperty(value = "盘点ID")
    private Long checkId;

    /**
     * 内部批次号/成品序列号
     */
    @ExcelProperty(value = "内部批次号/成品序列号")
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    @ExcelProperty(value = "供应商批次编号")
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    @ExcelProperty(value = "单品序列号")
    private String serialNumber;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 账面数量
     */
    @ExcelProperty(value = "账面数量")
    private BigDecimal bookQuantity;

    /**
     * 实盘数量
     */
    @ExcelProperty(value = "实盘数量")
    private BigDecimal actualQuantity;

    /**
     * 盈亏数量
     */
    @ExcelProperty(value = "盈亏数量")
    private BigDecimal differenceQuantity;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 盈亏金额
     */
    @ExcelProperty(value = "盈亏金额")
    private BigDecimal differenceAmount;

    /**
     * 盘点状态
     */
    @ExcelProperty(value = "盘点状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_item_status")
    private String itemStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
