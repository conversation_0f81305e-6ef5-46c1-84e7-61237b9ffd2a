package com.iotlaser.spms.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.mes.domain.bo.ProductionReportBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReportVo;
import com.iotlaser.spms.mes.service.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 生产报工记录
 *
 * <AUTHOR> Kai
 * @date 2025-06-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/mes/productionReport")
public class ProductionReportController extends BaseController {

    private final IProductionReportService productionReportService;
    private final IProductionWorkflowService productionWorkflowService;
    private final IRoutingIntegrationService routingIntegrationService;
    private final IQualityIntegrationService qualityIntegrationService;
    private final IEquipmentIntegrationService equipmentIntegrationService;

    /**
     * 查询生产报工记录列表
     */
    @SaCheckPermission("mes:productionReport:list")
    @GetMapping("/list")
    public TableDataInfo<ProductionReportVo> list(ProductionReportBo bo, PageQuery pageQuery) {
        return productionReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产报工记录列表
     */
    @SaCheckPermission("mes:productionReport:export")
    @Log(title = "生产报工记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductionReportBo bo, HttpServletResponse response) {
        List<ProductionReportVo> list = productionReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产报工记录", ProductionReportVo.class, response);
    }

    /**
     * 获取生产报工记录详细信息
     *
     * @param reportId 主键
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/{reportId}")
    public R<ProductionReportVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long reportId) {
        return R.ok(productionReportService.queryById(reportId));
    }

    /**
     * 新增生产报工记录
     */
    @SaCheckPermission("mes:productionReport:add")
    @Log(title = "生产报工记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductionReportBo bo) {
        return toAjax(productionReportService.insertByBo(bo));
    }

    /**
     * 修改生产报工记录
     */
    @SaCheckPermission("mes:productionReport:edit")
    @Log(title = "生产报工记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductionReportBo bo) {
        return toAjax(productionReportService.updateByBo(bo));
    }

    /**
     * 删除生产报工记录
     *
     * @param reportIds 主键串
     */
    @SaCheckPermission("mes:productionReport:remove")
    @Log(title = "生产报工记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{reportIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] reportIds) {
        return toAjax(productionReportService.deleteWithValidByIds(List.of(reportIds), true));
    }

    // ==================== 生产报工工作流程接口 (RESTful规范) ====================
    // 基于README_FLOW.md第7节：生产报工流程

    /**
     * 创建开工报工记录
     * POST /mes/productionReport/workflows/start-production
     * 基于README_FLOW.md第7.2节：开工 (首工序)
     */
    @SaCheckPermission("mes:productionReport:workflow")
    @Log(title = "开工报工", businessType = BusinessType.INSERT)
    @PostMapping("/workflows/start-production")
    public R<Map<String, Object>> createStartProductionWorkflow(@RequestBody Map<String, Object> params) {
        Long orderId = Long.valueOf(params.get("orderId").toString());
        Long productId = Long.valueOf(params.get("productId").toString());
        Long routingId = Long.valueOf(params.get("routingId").toString());
        Long routingStepId = Long.valueOf(params.get("routingStepId").toString());
        Long operatorId = Long.valueOf(params.get("operatorId").toString());

        Map<String, Object> result = productionWorkflowService.startProductionWorkflow(
            orderId, productId, routingId, routingStepId, operatorId);
        return R.ok(result);
    }

    /**
     * 创建物料消耗报工记录
     * POST /mes/productionReport/workflows/material-consumption
     * 基于README_FLOW.md第7.2节：物料消耗 (工序中)
     */
    @SaCheckPermission("mes:productionReport:workflow")
    @Log(title = "物料消耗报工", businessType = BusinessType.INSERT)
    @PostMapping("/workflows/material-consumption")
    public R<Map<String, Object>> createMaterialConsumptionWorkflow(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        @SuppressWarnings("unchecked")
        Map<Long, BigDecimal> materialConsumes = (Map<Long, BigDecimal>) params.get("materialConsumes");
        Long operatorId = Long.valueOf(params.get("operatorId").toString());

        Map<String, Object> result = productionWorkflowService.materialConsumeWorkflow(
            instanceCode, materialConsumes, operatorId);
        return R.ok(result);
    }

    /**
     * 创建完工报工记录
     * POST /mes/productionReport/workflows/complete-production
     * 基于README_FLOW.md第7.2节：完工 (当前工序)
     */
    @SaCheckPermission("mes:productionReport:workflow")
    @Log(title = "完工报工", businessType = BusinessType.INSERT)
    @PostMapping("/workflows/complete-production")
    public R<Map<String, Object>> createCompleteProductionWorkflow(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long routingStepId = Long.valueOf(params.get("routingStepId").toString());
        BigDecimal quantityGood = new BigDecimal(params.get("quantityGood").toString());
        BigDecimal quantityBad = new BigDecimal(params.get("quantityBad").toString());
        Long operatorId = Long.valueOf(params.get("operatorId").toString());

        Map<String, Object> result = productionWorkflowService.completeProductionWorkflow(
            instanceCode, routingStepId, quantityGood, quantityBad, operatorId);
        return R.ok(result);
    }

    /**
     * 获取产品实例追溯信息
     * GET /mes/productionReport/instances/{instanceCode}/traceability
     * 基于README_FLOW.md第7.3节：生产追溯流程（报工与组件消耗）
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/instances/{instanceCode}/traceability")
    public R<Map<String, Object>> getInstanceTraceability(@PathVariable String instanceCode) {
        Map<String, Object> traceInfo = productionWorkflowService.getCompleteTraceability(instanceCode);
        return R.ok(traceInfo);
    }

    /**
     * 获取产品实例生产进度
     * GET /mes/productionReport/instances/{instanceCode}/progress
     * 基于README_FLOW.md第7.2节：工序流转与报工
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/instances/{instanceCode}/progress")
    public R<Map<String, Object>> getInstanceProgress(@PathVariable String instanceCode) {
        // TODO: 实际项目中需要调用instanceService获取生产进度
        Map<String, Object> progressInfo = Map.of(
            "instanceCode", instanceCode,
            "message", "生产进度功能需要集成工艺路线、报工记录等模块数据"
        );
        return R.ok(progressInfo);
    }

    // ==================== 移动端简化接口 ====================

    /**
     * 移动端开工报工
     * POST /mes/productionReport/mobile/start-production
     * 适用于移动端扫码开工场景
     */
    @SaCheckPermission("mes:productionReport:mobile")
    @Log(title = "移动端开工报工", businessType = BusinessType.INSERT)
    @PostMapping("/mobile/start-production")
    public R<String> mobileStartProduction(@RequestParam Long orderId,
                                           @RequestParam Long routingStepId,
                                           @RequestParam Long operatorId) {
        try {
            String instanceCode = productionReportService.startProduction(orderId, routingStepId, operatorId);
            return R.ok(instanceCode, "开工成功");
        } catch (Exception e) {
            log.error("移动端开工报工失败：{}", e.getMessage(), e);
            return R.fail("开工失败：" + e.getMessage());
        }
    }

    /**
     * 移动端物料消耗报工
     * POST /mes/productionReport/mobile/material-consumption
     * 适用于移动端扫码消耗物料场景
     */
    @SaCheckPermission("mes:productionReport:mobile")
    @Log(title = "移动端物料消耗报工", businessType = BusinessType.INSERT)
    @PostMapping("/mobile/material-consumption")
    public R<Void> mobileMaterialConsumption(@RequestParam String instanceCode,
                                             @RequestParam String materialBatchCode,
                                             @RequestParam BigDecimal consumeQuantity,
                                             @RequestParam Long operatorId) {
        //TODO Boolean result = productionReportService.reportMaterialConsume(instanceCode, materialBatchCode, consumeQuantity, operatorId);
        Boolean result = true;
        return toAjax(result);
    }

    /**
     * 移动端完工报工
     * POST /mes/productionReport/mobile/complete-production
     * 适用于移动端完工报工场景
     */
    @SaCheckPermission("mes:productionReport:mobile")
    @Log(title = "移动端完工报工", businessType = BusinessType.INSERT)
    @PostMapping("/mobile/complete-production")
    public R<Void> mobileCompleteProduction(@RequestParam String instanceCode,
                                            @RequestParam Long routingStepId,
                                            @RequestParam BigDecimal quantityGood,
                                            @RequestParam(defaultValue = "0") BigDecimal quantityBad,
                                            @RequestParam Long operatorId) {
        //TODO Boolean result = productionReportService.completeProduction(instanceCode, routingStepId, quantityGood, quantityBad, operatorId);
        Boolean result = true;
        return toAjax(result);
    }

    // ==================== 高优先级功能接口 ====================

    // ========== 工艺路线集成功能 ==========

    /**
     * 获取下一工序信息
     * GET /mes/productionReport/routing/{routingId}/next-step/{currentStepId}
     */
    @SaCheckPermission("mes:productionReport:routing")
    @GetMapping("/routing/{routingId}/next-step/{currentStepId}")
    public R<Map<String, Object>> getNextRoutingStep(@PathVariable Long routingId,
                                                     @PathVariable Long currentStepId) {
        Map<String, Object> nextStepInfo = routingIntegrationService.getNextRoutingStep(routingId, currentStepId);
        return R.ok(nextStepInfo);
    }

    /**
     * 校验工序流转规则
     * POST /mes/productionReport/routing/validate-transition
     */
    @SaCheckPermission("mes:productionReport:routing")
    @PostMapping("/routing/validate-transition")
    public R<Boolean> validateStepTransition(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long fromStepId = Long.valueOf(params.get("fromStepId").toString());
        Long toStepId = Long.valueOf(params.get("toStepId").toString());

        Boolean isValid = routingIntegrationService.validateStepTransition(instanceCode, fromStepId, toStepId);
        return R.ok(isValid);
    }

    /**
     * 获取并行工序信息
     * GET /mes/productionReport/routing/{routingId}/parallel-steps/{sequenceNumber}
     */
    @SaCheckPermission("mes:productionReport:routing")
    @GetMapping("/routing/{routingId}/parallel-steps/{sequenceNumber}")
    public R<List<Map<String, Object>>> getParallelSteps(@PathVariable Long routingId,
                                                         @PathVariable Integer sequenceNumber) {
        List<Map<String, Object>> parallelSteps = routingIntegrationService.getParallelSteps(routingId, sequenceNumber);
        return R.ok(parallelSteps);
    }

    /**
     * 自动流转到下一工序
     * POST /mes/productionReport/routing/auto-transition
     */
    @SaCheckPermission("mes:productionReport:routing")
    @Log(title = "自动工序流转", businessType = BusinessType.UPDATE)
    @PostMapping("/routing/auto-transition")
    public R<Map<String, Object>> autoTransitionToNextStep(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long currentStepId = Long.valueOf(params.get("currentStepId").toString());

        Map<String, Object> transitionResult = routingIntegrationService.autoTransitionToNextStep(instanceCode, currentStepId);
        return R.ok(transitionResult);
    }

    /**
     * 获取工序流转历史
     * GET /mes/productionReport/routing/transition-history/{instanceCode}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/routing/transition-history/{instanceCode}")
    public R<List<Map<String, Object>>> getStepTransitionHistory(@PathVariable String instanceCode) {
        List<Map<String, Object>> transitionHistory = routingIntegrationService.getStepTransitionHistory(instanceCode);
        return R.ok(transitionHistory);
    }

    /**
     * 检查工序执行条件
     * GET /mes/productionReport/routing/check-conditions/{instanceCode}/{stepId}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/routing/check-conditions/{instanceCode}/{stepId}")
    public R<Map<String, Object>> checkStepExecutionConditions(@PathVariable String instanceCode,
                                                               @PathVariable Long stepId) {
        Map<String, Object> checkResult = routingIntegrationService.checkStepExecutionConditions(instanceCode, stepId);
        return R.ok(checkResult);
    }

    // ========== 质量管理集成功能 ==========

    /**
     * 创建工序质量检验记录
     * POST /mes/productionReport/quality/step-inspection
     */
    @SaCheckPermission("mes:productionReport:quality")
    @Log(title = "工序质量检验", businessType = BusinessType.INSERT)
    @PostMapping("/quality/step-inspection")
    public R<Map<String, Object>> createStepQualityInspection(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long stepId = Long.valueOf(params.get("stepId").toString());
        @SuppressWarnings("unchecked")
        Map<String, Object> inspectionData = (Map<String, Object>) params.get("inspectionData");
        Long inspectorId = Long.valueOf(params.get("inspectorId").toString());

        Map<String, Object> inspectionResult = qualityIntegrationService.createStepQualityInspection(
            instanceCode, stepId, inspectionData, inspectorId);
        return R.ok(inspectionResult);
    }

    /**
     * 处理不良品
     * POST /mes/productionReport/quality/handle-defects
     */
    @SaCheckPermission("mes:productionReport:quality")
    @Log(title = "不良品处理", businessType = BusinessType.INSERT)
    @PostMapping("/quality/handle-defects")
    public R<Map<String, Object>> handleDefectiveProducts(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long stepId = Long.valueOf(params.get("stepId").toString());
        BigDecimal defectQuantity = new BigDecimal(params.get("defectQuantity").toString());
        @SuppressWarnings("unchecked")
        List<String> defectCodes = (List<String>) params.get("defectCodes");
        Long handlerId = Long.valueOf(params.get("handlerId").toString());

        Map<String, Object> handleResult = qualityIntegrationService.handleDefectiveProducts(
            instanceCode, stepId, defectQuantity, defectCodes, handlerId);
        return R.ok(handleResult);
    }

    /**
     * 获取质量数据统计
     * GET /mes/productionReport/quality/statistics
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/quality/statistics")
    public R<Map<String, Object>> getQualityStatistics(@RequestParam(required = false) Long stepId,
                                                       @RequestParam LocalDate startDate,
                                                       @RequestParam LocalDate endDate) {
        Map<String, Object> statistics = qualityIntegrationService.getQualityStatistics(stepId, startDate, endDate);
        return R.ok(statistics);
    }

    /**
     * 创建返工流程
     * POST /mes/productionReport/quality/create-rework
     */
    @SaCheckPermission("mes:productionReport:quality")
    @Log(title = "创建返工流程", businessType = BusinessType.INSERT)
    @PostMapping("/quality/create-rework")
    public R<Long> createReworkProcess(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long stepId = Long.valueOf(params.get("stepId").toString());
        BigDecimal reworkQuantity = new BigDecimal(params.get("reworkQuantity").toString());

        Long reworkProcessId = qualityIntegrationService.createReworkProcess(instanceCode, stepId, reworkQuantity);
        return R.ok(reworkProcessId);
    }

    /**
     * 质量预警检查
     * GET /mes/productionReport/quality/alert-check/{stepId}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/quality/alert-check/{stepId}")
    public R<Map<String, Object>> checkQualityAlert(@PathVariable Long stepId) {
        Map<String, Object> alertInfo = qualityIntegrationService.checkQualityAlert(stepId);
        return R.ok(alertInfo);
    }

    /**
     * 获取产品实例质量档案
     * GET /mes/productionReport/quality/profile/{instanceCode}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/quality/profile/{instanceCode}")
    public R<Map<String, Object>> getInstanceQualityProfile(@PathVariable String instanceCode) {
        Map<String, Object> qualityProfile = qualityIntegrationService.getInstanceQualityProfile(instanceCode);
        return R.ok(qualityProfile);
    }

    // ========== 设备管理集成功能 ==========

    /**
     * 检查设备状态
     * GET /mes/productionReport/equipment/status/{equipmentId}
     */
    @SaCheckPermission("mes:productionReport:equipment")
    @GetMapping("/equipment/status/{equipmentId}")
    public R<Map<String, Object>> checkEquipmentStatus(@PathVariable Long equipmentId) {
        Map<String, Object> statusInfo = equipmentIntegrationService.checkEquipmentStatus(equipmentId);
        return R.ok(statusInfo);
    }

    /**
     * 创建设备使用记录
     * POST /mes/productionReport/equipment/usage-record
     */
    @SaCheckPermission("mes:productionReport:equipment")
    @Log(title = "设备使用记录", businessType = BusinessType.INSERT)
    @PostMapping("/equipment/usage-record")
    public R<Long> createEquipmentUsageRecord(@RequestBody Map<String, Object> params) {
        String instanceCode = params.get("instanceCode").toString();
        Long stepId = Long.valueOf(params.get("stepId").toString());
        Long equipmentId = Long.valueOf(params.get("equipmentId").toString());
        Long operatorId = Long.valueOf(params.get("operatorId").toString());
        String usageType = params.get("usageType").toString(); // START, END

        Long usageRecordId = equipmentIntegrationService.createEquipmentUsageRecord(
            instanceCode, stepId, equipmentId, operatorId, usageType);
        return R.ok(usageRecordId);
    }

    /**
     * 获取设备效率统计
     * GET /mes/productionReport/equipment/efficiency/{equipmentId}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/equipment/efficiency/{equipmentId}")
    public R<Map<String, Object>> getEquipmentEfficiencyStatistics(@PathVariable Long equipmentId,
                                                                   @RequestParam LocalDate startDate,
                                                                   @RequestParam LocalDate endDate) {
        Map<String, Object> statistics = equipmentIntegrationService.getEquipmentEfficiencyStatistics(
            equipmentId, startDate, endDate);
        return R.ok(statistics);
    }

    /**
     * 设备预防性维护检查
     * GET /mes/productionReport/equipment/maintenance-check/{equipmentId}
     */
    @SaCheckPermission("mes:productionReport:equipment")
    @GetMapping("/equipment/maintenance-check/{equipmentId}")
    public R<Map<String, Object>> checkPreventiveMaintenance(@PathVariable Long equipmentId) {
        Map<String, Object> maintenanceCheck = equipmentIntegrationService.checkPreventiveMaintenance(equipmentId);
        return R.ok(maintenanceCheck);
    }

    /**
     * 设备故障报告
     * POST /mes/productionReport/equipment/fault-report
     */
    @SaCheckPermission("mes:productionReport:equipment")
    @Log(title = "设备故障报告", businessType = BusinessType.INSERT)
    @PostMapping("/equipment/fault-report")
    public R<Long> reportEquipmentFault(@RequestBody Map<String, Object> params) {
        Long equipmentId = Long.valueOf(params.get("equipmentId").toString());
        String faultDescription = params.get("faultDescription").toString();
        String faultType = params.get("faultType").toString();
        Long reporterId = Long.valueOf(params.get("reporterId").toString());

        Long faultReportId = equipmentIntegrationService.reportEquipmentFault(
            equipmentId, faultDescription, faultType, reporterId);
        return R.ok(faultReportId);
    }

    /**
     * 获取设备实时状态监控
     * POST /mes/productionReport/equipment/real-time-status
     */
    @SaCheckPermission("mes:productionReport:query")
    @PostMapping("/equipment/real-time-status")
    public R<List<Map<String, Object>>> getEquipmentRealTimeStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> equipmentIds = (List<Long>) params.get("equipmentIds");

        List<Map<String, Object>> statusList = equipmentIntegrationService.getEquipmentRealTimeStatus(equipmentIds);
        return R.ok(statusList);
    }

    /**
     * 设备产能分析
     * GET /mes/productionReport/equipment/capacity-analysis/{equipmentId}
     */
    @SaCheckPermission("mes:productionReport:query")
    @GetMapping("/equipment/capacity-analysis/{equipmentId}")
    public R<Map<String, Object>> analyzeEquipmentCapacity(@PathVariable Long equipmentId,
                                                           @RequestParam LocalDate analysisDate) {
        Map<String, Object> capacityAnalysis = equipmentIntegrationService.analyzeEquipmentCapacity(
            equipmentId, analysisDate);
        return R.ok(capacityAnalysis);
    }
}
