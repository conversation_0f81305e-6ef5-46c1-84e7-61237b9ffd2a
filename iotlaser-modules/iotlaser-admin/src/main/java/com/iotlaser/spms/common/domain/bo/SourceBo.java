package com.iotlaser.spms.common.domain.bo;

import com.iotlaser.spms.common.domain.SourceItem;
import com.iotlaser.spms.pro.domain.bo.ProductBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SourceItem.class, reverseConvertGenerate = false)
public class SourceBo extends BaseEntity {
    /**
     * 主键
     */
    private Long id;
    /**
     * 来源类型
     */
    private String type;
    /**
     * 来源编号
     */
    private String code;
    /**
     * 来源时间
     */
    private Date time;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 产品查询条件
     */
    private ProductBo product;
    /**
     * 明细查询条件
     */
    private SourceItemBo item;
    /**
     * 批次
     */
    private List<SourceItemBo> items;
}
