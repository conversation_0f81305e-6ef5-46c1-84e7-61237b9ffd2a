package com.iotlaser.spms.mes.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 质量管理集成Service接口
 * 高优先级功能：质量管理集成
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
public interface IQualityIntegrationService {

    /**
     * 创建工序质量检验记录
     *
     * @param instanceCode   产品实例编码
     * @param stepId         工序ID
     * @param inspectionData 检验数据
     * @param inspectorId    检验员ID
     * @return 检验结果
     */
    Map<String, Object> createStepQualityInspection(String instanceCode, Long stepId,
                                                    Map<String, Object> inspectionData,
                                                    Long inspectorId);

    /**
     * 处理不良品
     *
     * @param instanceCode   产品实例编码
     * @param stepId         工序ID
     * @param defectQuantity 不良品数量
     * @param defectCodes    缺陷代码列表
     * @param handlerId      处理人ID
     * @return 处理结果
     */
    Map<String, Object> handleDefectiveProducts(String instanceCode, Long stepId,
                                                BigDecimal defectQuantity, List<String> defectCodes,
                                                Long handlerId);

    /**
     * 获取质量数据统计
     *
     * @param stepId    工序ID（可选）
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 质量统计数据
     */
    Map<String, Object> getQualityStatistics(Long stepId, LocalDate startDate, LocalDate endDate);

    /**
     * 创建返工流程
     *
     * @param instanceCode   产品实例编码
     * @param stepId         工序ID
     * @param reworkQuantity 返工数量
     * @return 返工流程ID
     */
    Long createReworkProcess(String instanceCode, Long stepId, BigDecimal reworkQuantity);

    /**
     * 质量预警检查
     *
     * @param stepId 工序ID
     * @return 预警信息
     */
    Map<String, Object> checkQualityAlert(Long stepId);

    /**
     * 获取产品实例质量档案
     *
     * @param instanceCode 产品实例编码
     * @return 质量档案
     */
    Map<String, Object> getInstanceQualityProfile(String instanceCode);
}
