package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 付款单状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApPaymentOrderStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "付款单申请已创建，但未提交"),
    UNAPPLIED("unapplied", "未付款", "付款单已确认，可以进行核销"),
    PARTIALLY_APPLIED("partially_applied", "部分核销", "付款单金额的一部分已用于核销发票"),
    FULLY_APPLIED("fully_applied", "全部核销", "付款单金额已全部分配给一张或多张发票"),
    CANCELLED("cancelled", "已取消", "付款单在执行前被取消"),
    CLOSED("closed", "已关闭", "付款单完工且财务核销等结束，付款单归档");

    public final static String DICT_CODE = "erp_fin_ap_payment_order_status";
    public final static String DICT_NAME = "付款单状态";
    public final static String DICT_DESC = "付款单的处理流程状态，从申请、审批到付款执行的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 付款单状态枚举
     */
    public static FinApPaymentOrderStatus getByValue(String value) {
        for (FinApPaymentOrderStatus paymentStatus : values()) {
            if (paymentStatus.getValue().equals(value)) {
                return paymentStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

}
