package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 付款单
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/finApPaymentOrder")
public class FinApPaymentOrderController extends BaseController {

    private final IFinApPaymentOrderService finApPaymentOrderService;

    /**
     * 查询付款单列表
     */
    @SaCheckPermission("erp:finApPaymentOrder:list")
    @GetMapping("/list")
    public TableDataInfo<FinApPaymentOrderVo> list(FinApPaymentOrderBo bo, PageQuery pageQuery) {
        return finApPaymentOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出付款单列表
     */
    @SaCheckPermission("erp:finApPaymentOrder:export")
    @Log(title = "付款单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinApPaymentOrderBo bo, HttpServletResponse response) {
        List<FinApPaymentOrderVo> list = finApPaymentOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "付款单", FinApPaymentOrderVo.class, response);
    }

    /**
     * 获取付款单详细信息
     *
     * @param paymentId 主键
     */
    @SaCheckPermission("erp:finApPaymentOrder:query")
    @GetMapping("/{paymentId}")
    public R<FinApPaymentOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long paymentId) {
        return R.ok(finApPaymentOrderService.queryById(paymentId));
    }

    /**
     * 新增付款单
     */
    @SaCheckPermission("erp:finApPaymentOrder:add")
    @Log(title = "付款单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<FinApPaymentOrderVo> add(@Validated(AddGroup.class) @RequestBody FinApPaymentOrderBo bo) {
        return R.ok(finApPaymentOrderService.insertByBo(bo));
    }

    /**
     * 修改付款单
     */
    @SaCheckPermission("erp:finApPaymentOrder:edit")
    @Log(title = "付款单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<FinApPaymentOrderVo> edit(@Validated(EditGroup.class) @RequestBody FinApPaymentOrderBo bo) {
        return R.ok(finApPaymentOrderService.updateByBo(bo));
    }

    /**
     * 删除付款单
     *
     * @param paymentIds 主键串
     */
    @SaCheckPermission("erp:finApPaymentOrder:remove")
    @Log(title = "付款单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paymentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] paymentIds) {
        return toAjax(finApPaymentOrderService.deleteWithValidByIds(List.of(paymentIds), true));
    }
}
