package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采购订单业务对象 erp_purchase_order
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PurchaseOrder.class, reverseConvertGenerate = false)
public class PurchaseOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空", groups = {EditGroup.class})
    private String orderCode;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 下单日期
     */
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    private PurchaseOrderStatus orderStatus;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicantName;

    /**
     * 采购员ID
     */
    private Long handlerId;

    /**
     * 采购员
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
