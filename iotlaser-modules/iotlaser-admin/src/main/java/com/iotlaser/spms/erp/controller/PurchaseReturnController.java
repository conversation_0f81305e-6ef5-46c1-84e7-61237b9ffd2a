package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购退货
 *
 * <AUTHOR> Kai
 * @date 2025-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseReturn")
public class PurchaseReturnController extends BaseController {

    private final IPurchaseReturnService purchaseReturnService;

    /**
     * 查询分页列表
     */
    @SaCheckPermission("erp:purchaseReturn:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseReturnVo> list(PurchaseReturnBo bo, PageQuery pageQuery) {
        return purchaseReturnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出列表
     */
    @SaCheckPermission("erp:purchaseReturn:export")
    @Log(title = "导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseReturnBo bo, HttpServletResponse response) {
        List<PurchaseReturnVo> list = purchaseReturnService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购退货", PurchaseReturnVo.class, response);
    }

    /**
     * 根据ID获取详细信息
     *
     * @param returnId 主键
     */
    @SaCheckPermission("erp:purchaseReturn:query")
    @GetMapping("/{returnId}")
    public R<PurchaseReturnVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long returnId) {
        return R.ok(purchaseReturnService.queryById(returnId));
    }

    /**
     * 新增采购退货单
     *
     * @param bo 包含新采购退货单所有信息的业务对象 (BO)
     * @return 创建成功后，返回包含新ID和完整信息的视图对象 (VO)
     */
    @SaCheckPermission("erp:purchaseReturn:add")
    @Log(title = "新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<PurchaseReturnVo> add(@Validated(AddGroup.class) @RequestBody PurchaseReturnBo bo) {
        return R.ok(purchaseReturnService.insertByBo(bo));
    }

    /**
     * 修改采购退货单
     *
     * @param bo 包含待更新信息的业务对象 (BO)，必须提供主键ID
     * @return 更新成功后，返回包含最新信息的视图对象 (VO)
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<PurchaseReturnVo> edit(@Validated(EditGroup.class) @RequestBody PurchaseReturnBo bo) {
        return R.ok(purchaseReturnService.updateByBo(bo));
    }

    /**
     * 删除采购退货单
     *
     * @param returnIds 待删除的采购退货单主键ID数组
     * @return 操作结果，成功时返回成功响应
     */
    @SaCheckPermission("erp:purchaseReturn:remove")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{returnIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] returnIds) {
        return toAjax(purchaseReturnService.deleteWithValidByIds(List.of(returnIds), true));
    }

    /**
     * 确认
     *
     * @param returnId 主键
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{returnId}")
    public R<Void> confirm(@NotNull(message = "主键不能为空") @PathVariable Long returnId) {
        return toAjax(purchaseReturnService.confirmReturn(returnId));
    }

    /**
     * 批量确认
     *
     * @param returnIds 主键集合
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "批量确认", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "主键不能为空") @RequestBody Long[] returnIds) {
        return toAjax(purchaseReturnService.batchConfirmReturns(List.of(returnIds)));
    }

    /**
     * 完成出库
     *
     * @param returnId 主键
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "完成出库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{returnId}")
    public R<Void> complete(@NotNull(message = "主键不能为空") @PathVariable Long returnId) {
        return toAjax(purchaseReturnService.completeReturn(returnId));
    }

    /**
     * 取消
     *
     * @param returnId 主键
     * @param reason   取消原因
     */
    @SaCheckPermission("erp:purchaseReturn:edit")
    @Log(title = "取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{returnId}")
    public R<Void> cancel(@NotNull(message = "主键不能为空") @PathVariable Long returnId,
                          @RequestParam(required = false) String reason) {
        return toAjax(purchaseReturnService.cancelReturn(returnId, reason));
    }
}
