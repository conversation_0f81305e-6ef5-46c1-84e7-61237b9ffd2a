package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinAccountLedgerBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountLedgerVo;
import com.iotlaser.spms.wms.enums.SourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 账户收支流水Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
public interface IFinAccountLedgerService {

    /**
     * 查询账户收支流水
     *
     * @param ledgerId 主键
     * @return 账户收支流水
     */
    FinAccountLedgerVo queryById(Long ledgerId);

    /**
     * 分页查询账户收支流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账户收支流水分页列表
     */
    TableDataInfo<FinAccountLedgerVo> queryPageList(FinAccountLedgerBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的账户收支流水列表
     *
     * @param bo 查询条件
     * @return 账户收支流水列表
     */
    List<FinAccountLedgerVo> queryList(FinAccountLedgerBo bo);

    /**
     * 新增账户收支流水
     *
     * @param bo 账户收支流水
     * @return 是否新增成功
     */
    Boolean insertByBo(FinAccountLedgerBo bo);

    /**
     * 修改账户收支流水
     *
     * @param bo 账户收支流水
     * @return 是否修改成功
     */
    Boolean updateByBo(FinAccountLedgerBo bo);

    /**
     * 校验并批量删除账户收支流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 从收款单生成收入流水
     *
     * @param receiptId 收款单ID
     * @param accountId 账户ID
     * @param remark    备注
     * @return 是否生成成功
     */
    Boolean generateIncomeFromReceiptOrder(Long receiptId, Long accountId, String remark);

    /**
     * 从付款单生成支出流水
     *
     * @param paymentId 付款单ID
     * @param accountId 账户ID
     * @param remark    备注
     * @return 是否生成成功
     */
    Boolean generateExpenseFromPaymentOrder(Long paymentId, Long accountId, String remark);

    /**
     * 查询来源单据的流水记录
     *
     * @param sourceId   源头单据ID
     * @param sourceType 源头单据类型
     * @return 流水记录列表
     */
    List<FinAccountLedgerVo> queryBySource(Long sourceId, SourceType sourceType);

    /**
     * 查询账户的流水记录
     *
     * @param accountId 账户ID
     * @return 流水记录列表
     */
    List<FinAccountLedgerVo> queryByAccountId(Long accountId);

    /**
     * 撤销流水记录
     *
     * @param ledgerId 流水ID
     * @param reason   撤销原因
     * @return 是否撤销成功
     */
    Boolean cancelLedger(Long ledgerId, String reason);
}
