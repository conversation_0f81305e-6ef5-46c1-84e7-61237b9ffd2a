package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售出库Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface SaleOutboundMapper extends BaseMapperPlus<SaleOutbound, SaleOutboundVo> {

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<SaleOutbound>().eq(SaleOutbound::getDirectSourceId, directSourceId));
    }

    default List<SaleOutbound> queryByDirectSourceId(Long directSourceId) {
        return selectList(new LambdaQueryWrapper<SaleOutbound>().eq(SaleOutbound::getDirectSourceId, directSourceId));
    }
}
