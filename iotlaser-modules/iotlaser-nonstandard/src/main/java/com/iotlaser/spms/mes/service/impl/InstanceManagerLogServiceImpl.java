package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.mes.domain.InstanceManagerLog;
import com.iotlaser.spms.mes.domain.bo.InstanceManagerLogBo;
import com.iotlaser.spms.mes.domain.vo.InstanceManagerLogVo;
import com.iotlaser.spms.mes.mapper.InstanceManagerLogMapper;
import com.iotlaser.spms.mes.service.IInstanceManagerLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 生产设备管理日志Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InstanceManagerLogServiceImpl implements IInstanceManagerLogService {

    private final InstanceManagerLogMapper baseMapper;

    /**
     * 查询生产设备管理日志
     *
     * @param logId 主键
     * @return 生产设备管理日志
     */
    @Override
    public InstanceManagerLogVo queryById(Long logId) {
        return baseMapper.selectVoById(logId);
    }

    /**
     * 分页查询生产设备管理日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产设备管理日志分页列表
     */
    @Override
    public TableDataInfo<InstanceManagerLogVo> queryPageList(InstanceManagerLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InstanceManagerLog> lqw = buildQueryWrapper(bo);
        Page<InstanceManagerLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产设备管理日志列表
     *
     * @param bo 查询条件
     * @return 生产设备管理日志列表
     */
    @Override
    public List<InstanceManagerLogVo> queryList(InstanceManagerLogBo bo) {
        LambdaQueryWrapper<InstanceManagerLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InstanceManagerLog> buildQueryWrapper(InstanceManagerLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InstanceManagerLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(InstanceManagerLog::getLogId);
        lqw.eq(bo.getManagerId() != null, InstanceManagerLog::getManagerId, bo.getManagerId());
        lqw.eq(bo.getOperatorId() != null, InstanceManagerLog::getOperatorId, bo.getOperatorId());
        lqw.like(StringUtils.isNotBlank(bo.getOperatorName()), InstanceManagerLog::getOperatorName, bo.getOperatorName());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatorStatus()), InstanceManagerLog::getOperatorStatus, bo.getOperatorStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatorRemark()), InstanceManagerLog::getOperatorRemark, bo.getOperatorRemark());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InstanceManagerLog::getStatus, bo.getStatus());
        lqw.between(params.get("beginOperatorTime") != null && params.get("endOperatorTime") != null,
            InstanceManagerLog::getOperatorTime, params.get("beginOperatorTime"), params.get("endOperatorTime"));
        return lqw;
    }

    /**
     * 新增生产设备管理日志
     *
     * @param bo 生产设备管理日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InstanceManagerLogBo bo) {
        InstanceManagerLog add = MapstructUtils.convert(bo, InstanceManagerLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLogId(add.getLogId());
        }
        return flag;
    }

    /**
     * 修改生产设备管理日志
     *
     * @param bo 生产设备管理日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InstanceManagerLogBo bo) {
        InstanceManagerLog update = MapstructUtils.convert(bo, InstanceManagerLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InstanceManagerLog entity) {

    }

    /**
     * 校验并批量删除生产设备管理日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 设备管理日志一般不允许删除，作为审计记录保留
            List<InstanceManagerLog> logs = baseMapper.selectByIds(ids);
            for (InstanceManagerLog mlog : logs) {
                log.info("删除设备管理日志校验：设备管理【{}】操作流程【{}】",
                    mlog.getManagerId(), mlog.getOperatorProcess());

                // 设备管理日志作为审计记录，一般不允许删除
                throw new ServiceException("设备管理日志作为审计记录，不允许删除");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
